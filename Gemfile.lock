GEM
  remote: https://rubygems.org/
  specs:
    CFPropertyList (3.0.2)
    addressable (2.7.0)
      public_suffix (>= 2.0.2, < 5.0)
    atomos (0.1.3)
    aws-eventstream (1.1.0)
    aws-partitions (1.388.0)
    aws-sdk-core (3.109.1)
      aws-eventstream (~> 1, >= 1.0.2)
      aws-partitions (~> 1, >= 1.239.0)
      aws-sigv4 (~> 1.1)
      jmespath (~> 1.0)
    aws-sdk-kms (1.39.0)
      aws-sdk-core (~> 3, >= 3.109.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-s3 (1.83.1)
      aws-sdk-core (~> 3, >= 3.109.0)
      aws-sdk-kms (~> 1)
      aws-sigv4 (~> 1.1)
    aws-sigv4 (1.2.2)
      aws-eventstream (~> 1, >= 1.0.2)
    babosa (1.0.4)
    claide (1.0.3)
    colored (1.2)
    colored2 (3.1.2)
    commander-fastlane (4.4.6)
      highline (~> 1.7.2)
    declarative (0.0.20)
    declarative-option (0.1.0)
    digest-crc (0.6.1)
      rake (~> 13.0)
    domain_name (0.5.20190701)
      unf (>= 0.0.5, < 1.0.0)
    dotenv (2.7.6)
    emoji_regex (3.2.1)
    excon (0.78.0)
    faraday (1.1.0)
      multipart-post (>= 1.2, < 3)
      ruby2_keywords
    faraday-cookie_jar (0.0.7)
      faraday (>= 0.8.0)
      http-cookie (~> 1.0.0)
    faraday_middleware (1.0.0)
      faraday (~> 1.0)
    fastimage (2.2.0)
    fastlane (2.166.0)
      CFPropertyList (>= 2.3, < 4.0.0)
      addressable (>= 2.3, < 3.0.0)
      aws-sdk-s3 (~> 1.0)
      babosa (>= 1.0.3, < 2.0.0)
      bundler (>= 1.12.0, < 3.0.0)
      colored
      commander-fastlane (>= 4.4.6, < 5.0.0)
      dotenv (>= 2.1.1, < 3.0.0)
      emoji_regex (>= 0.1, < 4.0)
      excon (>= 0.71.0, < 1.0.0)
      faraday (~> 1.0)
      faraday-cookie_jar (~> 0.0.6)
      faraday_middleware (~> 1.0)
      fastimage (>= 2.1.0, < 3.0.0)
      gh_inspector (>= 1.1.2, < 2.0.0)
      google-api-client (>= 0.37.0, < 0.39.0)
      google-cloud-storage (>= 1.15.0, < 2.0.0)
      highline (>= 1.7.2, < 2.0.0)
      json (< 3.0.0)
      jwt (>= 2.1.0, < 3)
      mini_magick (>= 4.9.4, < 5.0.0)
      multipart-post (~> 2.0.0)
      plist (>= 3.1.0, < 4.0.0)
      rubyzip (>= 2.0.0, < 3.0.0)
      security (= 0.1.3)
      simctl (~> 1.6.3)
      slack-notifier (>= 2.0.0, < 3.0.0)
      terminal-notifier (>= 2.0.0, < 3.0.0)
      terminal-table (>= 1.4.5, < 2.0.0)
      tty-screen (>= 0.6.3, < 1.0.0)
      tty-spinner (>= 0.8.0, < 1.0.0)
      word_wrap (~> 1.0.0)
      xcodeproj (>= 1.13.0, < 2.0.0)
      xcpretty (~> 0.3.0)
      xcpretty-travis-formatter (>= 0.0.3)
    fastlane-plugin-firebase_app_distribution (0.5.0)
    gh_inspector (1.1.3)
    google-api-client (0.38.0)
      addressable (~> 2.5, >= 2.5.1)
      googleauth (~> 0.9)
      httpclient (>= 2.8.1, < 3.0)
      mini_mime (~> 1.0)
      representable (~> 3.0)
      retriable (>= 2.0, < 4.0)
      signet (~> 0.12)
    google-cloud-core (1.5.0)
      google-cloud-env (~> 1.0)
      google-cloud-errors (~> 1.0)
    google-cloud-env (1.4.0)
      faraday (>= 0.17.3, < 2.0)
    google-cloud-errors (1.0.1)
    google-cloud-storage (1.29.1)
      addressable (~> 2.5)
      digest-crc (~> 0.4)
      google-api-client (~> 0.33)
      google-cloud-core (~> 1.2)
      googleauth (~> 0.9)
      mini_mime (~> 1.0)
    googleauth (0.14.0)
      faraday (>= 0.17.3, < 2.0)
      jwt (>= 1.4, < 3.0)
      memoist (~> 0.16)
      multi_json (~> 1.11)
      os (>= 0.9, < 2.0)
      signet (~> 0.14)
    highline (1.7.10)
    http-cookie (1.0.3)
      domain_name (~> 0.5)
    httpclient (2.8.3)
    jmespath (1.4.0)
    json (2.3.1)
    jwt (2.2.2)
    memoist (0.16.2)
    mini_magick (4.10.1)
    mini_mime (1.0.2)
    multi_json (1.15.0)
    multipart-post (2.0.0)
    nanaimo (0.3.0)
    naturally (2.2.0)
    os (1.1.1)
    plist (3.5.0)
    public_suffix (4.0.6)
    rake (13.0.1)
    representable (3.0.4)
      declarative (< 0.1.0)
      declarative-option (< 0.2.0)
      uber (< 0.2.0)
    retriable (3.1.2)
    rouge (2.0.7)
    ruby2_keywords (0.0.2)
    rubyzip (2.3.0)
    security (0.1.3)
    signet (0.14.0)
      addressable (~> 2.3)
      faraday (>= 0.17.3, < 2.0)
      jwt (>= 1.5, < 3.0)
      multi_json (~> 1.10)
    simctl (1.6.8)
      CFPropertyList
      naturally
    slack-notifier (2.3.2)
    terminal-notifier (2.0.0)
    terminal-table (1.8.0)
      unicode-display_width (~> 1.1, >= 1.1.1)
    tty-cursor (0.7.1)
    tty-screen (0.8.1)
    tty-spinner (0.9.3)
      tty-cursor (~> 0.7)
    uber (0.1.0)
    unf (0.1.4)
      unf_ext
    unf_ext (*******)
    unicode-display_width (1.7.0)
    word_wrap (1.0.0)
    xcodeproj (1.19.0)
      CFPropertyList (>= 2.3.3, < 4.0)
      atomos (~> 0.1.3)
      claide (>= 1.0.2, < 2.0)
      colored2 (~> 3.1)
      nanaimo (~> 0.3.0)
    xcpretty (0.3.0)
      rouge (~> 2.0.7)
    xcpretty-travis-formatter (1.0.0)
      xcpretty (~> 0.2, >= 0.0.7)

PLATFORMS
  ruby

DEPENDENCIES
  fastlane
  fastlane-plugin-firebase_app_distribution

BUNDLED WITH
   2.1.4
