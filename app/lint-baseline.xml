<?xml version="1.0" encoding="UTF-8"?>
<issues format="6" by="lint 7.1.0" type="baseline" client="gradle" dependencies="false" name="AGP (7.1.0)" variant="fatal" version="7.1.0">

    <issue
        id="NullSafeMutableLiveData"
        message="Expected non-nullable value"
        errorLine1="        _taker.postValue(taker)"
        errorLine2="                         ~~~~~">
        <location
            file="src/main/java/com/eatapp/clementine/ui/common/comments/ReservationCommentsViewModel.kt"
            line="49"
            column="26"/>
    </issue>

    <issue
        id="NullSafeMutableLiveData"
        message="Expected non-nullable value"
        errorLine1="        _comments.postValue(c)"
        errorLine2="                            ~">
        <location
            file="src/main/java/com/eatapp/clementine/ui/common/comments/ReservationCommentsViewModel.kt"
            line="69"
            column="29"/>
    </issue>

    <issue
        id="NullSafeMutableLiveData"
        message="Expected non-nullable value"
        errorLine1="        _reservation.value = reservation"
        errorLine2="                             ~~~~~~~~~~~">
        <location
            file="src/main/java/com/eatapp/clementine/ui/reservation/ReservationDetailsViewModel.kt"
            line="75"
            column="30"/>
    </issue>

    <issue
        id="NullSafeMutableLiveData"
        message="Expected non-nullable value"
        errorLine1="        _reservation.postValue(reservation)"
        errorLine2="                               ~~~~~~~~~~~">
        <location
            file="src/main/java/com/eatapp/clementine/ui/common/pos/ReservationPosViewModel.kt"
            line="32"
            column="32"/>
    </issue>

    <issue
        id="NullSafeMutableLiveData"
        message="Expected non-nullable value"
        errorLine1="        _posRecord.postValue(posRecord)"
        errorLine2="                             ~~~~~~~~~">
        <location
            file="src/main/java/com/eatapp/clementine/ui/common/pos/ReservationPosViewModel.kt"
            line="37"
            column="30"/>
    </issue>

    <issue
        id="NullSafeMutableLiveData"
        message="Expected non-nullable value"
        errorLine1="        _reservation.postValue(reservation)"
        errorLine2="                               ~~~~~~~~~~~">
        <location
            file="src/main/java/com/eatapp/clementine/ui/home/<USER>/ReviewViewModel.kt"
            line="18"
            column="32"/>
    </issue>

    <issue
        id="NullSafeMutableLiveData"
        message="Expected non-nullable value"
        errorLine1="        zone.value = z"
        errorLine2="                     ~">
        <location
            file="src/main/java/com/eatapp/clementine/ui/launch/SignUpViewModel.kt"
            line="58"
            column="22"/>
    </issue>

</issues>
