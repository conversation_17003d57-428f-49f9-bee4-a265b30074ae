<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission
        android:name="android.permission.BLUETOOTH"
        android:maxSdkVersion="30" />
    <uses-permission
        android:name="android.permission.BLUETOOTH_ADMIN"
        android:maxSdkVersion="30" />
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission android:name="android.permission.BLUETOOTH_SCAN" />
    <uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />

    <application
        android:name=".EatApplication"
        android:allowBackup="true"
        android:exported="true"
        android:hardwareAccelerated="true"
        android:icon="${appIcon}"
        android:label="@string/app_name"
        android:roundIcon="${appIconRound}"
        android:enableOnBackInvokedCallback="true"
        android:supportsRtl="true"
        android:theme="@style/AppTheme"
        tools:targetApi="tiramisu">

        <receiver
            android:name="com.adjust.sdk.AdjustReferrerReceiver"
            android:exported="true"
            android:permission="android.permission.INSTALL_PACKAGES">
            <intent-filter>
                <action android:name="com.android.vending.INSTALL_REFERRER" />
            </intent-filter>
        </receiver>

        <meta-data
            android:name="com.mixpanel.android.MPConfig.EnableDebugLogging"
            android:value="true" />

        <activity
            android:name=".ui.launch.SplashActivity"
            android:exported="true"
            android:theme="@style/SplashTheme">
            <intent-filter android:label="@string/app_name">
                <action android:name="android.intent.action.MAIN" />
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <activity
            android:name=".ui.launch.LaunchActivity"
            android:exported="true"
            android:label="@string/label_launch_activity"
            android:theme="@style/SplashTheme" />

        <activity
            android:name=".ui.home.HomeActivity"
            android:configChanges="keyboardHidden|orientation|screenSize"
            android:exported="true"
            android:label="@string/label_home_activity"
            android:launchMode="singleTask"
            android:windowSoftInputMode="adjustPan">

            <intent-filter>
                <data android:scheme="com.eatapp" />
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
            </intent-filter>

        </activity>
        <activity
            android:name=".ui.guest.GuestActivity"
            android:label="@string/label_guest_activity" />
        <activity
            android:name=".ui.common.selector.RestaurantsActivity"
            android:label="@string/label_restaurants_activity" />
        <activity
            android:name=".ui.home.more.guests.GuestsActivity"
            android:label="@string/label_guests_activity" />
        <activity
            android:name=".ui.reservation.ReservationActivity"
            android:label="@string/label_res_activity" />
        <activity
            android:name=".ui.common.voucher.RedeemVoucherActivity"
            android:label="@string/label_redeem_activity" />
        <activity
            android:name=".ui.common.hubspot.HubspotActivity"
            android:label="@string/label_hubspot_activity"
            android:windowSoftInputMode="adjustResize" />
        <activity
            android:name=".ui.common.printer.PrinterActivity"
            android:label="@string/label_print_settings" />
        <activity
            android:name=".ui.common.tables.TablesActivity"
            android:label="@string/label_select_table_activity" />
        <activity
            android:name=".ui.home.more.ReservationViewModeActivity"
            android:label="@string/reservation_mode_view" />
        <activity
            android:name=".ui.omnisearch.OmniSearchActivity"
            android:label="@string/search_label" />

        <service android:name="net.posprinter.service.PosprinterService" />

        <activity
            android:name="com.journeyapps.barcodescanner.CaptureActivity"
            android:screenOrientation="portrait"
            tools:replace="screenOrientation"
            tools:ignore="LockedOrientationActivity" />

        <service
            android:name=".internal.services.EatNotificationsService"
            android:exported="false">
            <intent-filter>
                <action android:name="com.google.firebase.MESSAGING_EVENT"/>
            </intent-filter>
        </service>

        <receiver
            android:name=".internal.services.NotificationActionReceiver"
            android:exported="false">
            <intent-filter>
                <action android:name="confirm" />
                <action android:name="decline" />
                <action android:name="waitlist" />
            </intent-filter>
        </receiver>

    </application>

</manifest>