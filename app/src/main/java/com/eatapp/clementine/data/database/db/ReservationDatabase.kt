package com.eatapp.clementine.data.database.db

import androidx.room.Database
import androidx.room.RoomDatabase
import androidx.room.TypeConverters
import com.eatapp.clementine.data.database.converters.DateTypeConverter
import com.eatapp.clementine.data.database.dao.ReservationDao
import com.eatapp.clementine.data.network.response.reservation.Reservation

@Database(entities = [Reservation::class], version = 5)
@TypeConverters(DateTypeConverter::class)
abstract class ReservationDatabase: RoomDatabase() {

    abstract fun reservationDao(): ReservationDao

}