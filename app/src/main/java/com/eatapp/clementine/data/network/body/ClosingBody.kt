package com.eatapp.clementine.data.network.body

import androidx.databinding.BaseObservable
import androidx.databinding.Bindable
import androidx.databinding.library.baseAdapters.BR
import com.google.gson.annotations.SerializedName
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

data class ClosingBody(
    @SerializedName("table_ids")
    var tableIds: MutableList<String>,
    @SerializedName("closing_types")
    var closingTypes: MutableList<String>,
    @SerializedName("time_range_begin")
    var timeRangeBegin: Date?,
    @SerializedName("time_range_end")
    var timeRangeEnd: Date?
) : BaseObservable() {

    constructor() : this(mutableListOf<String>(), mutableListOf<String>(), null, null)

    var closingStart: Date?
        get() {
            return timeRangeBegin
        }
        set(v) {
            timeRangeBegin = v
            notifyPropertyChanged(BR.startDate)
            notifyPropertyChanged(BR.startTime)
        }

    var closingEnd: Date?
        get() {
            return timeRangeEnd
        }
        set(v) {
            timeRangeEnd = v
            notifyPropertyChanged(BR.endDate)
            notifyPropertyChanged(BR.endTime)
        }

    val startDate: String
        @Bindable get() {
            return timeRangeBegin?.let { SimpleDateFormat("EEE, dd MMMM", Locale.US).format(it) } ?: ""
        }

    val startTime: String
        @Bindable get() {
            return timeRangeBegin?.let { SimpleDateFormat("hh:mm", Locale.US).format(it) } ?: ""
        }

    val endDate: String
        @Bindable get() {
            return timeRangeEnd?.let { SimpleDateFormat("EEE, dd MMMM", Locale.US).format(it) } ?: ""
        }

    val endTime: String
        @Bindable get() {
            return timeRangeEnd?.let { SimpleDateFormat("hh:mm", Locale.US).format(it) } ?: ""
        }

    fun addType(type:String) {
        closingTypes.add(type)
        notifyPropertyChanged(BR.startTime)
        notifyPropertyChanged(BR.endTime)
    }

    fun removeType(type:String) {
        closingTypes.remove(type)
        notifyPropertyChanged(BR.startTime)
        notifyPropertyChanged(BR.endTime)
    }
}