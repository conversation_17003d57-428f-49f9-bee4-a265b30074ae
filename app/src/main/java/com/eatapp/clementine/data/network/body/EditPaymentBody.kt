package com.eatapp.clementine.data.network.body


import com.google.gson.annotations.SerializedName

data class EditPaymentBody(
    @SerializedName("amount")
    val amount: Double,
    @SerializedName("auto_cancel_at")
    val autoCancelAt: String?,
    @SerializedName("charge_strategy")
    val chargeStrategy: String,
    @SerializedName("description")
    val description: String?,
    @SerializedName("notes")
    val notes: String?
)