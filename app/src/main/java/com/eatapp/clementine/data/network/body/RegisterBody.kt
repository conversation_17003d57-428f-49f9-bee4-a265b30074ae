package com.eatapp.clementine.data.network.body

import com.google.gson.annotations.SerializedName

data class RegisterBody(
    @SerializedName("email")
    var email: String?,
    @SerializedName("password")
    var password: String?,
    @SerializedName("name")
    var name: String?,
    @SerializedName("phone")
    var phone: String?,
    @SerializedName("time_zone_name")
    var zone: String?,
    @SerializedName("country_code")
    var code: String?,
    @SerializedName("captcha_token")
    var captchaToken: String
)