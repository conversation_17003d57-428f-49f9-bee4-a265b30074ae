package com.eatapp.clementine.data.network.body

import com.google.gson.annotations.SerializedName

data class RedeemVoucherAssignmentRequest(
    @SerializedName("redeem_voucher_assignment")
    val body: RedeemVoucherAssignmentBody
) {
    data class RedeemVoucherAssignmentBody(
        @SerializedName("id")
        val assignmentId: String,
        val code: String? = null,
        @SerializedName("reservation_id")
        val reservationId: String? = null,
        @SerializedName("edited_by_id")
        val editedBy: String?
    )
}

data class AssignVouchersRequest(
    @SerializedName("add_vouchers")
    val addVouchers: List<String>?,
    @SerializedName("remove_vouchers")
    val removeVouchers: List<String>? = null,
    @SerializedName("edited_by_id")
    val editedBy: String?
)