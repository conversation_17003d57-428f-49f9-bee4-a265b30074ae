package com.eatapp.clementine.data.network.response.apiresources

import com.google.gson.annotations.SerializedName

enum class NavigationItemType {
    @SerializedName("app")
    APP,
    @SerializedName("path")
    PATH
}

enum class NavigationItemNativeScreens(val path: String) {
    FLOOR("floor"),
    GUESTS("guests"),
    REPORTS("reports"),
    PRINT_SETTINGS("layout-settings#printer")
}

data class ApiResourceAttributes(
    val endpoints: ApiResourcesEndpoint,
    val navigation: List<NavigationItemCategory>,
    val sources: List<Source>,
    val countries: List<Country>
)