package com.eatapp.clementine.data.network.response.auth

import com.google.gson.annotations.SerializedName

data class Auth(
    @SerializedName("id")
    val id: String,
    @SerializedName("type")
    val type: String,
    @SerializedName("attributes")
    val attributes: AuthAttributes
) {
    val email: String
        get() {
            return attributes.email
        }

    val name: String
        get() {
            return attributes.name
        }

    val marketingAccepted: Boolean
        get() {
            return attributes.marketingAccepted
        }

    val role: String
        get() {
            return attributes.role
        }

    val termsAndConditionsAccepted: <PERSON>olean
        get() {
            return attributes.termsAndConditionsAccepted
        }

    val token: String?
        get() {
            return attributes.token
        }
}