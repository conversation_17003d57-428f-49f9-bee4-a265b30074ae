package com.eatapp.clementine.data.network.response.closing

import android.os.Parcelable
import androidx.databinding.BaseObservable
import androidx.databinding.Bindable
import com.google.gson.annotations.SerializedName
import kotlinx.parcelize.Parcelize
import java.util.Date

@Parcelize
data class Closing(
    @SerializedName("id")
    val id: String,
    @SerializedName("type")
    val type: String,
    @SerializedName("attributes")
    val attributes: ClosingAttributes,

    var longerRange: Boolean = false,
    var deleting: Boolean = false

): Parcelable, BaseObservable() {

    val closingType: String
        get() {
            return attributes.closingType
        }

    val closingTypes: List<String>
        get() {
            return attributes.closingTypes
        }

    val timeRangeBegin: Date
        get() {
            return attributes.timeRangeBegin
        }

    val timeRangeEnd: Date
        get() {
            return attributes.timeRangeEnd
        }

    val updatedAt: Date
        get() {
            return attributes.updatedAt
        }

    val tableIds: List<String>
        get() {
            return attributes.tableIds
        }

    val types: String
    @Bindable get() {
        return closingTypes.joinToString(", ") { it.replace("_", "-").capitalize() }
    }
}