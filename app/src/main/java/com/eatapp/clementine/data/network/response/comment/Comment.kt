package com.eatapp.clementine.data.network.response.comment

import android.os.Parcelable
import androidx.databinding.BaseObservable
import androidx.databinding.Bindable
import kotlinx.parcelize.Parcelize
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

@Parcelize
data class Comment(
    var comment: String,
    val name: String,
    val time: Date,
    var color: String?,
    var editable: Boolean,
    var isInEditMode: Boolean
) : Parcelable, BaseObservable() {

    val dateS: String?
        @Bindable get() {
            return SimpleDateFormat("hh:mm a, EEE dd MMMM", Locale.US).format(time)
        }
}