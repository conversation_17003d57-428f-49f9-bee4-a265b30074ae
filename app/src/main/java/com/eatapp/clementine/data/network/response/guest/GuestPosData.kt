package com.eatapp.clementine.data.network.response.guest

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Parcelize
data class GuestPosData(
    @SerializedName("total_spend")
    val totalSpend: Double,
    @SerializedName("visit_count")
    val posTicketsCount: Int,
    @SerializedName("average_spend_per_cover")
    val averageSpendPerCover: Double,
    @SerializedName("average_spend_per_visit")
    val averageSpendPerVisit: Double
) : Parcelable