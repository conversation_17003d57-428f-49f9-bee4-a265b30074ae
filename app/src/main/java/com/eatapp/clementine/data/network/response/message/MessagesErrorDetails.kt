package com.eatapp.clementine.data.network.response.message

import com.google.gson.annotations.SerializedName

enum class ActionType(val value: String) {
    ADDON("addon"),
    TEMPLATES("templates"),
    SUPPORT("support")
}

data class MessagesErrorDetails (
    @SerializedName("display_phrase")
    val displayPhrase: String?,
    @SerializedName("recommended_action")
    val recommendedAction: String?,
    @SerializedName("link")
    val link: String?,
    @SerializedName("whatsapp")
    val whatsapp: MessagingChannelError?,
    @SerializedName("sms")
    val sms: MessagingChannelError?,
    @SerializedName("email")
    val email: MessagingChannelError?
)

data class MessagingChannelError(
    @SerializedName("display")
    val display: String,
    @SerializedName("tooltip")
    val tooltip: String,
    @SerializedName("action_text")
    val actionText: String,
    @SerializedName("action_type")
    val type: String, 
    @SerializedName("addon_path")
    val addonPath: AddonPath?
) {
    val actionType: ActionType
        get() = ActionType.valueOf(type.uppercase())
}

data class AddonPath(
    @SerializedName("path")
    val path: String?,
    @SerializedName("type") 
    val type: String?,
    @SerializedName("iframe")
    val iframe: Boolean = false,
    @SerializedName("embedded")
    val embedded: String?,
    @SerializedName("addon_type")
    val addonType: String?,
    @SerializedName("restaurant_id")
    val restaurantId: String?
)
