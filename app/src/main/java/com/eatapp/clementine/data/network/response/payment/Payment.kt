package com.eatapp.clementine.data.network.response.payment


import android.os.Parcelable
import com.eatapp.clementine.R
import com.eatapp.clementine.data.network.response.Data
import com.eatapp.clementine.data.network.response.DataSingle
import com.google.gson.annotations.SerializedName
import kotlinx.parcelize.Parcelize

@Parcelize
data class Payment(

    @SerializedName("attributes")
    val attributes: PaymentAttributes,
    @SerializedName("id")
    val id: String,
    @SerializedName("type")
    val type: String,
    @SerializedName("relationships")
    var relationships: PaymentRelationships?,
    var amountUI: Double? = null
) : Parcelable {

    val status: String
        get() {
            return attributes.status
        }

    val paymentRule: Data?
        get() {
            return relationships?.paymentRule?.data
        }
}

@Parcelize
data class PaymentRelationships(
    @SerializedName("payment_rule")
    val paymentRule: DataSingle?
) : Parcelable

enum class PaymentType(val readableValue: String, val value: String) {
    LUMP("Lump", "lump"),
    PER_COVER("Per cover", "per_cover"),
    PAYMENT_PACKAGE("Payment package", "payment_package");

    companion object {
        fun fromReadableValue(value: String): PaymentType? {
            return entries.find { it.readableValue == value }
        }
    }
}

enum class PaymentChargeType(val readableValue: String, val value: String) {
    SMART("Smart (recommended)", value = "smart"),
    AUTHORIZATION("Authorization", value = "authorization"),
    PRE_PAYMENT("Pre-payment", value = "pre_payment");

    companion object {
        fun fromReadableValue(value: String): PaymentChargeType? {
            return PaymentChargeType.entries.find { it.readableValue == value }
        }
    }
}

enum class PaymentStatus(private val iconId: Int) {
    REQUESTED(R.drawable.ic_payment_status_requested),
    AUTHORIZED(R.drawable.ic_payment_status_authorised),
    CAPTURED(R.drawable.ic_payment_status_captured),
    VOIDED(R.drawable.ic_payment_status_voided),
    REFUNDED(R.drawable.ic_payment_status_refunded),
    PARTIALLY_REFUNDED(R.drawable.ic_payment_status_partially_refunded),
    CANCELED(R.drawable.ic_icon_status_canceled),
    PAID(R.drawable.ic_payment_status_captured),
    UNPAID(R.drawable.ic_icon_status_canceled),
    PARTIALLY_PAID(R.drawable.ic_payment_status_partially_paid);

    companion object {
        fun icon(status: String): Int {
            return try {
                valueOf(status.uppercase()).iconId
            } catch (e: Exception) {
                R.drawable.ic_icon_status_canceled
            }
        }
    }

    override fun toString(): String {
        return name.lowercase()
    }

    fun readableStatus(): String {
        return when (this) {
            PARTIALLY_REFUNDED -> "Partially refunded"
            PARTIALLY_PAID -> "Partially paid"
            else -> this.toString().replaceFirstChar { c: Char -> c.titlecase() }
        }
    }
}