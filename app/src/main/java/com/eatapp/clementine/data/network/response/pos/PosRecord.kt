package com.eatapp.clementine.data.network.response.pos

import android.os.Parcelable
import androidx.databinding.BaseObservable
import androidx.databinding.Bindable
import com.eatapp.clementine.data.network.response.reservation.Reservation
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

@Parcelize
data class PosRecord(
    @SerializedName("id")
    val id: String,
    @SerializedName("type")
    val type: String,
    @SerializedName("attributes")
    val attributes: PosRecordAttributes,

    var reservation: Reservation?

) : Parcelable, BaseObservable() {

    val createdAt: Date?
        get() {
            return attributes.createdAt
        }

    val updatedAt: Date?
        get() {
            return attributes.updatedAt
        }

    val manualMatch: Boolean
        get() {
            return attributes.manualMatch
        }

    val posServiceId: String?
        get() {
            return attributes.posServiceId
        }

    val reservationId: String?
        get() {
            return attributes.reservationId
        }

    val commercialDiscountTotal: Double?
        get() {
            return attributes.data.commercial_dicount_total
        }

    val commercialGuestCount: Int?
        get() {
            return attributes.data.commercial_guest_count
        }

    val commercialInclusiveTaxTotal: Double?
        get() {
            return attributes.data.commercial_inclusive_tax_total
        }

    val commercialOtherChargesTotal: Double?
        get() {
            return attributes.data.commercial_other_charges_total
        }

    val commercialPaymentComments: String?
        get() {
            return attributes.data.commercial_payment_comments
        }

    val commercialPaymentType: String?
        get() {
            return attributes.data.commercial_payment_type
        }

    val commercialRevenueCenterId: String?
        get() {
            return attributes.data.commercial_revenue_center_id
        }

    val commercialRevenueCenterName: String?
        get() {
            return attributes.data.commercial_revenue_center_name
        }

    val commercialRevenueCenterPosId: String?
        get() {
            return attributes.data.commercial_revenue_center_pos_id
        }

    val commercialServiceChargesTotal: Double?
        get() {
        return attributes.data.commercial_service_charges_total
    }

    val commercialSubTotal: Double?
        get() {
            return attributes.data.commercial_sub_total
        }

    val commercialTaxTotal: Double?
        get() {
            return attributes.data.commercial_tax_total
        }

    val commercialTips: Double?
        get() {
            return attributes.data.commercial_tips
        }

    val commercialTotal: Double?
        get() {
            return attributes.data.commercial_total
        }

    val discounts: List<Discount>
        get() {
            return attributes.data.discounts
        }

    val employeeCheckName: String?
        get() {
            return attributes.data.employee_check_name
        }

    val employeeFirstName: String?
        get() {
            return attributes.data.employee_first_name
        }

    val employeeId: String?
        get() {
            return attributes.data.employee_id
        }

    val employeeLastName: String?
        get() {
        return attributes.data.employee_last_name
    }

    val serverName: String?
        @Bindable get() {
            return String.format("%s %s", employeeFirstName ?: "", employeeLastName ?: "") }


    val employeePosId: String?
        get() {
            return attributes.data.employee_pos_id
        }

    val menuItems: List<MenuItem>
        get() {
            return attributes.data.menu_items
        }

    val orderTypeId: String?
        get() {
            return attributes.data.order_type_id
        }

    val orderTypeName: String?
        get() {
            return attributes.data.order_type_name
        }

    val orderTypePosId: String?
        get() {
        return attributes.data.order_type_pos_id
    }

    val payments: List<Payment>
        get() {
            return attributes.data.payments
        }

    val serviceCharges: List<MenuItem>
        get() {
            return attributes.data.service_charges
        }

    val tableId: String?
        get() {
            return attributes.data.table_id
        }

    val tableName: String?
        get() {
            return attributes.data.table_name
        }

    val tableNumber: String?
        get() {
            return attributes.data.table_number
        }

    val tablePosId: String?
        get() {
            return attributes.data.table_pos_id
        }

    val ticketClosedAt: Date?
        get() {
            return attributes.data.ticket_closed_at
        }

    val ticketClosedAtS: String?
        @Bindable get() { return if (attributes.data.ticket_closed_at != null)
            SimpleDateFormat("hh:mm a · EEE, MMMM dd, yyyy", Locale.US).format(attributes.data.ticket_closed_at) else "---" }

    val ticketId: String?
        get() {
            return attributes.data.ticket_id
        }

    val ticketName: String?
        get() {
            return attributes.data.ticket_name
        }

    val ticketNumber: String?
        get() {
            return attributes.data.ticket_number
        }

    val ticketOpen: Boolean
        get() {
            return attributes.data.ticket_open
        }

    val ticketOpenedAt: Date?
        get() {
            return attributes.data.ticket_opened_at
        }

    val ticketOpenedAtS: String?
        @Bindable get() { return if (attributes.data.ticket_opened_at != null)
            SimpleDateFormat("hh:mm a · EEE, MMMM dd, yyyy", Locale.US).format(attributes.data.ticket_opened_at) else "---" }

    val ticketVoid: Boolean
        get() {
            return attributes.data.ticket_void
        }

    val voidedItems: List<MenuItem>
        get() {
            return attributes.data.voided_items
        }
}
