package com.eatapp.clementine.data.network.response.pos

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize
import java.util.Date

@Parcelize
data class PosRecordAttributes(
    @SerializedName("id")
    val createdAt: Date,
    @SerializedName("manual_match")
    val manualMatch: <PERSON><PERSON><PERSON>,
    @SerializedName("pos_service_id")
    val posServiceId: String,
    @SerializedName("reservation_id")
    val reservationId: String,
    @SerializedName("updated_at")
    val updatedAt: Date,
    @SerializedName("data")
    val data: PosRecordSubAttributes
) : Parcelable