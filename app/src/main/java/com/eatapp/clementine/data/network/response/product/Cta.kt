package com.eatapp.clementine.data.network.response.product

import com.google.gson.annotations.SerializedName

data class Cta(
    val action: CtaType,
    val text: String,
    val type: String,
    val context: CtaContext
) {
    val url: String
        get() {
            return context.url
        }
}

enum class CtaType(val type: String) {
    @SerializedName("api")
    Api("api"),
    @SerializedName("iframe")
    Iframe("iframe"),
    @SerializedName("link")
    Link("link")
}

data class CtaContext(
    val url: String
)