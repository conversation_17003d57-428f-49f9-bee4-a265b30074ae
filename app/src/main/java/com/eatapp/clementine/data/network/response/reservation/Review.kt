package com.eatapp.clementine.data.network.response.reservation

import android.os.Parcelable
import androidx.databinding.BaseObservable
import androidx.databinding.Bindable
import com.eatapp.clementine.data.network.response.EatMap
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize
import java.util.Date

@Parcelize
data class Review(
    @SerializedName("captured_at")
    val capturedAt: Date?,
    @SerializedName("comment")
    val comment: String?,
    @SerializedName("more")
    val more: List<EatMap>?,
    @SerializedName("rating")
    val rating: Double?,

    var isExpanded : Boolean = false

) : Parcelable, BaseObservable() {

    val food: String
        @Bindable get() {
            return more?.find { it.key == "food" }?.value?.toDouble()?.toString() ?: "--"
        }

    val service: String
        @Bindable get() {
            return more?.find { it.key == "service" }?.value?.toDouble()?.toString() ?: "--"
        }

    val ambience: String
        @Bindable get() {
            return more?.find { it.key == "ambience" }?.value?.toDouble()?.toString() ?: "--"
        }

}