package com.eatapp.clementine.data.network.response.reservation

import android.os.Parcelable
import androidx.databinding.BaseObservable
import androidx.databinding.Bindable
import com.eatapp.clementine.internal.Status
import com.eatapp.clementine.internal.eatDateISO8601Timezone1DateFormatter
import com.eatapp.clementine.internal.eatDateISO8601TimezoneDateFormatter
import kotlinx.parcelize.Parcelize
import java.text.SimpleDateFormat
import java.util.*

@Parcelize
data class StatusHistory(
    val duration: Int = 0,
    val status: String,
    val timestamp: String?,
    var last: Boolean = false
) : Parcelable, BaseObservable() {

    val statusTitle: String
        @Bindable get() { return Status.getTitle(status) }

    val statusIcon: Int
        @Bindable get() { return Status.getIcon(status) }

    val statusColor: Int
        @Bindable get() { return Status.getColor(status) }

    val date: String?
        @Bindable get() {
            return if (timestamp != null) {
                try {
                    eatDateISO8601TimezoneDateFormatter().parse(timestamp)
                        ?.let { SimpleDateFormat("EEE, MMM dd, hh:mm a", Locale.US).format(it) }
                } catch (e: Exception) {
                    eatDateISO8601Timezone1DateFormatter().parse(timestamp)
                        ?.let { SimpleDateFormat("EEE, MMM dd, hh:mm a", Locale.US).format(it) }
                }
            } else "--"
        }
}