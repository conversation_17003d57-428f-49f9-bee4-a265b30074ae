package com.eatapp.clementine.data.network.response.room


import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize
import java.util.Date

@Parcelize
data class Table(
    @SerializedName("id")
    val id: String,
    @SerializedName("attributes")
    val attributes: TableAttributes

) : Parcelable {

    val maxCovers: Int
        get() {
            return attributes.maxCovers
        }

    val minCovers: Int
        get() {
            return attributes.minCovers
        }

    val number: String
        get() {
            return attributes.number
        }

    val type: TableType
        get() {
            return attributes.type
        }

    val color: String?
        get() {
            return attributes.color
        }

    val posServiceTableId: String?
        get() {
            return attributes.posServiceTableId
        }

    val restaurantServerId: String?
        get() {
            return attributes.restaurantServerId
        }

    val roomId: String
        get() {
            return attributes.roomId
        }

    val rotation: Int
        get() {
            return attributes.rotation
        }

    val shape: String
        get() {
            return attributes.shape
        }

    val size: String
        get() {
            return attributes.size
        }

    val soldOnline: Boolean
        get() {
            return attributes.soldOnline
        }

    val updatedAt: Date
        get() {
            return attributes.updatedAt
        }

    val x: Double
        get() {
            return attributes.x
        }

    val y: Double
        get() {
            return attributes.y
        }

    val width: Float
        get() {
            return size.split("x").first().toFloat()
        }

    val height: Float
        get() {
            return size.split("x").last().toFloat()
        }
}