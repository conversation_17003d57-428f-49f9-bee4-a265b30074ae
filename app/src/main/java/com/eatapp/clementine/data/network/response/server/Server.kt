package com.eatapp.clementine.data.network.response.server

import androidx.databinding.BaseObservable
import androidx.databinding.Bindable
import com.eatapp.clementine.BR
import com.eatapp.clementine.data.network.response.reservation.Reservation
import com.eatapp.clementine.data.network.response.room.RoomRelationships
import com.eatapp.clementine.internal.Status
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.IgnoredOnParcel
import java.text.SimpleDateFormat
import java.util.Locale

data class Server (
    @SerializedName("id")
    val id: String,
    @SerializedName("type")
    val type: String,
    @SerializedName("relationships")
    val relationships: RoomRelationships,
    @SerializedName("attributes")
    val attributes: ServerAttributes,
    @IgnoredOnParcel
    var res: MutableList<Reservation>? = mutableListOf()

): BaseObservable() {

    val color: String
        get() { return attributes.color }

    val name: String
        get() { return attributes.name }

    val nowServed: String
        @Bindable get() { return reservations?.filter { res -> Status.isSeated(res.status) }
            ?.sumOf { res -> res.covers }
            ?.toString() ?: "0"
        }

    val totalServed: String
        @Bindable get() { return reservations?.sumOf { res -> res.covers }
            ?.toString() ?: "0"
        }

    val lastSeated: String
        @Bindable get() {
            val formatter = SimpleDateFormat("hh:mm a", Locale.US)

            val res: Reservation? = reservations?.let {
                it.filter { res -> Status.isSeated(res.status) || Status.isFinished(res.status) }
                    .maxByOrNull { r ->   r.startTime } }

            res?.let {
                return formatter.format(it.startTime)
            } ?: return "N/A"
        }

    var reservations: MutableList<Reservation>?
        get() { return res }
        set(v) {
            res = v
            notifyPropertyChanged(BR.nowServed)
            notifyPropertyChanged(BR.totalServed)
            notifyPropertyChanged(BR.lastSeated)
        }
}