package com.eatapp.clementine.data.network.response.shift

import com.eatapp.clementine.data.network.response.reservation.Reservation
import com.google.gson.Gson
import com.google.gson.annotations.SerializedName
import java.util.UUID

data class Shift(
    @SerializedName("id")
    val id: String,
    @SerializedName("type")
    val type: String,
    @SerializedName("attributes")
    val attributes: ShiftAttributes,

    var reservations: MutableList<Reservation>? = mutableListOf(),
    val fakeId: String
    ) {

    constructor() : this("empty", "", ShiftAttributes("#b5b5b5"), mutableListOf(), "")

    constructor(name: String, id: String, firstSeating: Int, color: String, fakeId: String) : this(id, "", ShiftAttributes(name, firstSeating, color), fakeId = fakeId)

    val status: String
        get() {
            return attributes.status
        }

    val name: String
        get() {
            return attributes.name
        }

    val color: String
        get() {
            return attributes.color
        }

    val availabilityType: String
        get() {
            return attributes.availability_type
        }

    var firstSeating: Int
        get() { return attributes.first_seating }
        set(value) { attributes.first_seating = value}

    var lastSeating: Int
        get() { return attributes.last_seating }
        set(value) { attributes.last_seating = value}

    val shiftType: String
        get() {
            return attributes.shift_type
        }

    val daysOfWeek: List<String>
        get() {
            return attributes.days_of_week
        }

    val startDate: String?
        get() {
            return attributes.start_date
        }

    val endDate: String?
        get() {
            return attributes.end_date
        }

    fun clone(): Shift {
        val stringProject = Gson().toJson(this, Shift::class.java)
        return Gson().fromJson(stringProject, Shift::class.java)
    }
}