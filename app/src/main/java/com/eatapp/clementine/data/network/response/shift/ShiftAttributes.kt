package com.eatapp.clementine.data.network.response.shift

data class ShiftAttributes(
    val availability_type: String,
    var color: String,
    val covers: Int,
    val days_of_week: List<String>,
    val end_date: String?,
    var first_seating: Int,
    val interval: Int,
    var last_seating: Int,
    val max_covers_per_reservation: Int,
    val min_covers_per_reservation: Int,
    val name: String,
    val notes: Any,
    val notice_period: Int,
    val pacing: Int,
    val same_pacing: Any,
    val shift_type: String,
    val start_date: String?,
    val status: String,
    val updated_at: String,
    val use_pacing: Boolean
) {
    constructor(name: String, firstSeating: Int, color: String) : this("", color, 0, listOf(), null,
        firstSeating, 0, 0, 0 , 0,
        name, "", 0,0,0, "", "", "",
        "", false)

    constructor(color: String) : this("", color, 0, listOf(), null,
        0, 0, 0, 0 , 0,
    "", "", 0,0,0, "", "", "",
    "", false)
}