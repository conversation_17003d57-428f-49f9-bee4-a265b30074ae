package com.eatapp.clementine.data.network.response.tag

import com.eatapp.clementine.Flatten
import com.eatapp.clementine.internal.SelectorItem
import com.google.gson.annotations.SerializedName

data class Category(
    @SerializedName("id")
    val id: String,
    @SerializedName("type")
    val type: String,
    @Flatten("attributes::color")
    val color: String?,
    @Flatten("attributes::description")
    val description: String?,
    @Flatten("attributes::name")
    val name: String?,

    var advanceTags: List<SelectorItem> = mutableListOf()

) {
    constructor(name: String?, color: String?) : this("",  "", color, "", name, mutableListOf())
}