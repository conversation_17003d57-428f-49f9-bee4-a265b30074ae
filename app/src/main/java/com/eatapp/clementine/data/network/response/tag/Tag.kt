package com.eatapp.clementine.data.network.response.tag

import com.eatapp.clementine.Flatten
import com.google.gson.annotations.SerializedName

data class Tag(
    @SerializedName("id")
    val id: String,
    @SerializedName("type")
    val type: String,
    @Flatten("attributes::context")
    val context: List<String>,
    @Flatten("attributes::description")
    val description: String?,
    @Flatten("attributes::icon")
    val icon: String?,
    @Flatten("attributes::importance")
    val importance: Int,
    @Flatten("attributes::name")
    val name: String,
    @Flatten("attributes::updated_at")
    val updatedAt: String?,
    @SerializedName("relationships")
    val relationships: TagRelationships?,

    var category: Category?
)