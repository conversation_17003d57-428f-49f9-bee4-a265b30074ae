package com.eatapp.clementine.data.network.response.user


import com.eatapp.clementine.Flatten
import com.google.gson.annotations.SerializedName

data class User(
    @SerializedName("id")
    val id: String,
    @SerializedName("type")
    val type: String,
    @Flatten("attributes::color")
    val color: String,
    @Flatten("attributes::email")
    val email: String,
    @Flatten("attributes::last_sign_in_at")
    val lastSignInAt: String,
    @Flatten("attributes::login")
    val login: <PERSON><PERSON><PERSON>,
    @Flatten("attributes::merge_guests")
    val mergeGuests: <PERSON><PERSON><PERSON>,
    @Flatten("attributes::name")
    val name: String?,
    @Flatten("attributes::permissions")
    val permissions: List<Permission>,
    @Flatten("attributes::pin_code")
    val pinCode: String?,
    @Flatten("attributes::role")
    val role: String,
    @Flatten("attributes::server")
    val server: <PERSON><PERSON><PERSON>,
    @Flatten("attributes::show_shared_guests")
    val showSharedGuests: <PERSON><PERSON><PERSON>,
    @Flatten("attributes::taker")
    val taker: <PERSON><PERSON><PERSON>,
    @Flatten("attributes::refund")
    val allowRefund: <PERSON><PERSON><PERSON>,
    @Flatten("attributes::refund_pin_required")
    val refundPinRequired: Boolean,
    @Flatten("attributes::taker_pin_required")
    val takerPinRequired: Boolean
)