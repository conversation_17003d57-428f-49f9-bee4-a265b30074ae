package com.eatapp.clementine.data.network.response.vouchers

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.parcelize.Parcelize
import java.util.Date

interface VoucherBaseClass {
    val id: String
}

data class VouchersResponse(
    @SerializedName("data")
    val vouchers: List<Voucher>
)

data class Voucher(
    @SerializedName("attributes")
    val attributes: Attributes,
    @SerializedName("id")
    override val id: String
) : VoucherBaseClass {

    var selected = false

    data class Attributes(
        @SerializedName("code")
        val code: String?,
        @SerializedName("context")
        val context: List<VoucherType>?,
        @SerializedName("created_at")
        val createdAt: Date?,
        @SerializedName("description")
        val description: String?,
        @SerializedName("expires_at")
        val expiresAt: Date?,
        @SerializedName("key")
        val key: String?,
        @SerializedName("name")
        val name: String?,
        @SerializedName("status")
        val status: String?,
        @SerializedName("updated_at")
        val updatedAt: Date?,
        @SerializedName("volume")
        val volume: Int?,
    )
}

@Parcelize
data class VoucherAssignment(
    @SerializedName("id")
    override val id: String,
    @SerializedName("attributes")
    val attributes: VoucherAssignmentAttributes,
) : VoucherBaseClass, Parcelable {

    val redeemed: Boolean
        get() {
            return attributes.redeemedAt != null
        }

    val codeRequired: Boolean
        get() {
            return !attributes.code.isNullOrEmpty()
        }

    constructor(voucher: Voucher): this(voucher.id,
        VoucherAssignmentAttributes(
            voucher.attributes.name,
            voucher.id,
            voucher.attributes.volume,
            voucher.attributes.expiresAt,
            voucher.attributes.context,
            voucher.attributes.status,
            null,
            voucher.attributes.description,
            voucher.attributes.code,
            null,
            voucher.attributes.createdAt,
            voucher.attributes.updatedAt
        )
    )
}

@Parcelize
data class VoucherAssignmentAttributes(
    @SerializedName("name")
    val name: String?,
    @SerializedName("voucher_id")
    val voucherId: String?,
    @SerializedName("volume")
    val volume: Int?,
    @SerializedName("expires_at")
    val expiresAt: Date?,
    @SerializedName("context")
    val context: List<VoucherType>?,
    @SerializedName("status")
    val status: String?,
    @SerializedName("reservation_id")
    val reservationId: String?,
    @SerializedName("description")
    val description: String?,
    @SerializedName("code")
    val code: String?,
    @SerializedName("redeemed_at")
    val redeemedAt: Date?,
    @SerializedName("created_at")
    val createdAt: Date?,
    @SerializedName("updated_at")
    val updatedAt: Date?
) : Parcelable

enum class VoucherType {
    @SerializedName("guest")
    GUEST,

    @SerializedName("reservation")
    RESERVATION
}