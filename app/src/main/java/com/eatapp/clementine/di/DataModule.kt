package com.eatapp.clementine.data.repository

import com.eatapp.clementine.GuestVouchersHandler
import com.eatapp.clementine.GuestVouchersHandlerImpl
import com.eatapp.clementine.ReservationVouchersHandler
import com.eatapp.clementine.ReservationVouchersHandlerImpl
import com.eatapp.clementine.VouchersHandler
import dagger.Binds
import dagger.Module
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
abstract class DataModule {
    @Binds
    @Singleton
    abstract fun provideDayNoteRepository(dayNoteRepositoryImpl: DayNoteRepositoryImpl): DayNoteRepository

    @Binds
    @Singleton
    abstract fun provideClosingsRepository(closingsRepositoryImpl: ClosingsRepositoryImpl): ClosingsRepository

    @Binds
    @Singleton
    abstract fun provideFabricationRepository(fabricateRepositoryImpl: FabricateRepositoryImpl): FabricateRepository

    @Binds
    @Singleton
    abstract fun provideGuestsRepository(guestsRepositoryImpl: GuestsRepositoryImpl): GuestsRepository

    @Binds
    @Singleton
    abstract fun providePosRepository(posRepositoryImpl: PosRepositoryImpl): PosRepository

    @Binds
    @Singleton
    abstract fun provideHubspotRepository(hubspotRepositoryImpl: HubspotRepositoryImpl): HubspotRepository

    @Binds
    @Singleton
    abstract fun provideReservationsRepository(reservationsRepositoryImpl: ReservationsRepositoryImpl): ReservationsRepository

    @Binds
    @Singleton
    abstract fun provideReportsRepository(reportsRepositoryImpl: ReportsRepositoryImpl): ReportsRepository

    @Binds
    @Singleton
    abstract fun provideApiResourcesRepository(apiResourcesRepositoryImpl: ApiResourcesRepositoryImpl): ApiResourcesRepository

    @Binds
    @Singleton
    abstract fun provideServersRepository(serversRepositoryImpl: ServersRepositoryImpl): ServersRepository

    @Binds
    @Singleton
    abstract fun providePaymentsRepository(paymentsRepositoryImpl: PaymentsRepositoryImpl): PaymentsRepository

    @Binds
    @Singleton
    abstract fun provideOmniSearchRepository(omniSearchRepositoryImpl: OmniSearchRepositoryImpl): OmniSearchRepository

    @Binds
    @Singleton
    abstract fun provideMessagingRepository(messagingRepositoryImpl: MessagingRepositoryImpl): MessagingRepository

    @Binds
    @Singleton
    @GuestVouchersHandler
    abstract fun provideGuestVouchersHandler(guestVouchersHandlerImpl: GuestVouchersHandlerImpl): VouchersHandler

    @Binds
    @Singleton
    @ReservationVouchersHandler
    abstract fun provideReservationVouchersHandler(reservationVouchersHandlerImpl: ReservationVouchersHandlerImpl): VouchersHandler
}