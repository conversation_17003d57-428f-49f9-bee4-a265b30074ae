package com.eatapp.clementine.di

import android.content.Context
import com.eatapp.clementine.BuildConfig
import com.eatapp.clementine.FlattenTypeAdapterFactory
import com.eatapp.clementine.data.network.EatApiCanaryWsService
import com.eatapp.clementine.data.network.EatApiHubspotService
import com.eatapp.clementine.data.network.EatApiHydraWsService
import com.eatapp.clementine.data.network.EatApiMessaging
import com.eatapp.clementine.data.network.EatApiPaymentsService
import com.eatapp.clementine.data.network.EatApiPosService
import com.eatapp.clementine.data.network.EatApiRestaurant
import com.eatapp.clementine.data.network.deserializers.GuestResponseDeserializer
import com.eatapp.clementine.data.network.deserializers.GuestsResponseDeserializer
import com.eatapp.clementine.data.network.deserializers.NavigationCategoryDeserializer
import com.eatapp.clementine.data.network.deserializers.ReservationResponseDeserializer
import com.eatapp.clementine.data.network.deserializers.ReservationsResponseDeserializer
import com.eatapp.clementine.data.network.deserializers.RestaurantResponseDeserializer
import com.eatapp.clementine.data.network.interceptor.EatInterceptor
import com.eatapp.clementine.data.network.interceptor.EatInterceptorImpl
import com.eatapp.clementine.data.network.response.apiresources.NavigationItemCategory
import com.eatapp.clementine.data.network.response.guest.GuestResponse
import com.eatapp.clementine.data.network.response.guest.GuestsResponse
import com.eatapp.clementine.data.network.response.reservation.ReservationResponse
import com.eatapp.clementine.data.network.response.reservation.ReservationsResponse
import com.eatapp.clementine.data.network.response.restaurant.RestaurantResponse
import com.eatapp.clementine.data.repository.ReservationsRepository
import com.eatapp.clementine.internal.DateTypeAdapter
import com.eatapp.clementine.internal.Endpoints
import com.eatapp.clementine.internal.managers.EatManager
import com.google.gson.Gson
import com.google.gson.GsonBuilder
import com.jakewharton.retrofit2.adapter.kotlin.coroutines.CoroutineCallAdapterFactory
import dagger.Module
import dagger.Provides
import dagger.hilt.EntryPoint
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import okhttp3.Cache
import okhttp3.OkHttpClient
import okhttp3.logging.HttpLoggingInterceptor
import retrofit2.Retrofit
import retrofit2.converter.gson.GsonConverterFactory
import java.io.File
import java.util.*
import java.util.concurrent.TimeUnit
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
class NetworkModule {

    @Singleton
    @Provides
    fun provideInterceptor(eatInterceptorImpl: EatInterceptorImpl): EatInterceptor {
        return eatInterceptorImpl
    }

    @Singleton
    @Provides
    fun provideDateSerializer(): DateTypeAdapter {
        return DateTypeAdapter()
    }

    @Singleton
    @Provides
    fun provideGuestsDeserializer(eatManager: EatManager, dateTypeAdapter: DateTypeAdapter): GuestsResponseDeserializer {
        return GuestsResponseDeserializer(eatManager, dateTypeAdapter)
    }

    @Singleton
    @Provides
    fun provideGuestDeserializer(eatManager: EatManager, dateTypeAdapter: DateTypeAdapter): GuestResponseDeserializer {
        return GuestResponseDeserializer(eatManager, dateTypeAdapter)
    }

    @Singleton
    @Provides
    fun provideReservationsDeserializer(eatManager: EatManager, dateTypeAdapter: DateTypeAdapter): ReservationsResponseDeserializer {
        return ReservationsResponseDeserializer(eatManager, dateTypeAdapter)
    }

    @Singleton
    @Provides
    fun provideReservationDeserializer(eatManager: EatManager, dateTypeAdapter: DateTypeAdapter): ReservationResponseDeserializer {
        return ReservationResponseDeserializer(eatManager, dateTypeAdapter)
    }

    @Singleton
    @Provides
    fun provideRestaurantDeserializer(): RestaurantResponseDeserializer {
        return RestaurantResponseDeserializer()
    }

    @Singleton
    @Provides
    fun provideNavigationItemDeserializer(): NavigationCategoryDeserializer {
        return NavigationCategoryDeserializer()
    }

    @Provides
    @Singleton
    fun provideCache(@ApplicationContext context: Context): Cache {
        val cacheSize = 50L * 1024 * 1024 // 50MB
        return Cache(directory = File(context.cacheDir, "http_cache"), maxSize = cacheSize)
    }

    @Singleton
    @Provides
    fun provideClient(
        eatInterceptor: EatInterceptor,
        cache: Cache,
        httpLoggingInterceptor: HttpLoggingInterceptor
    ): OkHttpClient {
        val builder = OkHttpClient.Builder()
            .addInterceptor(eatInterceptor)
            .cache(cache = cache)
            .connectTimeout(40, TimeUnit.SECONDS)
            .readTimeout(40, TimeUnit.SECONDS)
        if (BuildConfig.DEBUG) {
            builder.addInterceptor(httpLoggingInterceptor)
        }

        return builder.build()
    }

    @Provides
    fun provideHttpLoggingInterceptor(): HttpLoggingInterceptor {
        return HttpLoggingInterceptor().setLevel(HttpLoggingInterceptor.Level.BODY)
    }

    @Singleton
    @Provides
    fun gson(
        dateTypeAdapter: DateTypeAdapter,
        guestsDeserializer: GuestsResponseDeserializer,
        guestDeserializer: GuestResponseDeserializer,
        reservationsResponseDeserializer: ReservationsResponseDeserializer,
        reservationResponseDeserializer: ReservationResponseDeserializer,
        restaurantResponseDeserializer: RestaurantResponseDeserializer,
        navigationItemDeserializer: NavigationCategoryDeserializer
    ): Gson {
        return GsonBuilder()
            .registerTypeAdapterFactory(FlattenTypeAdapterFactory())
            .registerTypeAdapter(Date::class.java, dateTypeAdapter)
            .registerTypeAdapter(GuestsResponse::class.java, guestsDeserializer)
            .registerTypeAdapter(GuestResponse::class.java, guestDeserializer)
            .registerTypeAdapter(ReservationsResponse::class.java, reservationsResponseDeserializer)
            .registerTypeAdapter(ReservationResponse::class.java, reservationResponseDeserializer)
            .registerTypeAdapter(RestaurantResponse::class.java, restaurantResponseDeserializer)
            .registerTypeAdapter(NavigationItemCategory::class.java, navigationItemDeserializer)
            .serializeNulls()
            .create()
    }

    @Singleton
    @Provides
    fun retrofit(okHttpClient: OkHttpClient, gson: Gson): Retrofit {
        return Retrofit.Builder()
            .client(okHttpClient)
            .baseUrl(Endpoints.restaurantEndpoint)
            .addCallAdapterFactory(CoroutineCallAdapterFactory())
            .addConverterFactory(GsonConverterFactory.create(gson))
            .build()
    }

    @Singleton
    @Provides
    fun provideApiFactory(retrofitApiFactoryImpl: RetrofitApiFactoryImpl): RetrofitApiFactory {
        return retrofitApiFactoryImpl
    }

    @Provides
    @Singleton
    fun provideEatApiRestaurant(apiFactory: RetrofitApiFactory): EatApiRestaurant {
        return apiFactory.buildApi(EatApiRestaurant::class.java, Endpoints.restaurantEndpoint)
    }

    @Provides
    @Singleton
    fun provideEatApiMessaging(apiFactory: RetrofitApiFactory): EatApiMessaging {
        return apiFactory.buildApi(EatApiMessaging::class.java, Endpoints.messagingEndpoint)
    }

    @Provides
    @Singleton
    fun provideEatApiPosService(apiFactory: RetrofitApiFactory): EatApiPosService {
        return apiFactory.buildApi(EatApiPosService::class.java, Endpoints.posEndPoint)
    }

    @Provides
    @Singleton
    fun provideEatApiPaymentsService(apiFactory: RetrofitApiFactory): EatApiPaymentsService {
        return apiFactory.buildApi(EatApiPaymentsService::class.java, Endpoints.paymentsEndPoint)
    }

    @Provides
    @Singleton
    fun provideEatApiHubspotService(apiFactory: RetrofitApiFactory): EatApiHubspotService {
        return apiFactory.buildApi(EatApiHubspotService::class.java, Endpoints.hubspotEndpoint)
    }

    @Provides
    @Singleton
    fun provideHydraWsService(eatInterceptor: EatInterceptor, gson: Gson): EatApiHydraWsService {
        return EatApiHydraWsService.invoke(eatInterceptor, gson)
    }

    @Provides
    @Singleton
    fun provideCanaryWsService(eatInterceptor: EatInterceptor, gson: Gson): EatApiCanaryWsService {
        return EatApiCanaryWsService.invoke(eatInterceptor, gson)
    }
}