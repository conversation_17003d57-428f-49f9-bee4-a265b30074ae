package com.eatapp.clementine.di

import com.google.gson.Gson
import com.jakewharton.retrofit2.adapter.kotlin.coroutines.CoroutineCallAdapterFactory
import okhttp3.OkHttpClient
import retrofit2.Retrofit
import retrofit2.converter.gson.GsonConverterFactory
import javax.inject.Inject

interface RetrofitApiFactory {
    fun <T> buildApi(type: Class<T>, endpoint: String): T
}

class RetrofitApiFactoryImpl @Inject constructor(
    private val okHttpClient: OkHttpClient,
    private val gson: Gson
) : RetrofitApiFactory {
    override fun <T> buildApi(type: Class<T>, endpoint: String): T {
        val retrofit = Retrofit.Builder()
            .client(okHttpClient)
            .baseUrl(endpoint)
            .addCallAdapterFactory(CoroutineCallAdapterFactory())
            .addConverterFactory(GsonConverterFactory.create(gson))
            .build()

        return retrofit.create(type)
    }
}