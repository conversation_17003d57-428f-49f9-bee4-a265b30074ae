package com.eatapp.clementine.ui.base

import android.content.Context.INPUT_METHOD_SERVICE
import android.content.Intent
import android.content.res.ColorStateList
import android.graphics.Color
import android.net.Uri
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.inputmethod.InputMethodManager
import android.view.inputmethod.InputMethodManager.SHOW_IMPLICIT
import android.widget.FrameLayout
import android.widget.LinearLayout
import androidx.appcompat.app.AlertDialog
import androidx.constraintlayout.widget.Group
import androidx.coordinatorlayout.widget.CoordinatorLayout
import androidx.core.content.ContextCompat
import androidx.databinding.ViewDataBinding
import androidx.fragment.app.Fragment
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import com.eatapp.clementine.BR
import com.eatapp.clementine.BuildConfig
import com.eatapp.clementine.R
import com.eatapp.clementine.data.network.response.product.CtaType
import com.eatapp.clementine.internal.Constants
import com.eatapp.clementine.internal.EatException
import com.eatapp.clementine.internal.ItemsGroup
import com.eatapp.clementine.internal.SelectorItem
import com.eatapp.clementine.internal.isTablet
import com.eatapp.clementine.internal.managers.EatManager
import com.eatapp.clementine.internal.managers.FeatureFlagsManager
import com.eatapp.clementine.internal.showErrorAlert
import com.eatapp.clementine.ui.common.webview.WebViewFragment
import com.eatapp.clementine.ui.launch.LaunchActivity
import com.eatapp.clementine.views.BottomSheetFragment
import com.eatapp.clementine.views.BottomSheetGroupView
import com.eatapp.clementine.views.BottomSheetView
import com.eatapp.clementine.views.LockdownView
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.google.android.material.bottomsheet.BottomSheetDialog
import kotlinx.coroutines.launch
import okhttp3.Call
import okhttp3.Callback
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.RequestBody
import okhttp3.Response
import okio.IOException
import java.net.URLDecoder
import javax.inject.Inject
import androidx.core.net.toUri
import com.google.android.material.shape.CornerFamily
import com.google.android.material.shape.MaterialShapeDrawable
import com.google.android.material.shape.ShapeAppearanceModel


abstract class BaseFragment<VM : BaseViewModel, DB : ViewDataBinding> : Fragment() {

    enum class ScreenType {
        Reservations,
        Waitlist,
        GuestSpend,
        DailyNotes,
        Guests,
        Reports,
        Omnisearch,
        Floor,
        Other
    }

    lateinit var vm: VM

    lateinit var binding: DB

    var progress: Group? = null
    private var alertDialog: AlertDialog? = null

    @Inject
    lateinit var featureFlagsManager: FeatureFlagsManager

    private fun bindContentView() {
        binding = inflateLayout()
        binding.lifecycleOwner = viewLifecycleOwner
        binding.setVariable(BR.viewmodel, vm)
        binding.executePendingBindings()
    }

    val isViewModelInitialized get() = ::vm.isInitialized

    abstract fun viewModelClass(): Class<VM>

    abstract fun onShowLockdownView(view: View)

    abstract fun onHideLockdownView(view: View)

    protected abstract fun inflateLayout(): DB

    protected abstract fun viewCreated()

    open fun reloadRestaurant() {}

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        vm = ViewModelProvider(this)[viewModelClass()]
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        bindContentView()
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        observe()
        lifecycleScope.launch {
            viewLifecycleOwner.repeatOnLifecycle(Lifecycle.State.CREATED) {
                viewCreated()
            }
        }
    }

    override fun onPause() {
        super.onPause()
        vm.paused = true
    }

    override fun onResume() {
        super.onResume()
        vm.paused = false
        if (context?.isTablet == false) {
            hideKeyboard()
        }
    }

    private fun observe() {

        vm.error.observe(viewLifecycleOwner) { error ->
            if (error is EatException && error.alert) {
                showErrorAlert(error.title, error.description)
            }
        }

        vm.loading.observe(viewLifecycleOwner) { loading ->

            when (loading) {
                true -> progress?.visibility = View.VISIBLE
                else -> progress?.visibility = View.GONE
            }
        }
    }

    fun checkLockdown(em: EatManager, screenType: ScreenType) {

        /**
         * Shows lockdown alert
         */
        addLockdownView(em, screenType)

    }

    private fun addLockdownView(em: EatManager, screenType: ScreenType) {

        em.productMessage()?.let {

            val ch = LockdownView(requireContext())

            val params = LinearLayout.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.MATCH_PARENT, 1.0f
            )
            ch.layoutParams = params
            ch.translationZ = 999F
            ch.updateWithSubscription(
                it,
                em.accountState(),
                em.token(),
                em.restaurantId(),
                screenType
            )

            ch.listener = { cta, token, resId ->

                onHideLockdownView(ch)

                if (cta?.text?.contains(
                        Constants.DEMO,
                        true
                    ) == true && featureFlagsManager.chilipiperEnabled
                ) {
                    val user = em.user()
                    val restaurant = em.restaurant()
                    val chilipiperHtml = getString(
                        com.eatapp.clementine.R.string.html,
                        user?.name,
                        user?.email,
                        restaurant?.phone,
                        restaurant?.attributes?.country,
                        restaurant?.name
                    )
                    val fragment = WebViewFragment.newInstance(chilipiperHtml, true)
                    bottomSheetFragment("", fragment, false, onDismiss = {
                        reloadRestaurant()
                    })
                } else {
                    when (cta?.action) {
                        CtaType.Api -> executeApiCall(cta.url, token, resId)
                        CtaType.Iframe -> loadIframe(cta.url, token, resId)
                        CtaType.Link -> openBrowser(cta.url, token, resId)
                        else -> {}
                    }
                }
            }

            onShowLockdownView(ch)
        }
    }

    private fun executeApiCall(url: String, token: String, resId: String) {

        val body = RequestBody.create(null, byteArrayOf())

        val request = Request.Builder()
            .url(URLDecoder.decode(url, "UTF-8"))
            .method("POST", body)
            .addHeader("Authorization", "Bearer $token")
            .addHeader(Constants.RESTAURANT_ID_HEADER, resId)
            .addHeader(
                "User-Agent",
                String.format("%s %s", BuildConfig.USER_AGENT, BuildConfig.VERSION_NAME)
            )
            .build()

        OkHttpClient().newCall(request).enqueue(object : Callback {
            override fun onFailure(call: Call, e: IOException) {
                requireContext().showErrorAlert(
                    resources.getString(com.eatapp.clementine.R.string.error),
                    resources.getString(com.eatapp.clementine.R.string.error_message)
                )
            }

            override fun onResponse(call: Call, response: Response) {}
        })
    }

    private fun loadIframe(url: String, token: String, resId: String) {
        bottomSheetFragment(
            resources.getString(com.eatapp.clementine.R.string.packages),
            WebViewFragment.newInstance(decodeAndReplaceUrl(url, resId, token), false),
            false,
            webViewMinusPadding = true,
            onDismiss = {
                reloadRestaurant()
            }
        )
    }

    private fun openBrowser(url: String, token: String, resId: String) {
        val browserIntent =
            Intent(Intent.ACTION_VIEW, decodeAndReplaceUrl(url, resId, token).toUri())
        context?.let { ContextCompat.startActivity(it, browserIntent, null) }
    }

    fun redirectToLogin() {
        val intent = Intent(requireContext(), LaunchActivity::class.java)
        startActivity(intent)
        requireActivity().finish()
    }

    fun bottomSheetFragment(
        sheetTitle: String?,
        fragment: Fragment,
        draggable: Boolean = true,
        cancellable: Boolean = true,
        showTitle: Boolean = true,
        webViewMinusPadding: Boolean = false,
        onDismiss: (() -> Unit)? = null
    ) {
        val dialog = BottomSheetFragment.newInstance(
            sheetTitle = sheetTitle,
            draggable = draggable,
            cancellable = cancellable,
            showTitle = showTitle,
            webViewMinusPadding = webViewMinusPadding
        ) {
            onDismiss?.let { it() }
        }

        dialog.show(fragment, requireActivity().supportFragmentManager, "dialog")
    }

    fun bottomSheetDialog(
        list: MutableList<SelectorItem>?,
        actionTitle: String,
        singleSelection: Boolean,
        centerItemHorizontally: Boolean = false,
        subtitle: String? = null,
        allowDeselect: Boolean = false,
        completion: (SelectorItem?) -> Unit
    ) {
        val bottomSheetDialog = BottomSheetDialog(requireContext())
        val view = BottomSheetView(
            requireContext(),
            items = list,
            actionTitle = actionTitle,
            subtitle = subtitle,
            singleSelection = singleSelection,
            centerItemsHorizontally = centerItemHorizontally,
            allowDeselect = allowDeselect
        )
        view.dismiss = {
            completion(it)
            bottomSheetDialog.dismiss()
        }

        showBottomSheetDialog(bottomSheetDialog, view)

    }

    fun bottomSheetGroupDialog(
        list: MutableList<ItemsGroup>,
        actionTitle: String,
        singleSelection: Boolean,
        completion: () -> Unit
    ) {
        val bottomSheetDialog = BottomSheetDialog(requireContext())
        val view = BottomSheetGroupView(
            requireContext(), items = list,
            actionTitle = actionTitle, singleSelection = singleSelection
        )
        view.dismiss = {
            completion()
            bottomSheetDialog.dismiss()
        }

        showBottomSheetDialog(bottomSheetDialog, view)
    }

    private fun showBottomSheetDialog(
        bottomSheetDialog: BottomSheetDialog,
        view: View
    ) {
        bottomSheetDialog.setContentView(view)

        (view.parent as View).setBackgroundColor(requireContext().resources.getColor(R.color.transparent))
        val layoutParams = (view.parent as View)
            .layoutParams as CoordinatorLayout.LayoutParams
        (view.parent as View).layoutParams = layoutParams

        bottomSheetDialog.setOnShowListener {
            val bottomSheet = bottomSheetDialog.findViewById<FrameLayout>(com.google.android.material.R.id.design_bottom_sheet)

            val shapeAppearanceModel = ShapeAppearanceModel.Builder()
                .setTopLeftCorner(CornerFamily.ROUNDED, 32f)
                .setTopRightCorner(CornerFamily.ROUNDED, 32f)
                .build()

            val background = MaterialShapeDrawable(shapeAppearanceModel).apply {
                fillColor = ColorStateList.valueOf(Color.WHITE)
            }

            bottomSheet?.background = background
        }

        bottomSheetDialog.show()
    }

    fun showErrorAlert(title: String, description: String) {
        if (alertDialog?.isShowing == true) return
        val builder = AlertDialog.Builder(requireContext())
        alertDialog = builder.setTitle(title)
            .setMessage(description)
            .setPositiveButton(R.string.cancel) { _, _ -> }
            .show()
    }

    fun showConfirmationAlert(title: Int, description: Int, listener: () -> Unit) {
        val builder = AlertDialog.Builder(requireContext())
        builder.setTitle(title)
            .setMessage(description)
            .setNegativeButton(R.string.cancel) { _, _ -> }
            .setPositiveButton(R.string.yes) { _, _ -> listener() }
            .show()
    }

    fun showAlert(
        title: String,
        description: String,
        positiveButtonText: String? = null,
        positiveButtonListener: (() -> Unit)? = null,
        negativeButtonText: String? = null,
        negativeButtonListener: (() -> Unit)? = null
    ) {
        val builder = AlertDialog.Builder(requireContext())
        builder.setTitle(title)
            .setMessage(description)

        positiveButtonText?.let {
            builder.setPositiveButton(
                positiveButtonText
            ) { _, _ -> positiveButtonListener?.invoke() }
        }

        negativeButtonText?.let {
            builder.setNegativeButton(
                negativeButtonText
            ) { _, _ -> negativeButtonListener?.invoke() }
        }

        builder.show()
    }

    fun showAlert(
        title: String,
        description: String,
        positiveButtonText: String? = null,
        positiveButtonListener: (() -> Unit)? = null,
        negativeButtonText: String? = null,
        negativeButtonListener: (() -> Unit)? = null,
        neutralButtonText: String? = null,
        neutralButtonListener: (() -> Unit)? = null
    ) {
        val builder = AlertDialog.Builder(requireContext())
        builder.setTitle(title)
            .setMessage(description)

        positiveButtonText?.let {
            builder.setPositiveButton(
                positiveButtonText
            ) { _, _ -> positiveButtonListener?.invoke() }
        }

        negativeButtonText?.let {
            builder.setNegativeButton(
                negativeButtonText
            ) { _, _ -> negativeButtonListener?.invoke() }
        }

        neutralButtonText?.let {
            builder.setNeutralButton(
                neutralButtonText
            ) { _, _ -> neutralButtonListener?.invoke() }
        }

        builder.show()
    }

    fun hideKeyboard() {
        try {
            val imm = activity?.getSystemService(INPUT_METHOD_SERVICE) as InputMethodManager
            if (imm.isAcceptingText) {
                imm.hideSoftInputFromWindow(requireView().windowToken, 0)
            }
        } catch (_: Exception) {
        }
    }

    fun showKeyboard() {
        val v = activity?.currentFocus
        val imm = activity?.getSystemService(INPUT_METHOD_SERVICE) as InputMethodManager?
        assert(imm != null && v != null)
        imm!!.showSoftInput(v, SHOW_IMPLICIT)
    }

    private fun decodeAndReplaceUrl(url: String, resId: String, token: String): String {
        return URLDecoder.decode(url, "UTF-8")
            .replace("{{restaurant_id}}", resId)
            .replace("{{platform}}", BuildConfig.USER_AGENT)
            .replace("{{token}}", token)
    }

    fun openBrowser(url: String) {
        Intent(Intent.ACTION_VIEW).apply {
            data = url.toUri()
            startActivity(this)
        }
    }
}