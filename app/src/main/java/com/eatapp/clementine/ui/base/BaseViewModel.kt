package com.eatapp.clementine.ui.base

import SingleLiveEvent
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.eatapp.clementine.internal.EatException
import kotlinx.coroutines.CoroutineExceptionHandler
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.launch
import java.util.Timer

abstract class BaseViewModel : ViewModel() {

    private val _error = SingleLiveEvent<Throwable>()
    val error: LiveData<Throwable> = _error

    private val _loading = MutableLiveData<Boolean>()
    val loading: LiveData<Boolean>
        get() = _loading

    /**
     * [paused] This is the flag used for all jobs that are scheduled to repeat.
     * Should change state following the lifecycle.
     */
    var timers: ArrayList<Timer> = ArrayList()

    /**
     * [paused] This is the flag used for all jobs that are scheduled to repeat.
     * Should change state following the lifecycle.
     */
    var paused = false

    /**
     * [_ceh] context element is used as generic catch block of coroutine.
     */
    private var _ceh = CoroutineExceptionHandler { _, err ->
       _error.postValue(err)
    }

    /**
     * Cancel all coroutines when the ViewModel is cleared
     */
    override fun onCleared() {
        super.onCleared()
        timers.forEach {
            it.cancel()
        }
        timers.clear()
    }

    protected fun loading(isLoading: Boolean) {
        _loading.postValue(isLoading)
    }

    protected fun setError(exception: EatException) {
        _error.postValue(exception)
    }

    protected fun launch(block: suspend CoroutineScope.() -> Unit, poll: Boolean = false): Job {
        return viewModelScope.launch(_ceh) {
            try {
                block()
            } catch (exception: Exception) {
                _ceh.handleException(this.coroutineContext, EatException(exception, poll))
                loading(false)
            }
        }
    }
}