package com.eatapp.clementine.ui.common

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import android.widget.ImageView
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.viewpager2.widget.ViewPager2
import com.eatapp.clementine.R
import com.eatapp.clementine.adapter.EatPagerAdapter
import com.eatapp.clementine.internal.px
import com.google.android.material.tabs.TabLayout
import com.google.android.material.tabs.TabLayoutMediator

class EatTabLayout @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : TabLayout(context, attrs, defStyleAttr) {

    private var viewPager: ViewPager2? = null
    private var adapter: EatPagerAdapter? = null
    private var mediator: TabLayoutMediator? = null

    init {
        setupTabLayout()
    }

    private fun setupTabLayout() {
        // Add tab selection listener
        addOnTabSelectedListener(object : OnTabSelectedListener {
            override fun onTabSelected(tab: Tab?) {
                tab?.position?.let { updateTabSelection(it) }
            }

            override fun onTabUnselected(tab: Tab?) {
                // Update all tabs to ensure proper state
            }

            override fun onTabReselected(tab: Tab?) {}
        })
    }

    /**
     * Setup the TabLayout with ViewPager2 and adapter
     */
    fun setupWithViewPager(viewPager: ViewPager2, adapter: EatPagerAdapter) {
        this.viewPager = viewPager
        this.adapter = adapter

        // Detach any existing mediator
        mediator?.detach()

        // Create new mediator
        mediator = TabLayoutMediator(this, viewPager) { tab, position ->
            setupCustomTab(tab, position)
        }
        mediator?.attach()

        // Set initial selection state after a short delay to ensure tabs are created
        post {
            updateAllTabsSelection()
            redistributeTabWidths()
        }
    }

    private fun setupCustomTab(tab: Tab, position: Int) {
        val customView = LayoutInflater.from(context).inflate(R.layout.custom_tab_layout, null)
        val tabIcon = customView.findViewById<ImageView>(R.id.tab_icon)
        val tabText = customView.findViewById<TextView>(R.id.tab_text)

        adapter?.let { adapter ->
            tabIcon.setImageResource(adapter.getPageIcon(position))
            tabText.text = adapter.getPageTitle(position)
        }

        tab.customView = customView
    }

    private fun updateTabSelection(selectedPosition: Int) {
        updateAllTabsSelection()
        redistributeTabWidths()
    }

    private fun updateAllTabsSelection() {
        val selectedPosition = selectedTabPosition

        for (i in 0 until tabCount) {
            val tab = getTabAt(i)
            val customView = tab?.customView
            val tabIcon = customView?.findViewById<ImageView>(R.id.tab_icon)
            val tabText = customView?.findViewById<TextView>(R.id.tab_text)

            if (i == selectedPosition) {
                // Selected tab: show icon + text
                tabText?.visibility = View.VISIBLE
                tabIcon?.setColorFilter(ContextCompat.getColor(context, R.color.colorPrimary))
                tabText?.setTextColor(ContextCompat.getColor(context, R.color.colorPrimary))
            } else {
                // Unselected tab: show only icon
                tabText?.visibility = View.GONE
                tabIcon?.setColorFilter(ContextCompat.getColor(context, R.color.colorDark50))
            }
        }
    }

    private fun redistributeTabWidths() {
        val selectedPosition = selectedTabPosition
        val tabCount = tabCount

        if (tabCount == 0) return

        post {
            val totalWidth = width
            if (totalWidth <= 0) return@post

            // Fixed width for selected tab (150dp)
            val selectedTabWidth = 150.px

            // Calculate remaining width for unselected tabs
            val remainingWidth = totalWidth - selectedTabWidth
            val unselectedTabCount = tabCount - 1
            val unselectedTabWidth = if (unselectedTabCount > 0) {
                maxOf(remainingWidth / unselectedTabCount, 48.px) // Minimum 48dp per tab
            } else {
                totalWidth // If only one tab, it takes full width
            }

            // Apply the calculated widths
            for (i in 0 until tabCount) {
                val tab = getTabAt(i)
                val customView = tab?.customView

                if (customView != null) {
                    val layoutParams = customView.layoutParams
                    if (i == selectedPosition) {
                        layoutParams.width = selectedTabWidth
                    } else {
                        layoutParams.width = unselectedTabWidth
                    }
                    customView.layoutParams = layoutParams
                }
            }
        }
    }

    /**
     * Show the TabLayout and separator
     */
    fun show() {
        visibility = View.VISIBLE
    }

    /**
     * Hide the TabLayout and separator
     */
    fun hide() {
        visibility = View.GONE
    }

    /**
     * Clean up resources when the view is detached
     */
    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        mediator?.detach()
        mediator = null
        viewPager = null
        adapter = null
    }
}
