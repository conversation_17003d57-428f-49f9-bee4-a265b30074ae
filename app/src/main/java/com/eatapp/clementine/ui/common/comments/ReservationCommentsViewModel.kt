package com.eatapp.clementine.ui.common.comments

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.eatapp.clementine.data.network.response.comment.Comment
import com.eatapp.clementine.data.network.response.reservation.Reservation
import com.eatapp.clementine.internal.SelectorItem
import com.eatapp.clementine.internal.asLiveData
import com.eatapp.clementine.internal.managers.EatManager
import com.eatapp.clementine.ui.base.BaseViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import java.util.Date
import javax.inject.Inject

@HiltViewModel
class ReservationCommentsViewModel @Inject constructor(
    private val eatManager: EatManager
) : BaseViewModel() {

    private val _comments = MutableLiveData<MutableList<Comment>>()
    val comments: LiveData<MutableList<Comment>> by lazy {
        _comments.asLiveData()
    }

    private val _taker = MutableLiveData<String>()
    val taker: LiveData<String> by lazy {
        _taker.asLiveData()
    }

    val commentText = MutableLiveData<String>()

    init {
        _taker.postValue(eatManager.findTaker())
    }

    fun reservation(res: Reservation?) {
        res?.comments?.forEach {
            it.color = color(staffName = it.name)
            it.editable = it.name == eatManager.findTaker()
        }

        _comments.postValue(res?.comments)
    }

    private fun checkEditable(taker: String?) {
        _comments.value?.forEach {
            it.editable = it.name == taker
        }
    }

    fun taker(taker: String) {
        _taker.postValue(taker)
        checkEditable(taker)
    }

    fun postNewComment() {

        if (commentText.value == null || commentText.value == "") return

        val comment = Comment(
            commentText.value ?: "",
            taker.value ?: "",
            Date(),
            color(taker.value),
            editable = true,
            isInEditMode = false
        )

        val c: MutableList<Comment>? = _comments.value?.toMutableList()
        c?.add(comment)

        _comments.postValue(c!!)

        commentText.postValue("")

    }

    fun staffNames(): MutableList<SelectorItem> {

       return eatManager.takers.map {

           SelectorItem(
               "",
               it.name ?: "",
               it,
               icon = null,
               color = 0,
               isSelected = false,
               isHeader = false,
               isDisabled = false
           )
       }.toMutableList()
    }

    fun color(staffName: String?): String {

        val allColors = listOf(
                "#336699",
                "#2F4858",
                "#99C5B5",
                "#FEDC97",
                "#B5B682",
                "#706C61",
                "#B4436C",
                "#F78154",
                "#F2C14E",
                "#3CDBD3",
                "#A63D40",
                "#E2D4B7"
        )

        eatManager.takers.forEachIndexed { index, staff ->
            if (staff.name == staffName) {
                val er = index % 12
                return allColors[er]
            }
        }

        return allColors[0]
    }
}