package com.eatapp.clementine.ui.common.custom_fields

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.eatapp.clementine.databinding.ListItemMultipleChoiceBinding

typealias MultipleChoiceSelectedListener = (List<String>)-> Unit

class CustomFieldMultiAdapter : ListAdapter<String, CustomFieldMultiAdapter.MultiChoiceViewHolder>(
    VoucherAssignmentItemDiffCallback()
) {

    var selectedOptions: MutableList<String> = mutableListOf()
    var selectedListener: MultipleChoiceSelectedListener? = null
    var enabled = true

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): MultiChoiceViewHolder {
        return MultiChoiceViewHolder(
            ListItemMultipleChoiceBinding.inflate(
                LayoutInflater.from(parent.context), parent, false
            )
        )
    }

    override fun onBindViewHolder(holder: MultiChoiceViewHolder, position: Int) {
        holder.bind(getItem(position))
    }

    inner class MultiChoiceViewHolder(val binding: ListItemMultipleChoiceBinding) :
        RecyclerView.ViewHolder(binding.root) {

        fun bind(field: String) = with(binding) {
            tvName.text = field
            cbValue.isChecked = selectedOptions.contains(field)
            cbValue.isEnabled = enabled
            cbValue.setOnClickListener {
                if (cbValue.isChecked) {
                    selectedOptions.add(field)
                } else {
                    val indexOfFirst = selectedOptions.indexOfFirst { it == field }
                    selectedOptions.removeAt(indexOfFirst)
                }

                selectedListener?.invoke(selectedOptions)
            }
        }
    }
}

class VoucherAssignmentItemDiffCallback : DiffUtil.ItemCallback<String>() {

    override fun areItemsTheSame(oldItem: String, newItem: String): Boolean {
        return oldItem == newItem
    }

    override fun areContentsTheSame(
        oldItem: String,
        newItem: String
    ): Boolean {
        return oldItem == newItem
    }
}