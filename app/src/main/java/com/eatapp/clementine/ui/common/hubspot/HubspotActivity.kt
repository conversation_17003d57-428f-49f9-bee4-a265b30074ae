package com.eatapp.clementine.ui.common.hubspot

import android.os.Bundle
import com.eatapp.clementine.R
import com.eatapp.clementine.databinding.HubspotActivityBinding
import com.eatapp.clementine.ui.base.BaseActivity
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class HubspotActivity : BaseActivity<HubspotActivityBinding>() {

    override fun getLayoutId() = R.layout.hubspot_activity

    override fun onCreated(savedInstanceState: Bundle?) {
        if (savedInstanceState == null) {
            supportFragmentManager.beginTransaction()
                .replace(
                    R.id.container,
                    HubspotFragment()
                )
                .commitNow()
        }

        binding.backBtnHC.setOnClickListener {
            finish()
        }
    }
}
