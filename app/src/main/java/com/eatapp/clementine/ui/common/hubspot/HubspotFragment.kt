package com.eatapp.clementine.ui.common.hubspot

import android.annotation.SuppressLint
import android.util.DisplayMetrics
import android.view.View
import android.webkit.WebChromeClient
import android.webkit.WebView
import android.widget.ProgressBar
import com.eatapp.clementine.BuildConfig
import com.eatapp.clementine.databinding.HubspotFragmentBinding
import com.eatapp.clementine.ui.base.BaseFragment
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class HubspotFragment : BaseFragment<HubspotViewModel, HubspotFragmentBinding>() {

    private val desktopUserAgent = "Mozilla/5.0 (X11; U; Linux i686; en-US; rv:*******) Gecko/20100101 Firefox/4.0"

    override fun viewModelClass() = HubspotViewModel::class.java

    override fun inflateLayout() = HubspotFragmentBinding.inflate(layoutInflater)

    override fun viewCreated() {
        observe()
    }

    override fun onShowLockdownView(view: View) {}

    override fun onHideLockdownView(view: View) {}

    private fun observe() {

        vm.hubspotToken.observe(viewLifecycleOwner) {
            loadHubspot(it)
        }
    }

    @SuppressLint("SetJavaScriptEnabled")
    private fun loadHubspot(pair: Pair<String, String>) {

        binding.webView.settings.javaScriptEnabled = true
        binding.webView.webChromeClient = CustomWebChromeClient(binding.progressBar)
        binding.webView.isVerticalScrollBarEnabled = true
        binding.webView.settings.userAgentString = desktopUserAgent

        val displayMetrics = DisplayMetrics()
        activity?.windowManager?.defaultDisplay?.getMetrics(displayMetrics)

        val html =
            "<!DOCTYPE html>" +
                    "<html lang='en-US'>" +
                    "<head>" +
                    "  <script>" +
                    "    window.hsConversationsSettings = {" +
                    "      loadImmediately: true," +
                    "      disableAttachment: true," +
                    "      inlineEmbedSelector: '#hub-spot-chat-container'," +
                    "      identificationEmail: '" + pair.first + "'," +
                    "      identificationToken: '" + pair.second + "'" +
                    "    };" +
                    "  </script>" +
                    "  <style>" +
                    "    #hubspot-conversations-inline-iframe {" +
                    "      width: -webkit-fill-available;" +
                    "      height: " + (view?.height?.div(displayMetrics.density)) + "px;" +
                    "      margin: -8px;" +
                    "      border: none;" +
                    "    }" +
                    "  </style>" +
                    "  <script type='text/javascript' id='hs-script-loader' async defer src='https://js.hs-scripts.com/" + BuildConfig.HUBSPOT_SCRIPT_ID + ".js'></script>" +
                    "</head>" +
                    "<body>" +
                    "<div id='hub-spot-chat-container'></div>" +
                    "</body>" +
                    "</html>"

        binding.webView.loadDataWithBaseURL(
            "https://www.google.com/",
            html,
            "text/html",
            "UTF-8",
            "index.html"
        )
    }
}

class CustomWebChromeClient(private val progressBar: ProgressBar) : WebChromeClient() {

    override fun onProgressChanged(view: WebView?, newProgress: Int) {
        super.onProgressChanged(view, newProgress)
        if (newProgress == 100) {
            android.os.Handler().postDelayed({ progressBar.visibility = View.GONE }, 1000)
        }
    }
}
