package com.eatapp.clementine.ui.common.hubspot

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.eatapp.clementine.data.repository.HubspotRepository
import com.eatapp.clementine.internal.JWTUtils
import com.eatapp.clementine.internal.asLiveData
import com.eatapp.clementine.internal.managers.AnalyticsManager
import com.eatapp.clementine.internal.managers.EatManager
import com.eatapp.clementine.ui.base.BaseViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import java.util.Date
import javax.inject.Inject

@HiltViewModel
class HubspotViewModel @Inject constructor(
    private val eatManager: EatManager,
    private val analyticsManager: AnalyticsManager,
    private val hubspotRepository: HubspotRepository
) : BaseViewModel() {

    private val _hubspotToken = MutableLiveData<Pair<String, String>>()
    val hubspotToken: LiveData<Pair<String, String>> by lazy {
        _hubspotToken.asLiveData()
    }

    init {

        analyticsManager.trackOpenHubspot()

        checkHubspotToken()

    }

    private fun checkHubspotToken() {

        val token = eatManager.hubspotToken()

        if (token == null) {
            fetchHubspotToken()
            return
        }

        val expiryDate = JWTUtils.expiryDate(token)

        if (expiryDate > Date()) {

            _hubspotToken.postValue(Pair(eatManager.user()?.email ?: "", token))

        } else {

            fetchHubspotToken()
        }
    }

    private fun fetchHubspotToken() {

        launch({

            val response = hubspotRepository.token()

            if (response.data.token.isNotEmpty()) {
                eatManager.hubspotToken(response.data.token)
                _hubspotToken.postValue(Pair(eatManager.user()?.email ?: "", response.data.token))
            }

        }, false)
    }
}