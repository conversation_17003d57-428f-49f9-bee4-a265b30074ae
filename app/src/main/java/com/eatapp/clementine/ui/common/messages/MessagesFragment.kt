package com.eatapp.clementine.ui.common.messages

import android.content.Intent
import android.view.Gravity
import android.view.View
import android.widget.Toast
import com.eatapp.clementine.R
import com.eatapp.clementine.adapter.MessagesAdapter
import com.eatapp.clementine.data.network.response.message.ChannelType
import com.eatapp.clementine.data.network.response.reservation.Reservation
import com.eatapp.clementine.data.network.response.restaurant.MessagingStatus
import com.eatapp.clementine.databinding.MessagesFragmentBinding
import com.eatapp.clementine.internal.Constants
import com.eatapp.clementine.internal.Status
import com.eatapp.clementine.ui.base.BaseFragment
import com.eatapp.clementine.views.LoadingButton
import dagger.hilt.android.AndroidEntryPoint
import android.view.ViewGroup
import android.view.WindowManager
import android.widget.PopupWindow
import androidx.core.content.ContextCompat
import androidx.navigation.fragment.findNavController
import com.eatapp.clementine.data.network.response.templates.Template
import com.eatapp.clementine.internal.px
import com.eatapp.clementine.ui.reservation.ReservationFragmentDirections
import com.eatapp.clementine.internal.Constants.TEMPLATE_EXTRA
import com.eatapp.clementine.views.ErrorDetailsView
import androidx.core.net.toUri
import com.eatapp.clementine.data.network.response.message.ActionType
import com.eatapp.clementine.internal.Endpoints
import com.eatapp.clementine.ui.common.webview.WebViewFragment
import com.eatapp.clementine.data.network.response.message.AddonPath
import com.eatapp.clementine.ui.common.hubspot.HubspotActivity
import com.eatapp.clementine.ui.reservation.ReservationActivity
import com.jay.widget.StickyHeadersLinearLayoutManager

@AndroidEntryPoint
class MessagesFragment: BaseFragment<MessagesViewModel, MessagesFragmentBinding>() {

    private lateinit var adapter: MessagesAdapter

    override fun viewModelClass() = MessagesViewModel::class.java

    override fun inflateLayout() = MessagesFragmentBinding.inflate(layoutInflater)

    override fun viewCreated() {

        observe()

        adapter = MessagesAdapter()
        binding.messagesList.layoutManager = StickyHeadersLinearLayoutManager<MessagesAdapter>(context)

        adapter.failedMessageListener = { anchorView, message ->
            // Show error alert with message details
            val errorDetailsView = ErrorDetailsView(requireContext())
            errorDetailsView.setMessage(message)

            val popupWindow = PopupWindow(
                errorDetailsView,
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.WRAP_CONTENT,
                true
            )

            errorDetailsView.onCloseClickListener = {
                popupWindow.dismiss()
            }

            errorDetailsView.onMetaClickListener = {
                val intent = Intent(Intent.ACTION_VIEW, it.toUri())
                startActivity(intent)
            }

            // Check if there's enough space below the anchor view
            val anchorLocation = IntArray(2)
            anchorView.getLocationOnScreen(anchorLocation)
            val screenHeight = resources.displayMetrics.heightPixels
            val spaceBelow = screenHeight - anchorLocation[1] - anchorView.height

            errorDetailsView.measure(
                View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED),
                View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED)
            )

            if (spaceBelow < errorDetailsView.measuredHeight) {
                popupWindow.showAsDropDown(anchorView, 0, -errorDetailsView.measuredHeight - anchorView.height)
            } else {
                popupWindow.showAsDropDown(anchorView)
            }
        }

        binding.messagesList.adapter = adapter

        (activity as? ReservationActivity)?.reservation
            ?.let { reservation ->
                vm.reservation(reservation)
            }

        activity?.intent?.extras?.getString(Constants.RESTAURANT_ID_EXTRA)
            ?.let { vm.restaurantId(it) }

        bindUI()
    }

    override fun onShowLockdownView(view: View) {}

    override fun onHideLockdownView(view: View) {}

    private fun bindUI() {
        progress = binding.progress

        setupActionButtons()

        binding.whatsappButton.setOnClickListener(whatsappClickListener)
        binding.smsButton.setOnClickListener(smsClickListener)
        binding.emailButton.setOnClickListener(emailClickListener)

        binding.sendButton.setOnClickListener {
            val messageText = binding.messageInput.text.toString().trim()
            if (messageText.isNotEmpty()) {
                vm.sendMessage(null, messageText)
                binding.messageInput.text?.clear()
            }
        }

        binding.channelButton.setOnClickListener { view ->
            showChannelsMenu(view)
        }

        binding.templateButton.setOnClickListener {
            if (vm.preferredChannel == ChannelType.SMS && (vm.eatManager.restaurant()?.smsStatus == MessagingStatus.DISABLED || vm.eatManager.restaurant()?.smsStatus == MessagingStatus.BLOCKED)) {
                Toast.makeText(requireContext(), getString(R.string.sms_messaging_disabled), Toast.LENGTH_LONG).show()
            } else if (vm.preferredChannel == ChannelType.WHATSAPP && (vm.eatManager.restaurant()?.whatsappStatus == MessagingStatus.DISABLED || vm.eatManager.restaurant()?.whatsappStatus == MessagingStatus.BLOCKED)) {
                Toast.makeText(requireContext(), getString(R.string.whatsapp_messaging_disabled), Toast.LENGTH_LONG).show()
            } else {
                findNavController().navigate(
                    ReservationFragmentDirections
                        .actionReservationFragmentToTemplatesFragment(
                            vm.reservation, vm.preferredChannel, vm.messageTypes().toTypedArray()
                        )
                )
            }
        }

        binding.sendButton.setOnClickListener {
            val messageText = binding.messageInput.text.toString().trim()
            if (messageText.isNotEmpty()) {
                vm.sendMessage(null, messageText)
                binding.messageInput.text?.clear()
            }
        }
    }

    private fun observe() {
        vm.messages.observe(viewLifecycleOwner) { messages ->
            adapter.submitMessages(messages)

            if (messages.isNotEmpty() && vm.newMessage) {
                binding.messagesList.smoothScrollToPosition(adapter.currentList.size)
                vm.newMessage = false
            }

            if (vm.initialFetch) {
                updateBottomContainer()
            }
        }

        vm.messageError.observe(viewLifecycleOwner) { error ->
            val (title, message) = error
            showErrorAlert(title, message)
        }

        vm.canSendWhatsapp.observe(viewLifecycleOwner) {
            when (it || binding.whatsappButton.state == LoadingButton.LoadingButtonState.Disabled) {
                true -> binding.whatsappButton.iconRight.visibility = View.GONE
                false -> binding.whatsappButton.setRightIcon(
                    ContextCompat.getDrawable(requireContext(), R.drawable.ic_warning)!!,
                    R.color.green200)
            }
        }

        vm.canSendSms.observe(viewLifecycleOwner) {
            when (it || binding.smsButton.state == LoadingButton.LoadingButtonState.Disabled) {
                true -> binding.smsButton.iconRight.visibility = View.GONE
                false -> binding.smsButton.setRightIcon(
                    ContextCompat.getDrawable(requireContext(), R.drawable.ic_warning)!!,
                    R.color.green200)
            }
        }

        vm.canSendEmail.observe(viewLifecycleOwner) {
            when (it || binding.emailButton.state == LoadingButton.LoadingButtonState.Disabled) {
                true -> binding.emailButton.iconRight.visibility = View.GONE
                false -> binding.emailButton.setRightIcon(
                    ContextCompat.getDrawable(requireContext(), R.drawable.ic_warning)!!,
                    R.color.green200)
            }
        }

        // Observe for template selection
        findNavController().currentBackStackEntry?.savedStateHandle?.getLiveData<Template>(
            TEMPLATE_EXTRA
        )?.observe(viewLifecycleOwner) { template ->
            vm.sendMessage(template, null)
        }
    }

    private fun setupActionButtons() {
        val guest = vm.reservation?.guest
        val hasPhone = guest?.phone?.isNotEmpty() == true
        val hasEmail = guest?.email?.isNotEmpty() == true

        // Setup email button if not waitlist
        if (vm.reservation?.status == Status.Value.WAITLIST.code) {
            binding.emailButton.state = LoadingButton.LoadingButtonState.Disabled
        } else if (guest == null || !hasEmail) {
            binding.emailButton.state = LoadingButton.LoadingButtonState.Disabled
        }

        if (guest == null || !hasPhone) {
            binding.smsButton.state = LoadingButton.LoadingButtonState.Disabled
            binding.whatsappButton.state = LoadingButton.LoadingButtonState.Disabled
        }
    }

    private fun updateBottomContainer() {
        if (vm.canSendWhatsapp.value == true) {
            showSendMessageContainer(ChannelType.WHATSAPP)
        } else {
            showActionButtons()
        }
    }

    private fun showActionButtons() {
        binding.actionButtonsCont.visibility = View.VISIBLE
        binding.sendMessageCont.visibility = View.GONE
    }

    private fun showSendMessageContainer(channel: ChannelType) {
        binding.actionButtonsCont.visibility = View.GONE
        binding.sendMessageCont.visibility = View.VISIBLE
        updatePreferredChannel(channel)
    }

    private fun updatePreferredChannel(channel: ChannelType) {
        vm.preferredChannel = channel

        val smsEnabled = vm.canSendSms.value == true && !vm.reservation?.guest?.phone.isNullOrEmpty()
        val whatsappEnabled = vm.canSendWhatsapp.value == true && !vm.reservation?.guest?.phone.isNullOrEmpty()
        val emailEnabled = vm.canSendEmail.value == true && !vm.reservation?.guest?.email.isNullOrEmpty()

        when (channel) {
            ChannelType.SMS -> {
                binding.messageInput.isEnabled = smsEnabled
                binding.messageInput.hint = if (smsEnabled) getString(R.string.type_your_message) else getString(R.string.cant_send_text)
                binding.channelButton.setImageResource(R.drawable.ic_icon_sms)
            }
            ChannelType.EMAIL -> {
                binding.messageInput.isEnabled = emailEnabled
                binding.messageInput.hint = if (emailEnabled) getString(R.string.type_your_message) else getString(R.string.cant_send_text)
                binding.channelButton.setImageResource(R.drawable.ic_icon_email)
            }
            ChannelType.WHATSAPP -> {
                binding.messageInput.isEnabled = whatsappEnabled
                binding.messageInput.hint = if (whatsappEnabled) getString(R.string.type_your_message) else getString(R.string.cant_send_text)
                binding.channelButton.setImageResource(R.drawable.ic_icon_whatsapp)
            }
        }

        binding.channelButton.isEnabled = smsEnabled || whatsappEnabled || emailEnabled
        binding.sendButton.isEnabled = smsEnabled || whatsappEnabled || emailEnabled
    }
    
    private fun showChannelsMenu(view: View) {
        val popupView = layoutInflater.inflate(R.layout.popup_channel_menu, null)
        val popupWindow = PopupWindow(
            popupView,
            ViewGroup.LayoutParams.WRAP_CONTENT,
            ViewGroup.LayoutParams.WRAP_CONTENT,
            false
        )

        val phoneEnabled = !vm.reservation?.guest?.phone.isNullOrEmpty()
        val emailEnabled = !vm.reservation?.guest?.email.isNullOrEmpty()

        // Hide options based on enabled status
        popupView.findViewById<View>(R.id.sms_option).apply {
            visibility = if (phoneEnabled) View.VISIBLE else View.GONE
            setOnClickListener {
                smsClickListener.onClick(null)
                popupWindow.dismiss()
            }
        }

        popupView.findViewById<View>(R.id.whatsapp_option).apply {
            visibility = if (phoneEnabled) View.VISIBLE else View.GONE
            setOnClickListener {
                whatsappClickListener.onClick(null)
                popupWindow.dismiss()
            }
        }

        popupView.findViewById<View>(R.id.email_option).apply {
            visibility = if (emailEnabled) View.VISIBLE else View.GONE
            setOnClickListener {
                emailClickListener.onClick(null)
                popupWindow.dismiss()
            }
        }

        // Don't show popup if no options are available
        if (!phoneEnabled && !emailEnabled) {
            return
        }

        // Prevent keyboard dismissal
        popupWindow.inputMethodMode = PopupWindow.INPUT_METHOD_NEEDED
        popupWindow.softInputMode = WindowManager.LayoutParams.SOFT_INPUT_ADJUST_RESIZE
        popupWindow.isTouchable = true
        popupWindow.isOutsideTouchable = true

        // Measure the popupView before showing
        popupView.measure(
            View.MeasureSpec.UNSPECIFIED,
            View.MeasureSpec.UNSPECIFIED
        )

        val popupHeight = popupView.measuredHeight
        val popupWidth = popupView.measuredWidth

        // Get anchor's screen position
        val location = IntArray(2)
        view.getLocationOnScreen(location)
        val anchorX = location[0]
        val anchorY = location[1]

        // Animation style
        popupWindow.animationStyle = R.style.PopupAnimation

        // Calculate X & Y to show above the anchor
        val xOffset = anchorX + (view.width - popupWidth) / 2 // center align
        val yOffset = anchorY - popupHeight + 10.px

        // Show popup above the channel button
        popupWindow.showAtLocation(view, Gravity.NO_GRAVITY, xOffset, yOffset)
    }

    private fun buildAddonUrl(addon: AddonPath): String = buildString {
        append("${Endpoints.adminEndpoint}/auth")
        append("?path=${addon.path}")
        append("&addon_type=${addon.addonType ?: ""}")
        append("&embedded=${addon.embedded ?: ""}")
        append("&iframe=${addon.iframe}")
        append("&type=${addon.type ?: ""}")
        append("&restaurant_id=${vm.eatManager.restaurantId()}")
        append("&token=${vm.eatManager.token()}")
    }

    private val whatsappClickListener = View.OnClickListener {
        if (vm.canSendWhatsapp.value == true) {
            showSendMessageContainer(ChannelType.WHATSAPP)
        } else {
            vm.errorDetails.value?.whatsapp?.let { whatsappError ->
                showAlert(
                    whatsappError.display,
                    whatsappError.tooltip,
                    negativeButtonText = resources.getString(R.string.cancel),
                    positiveButtonText = whatsappError.actionText,
                    positiveButtonListener = {
                        when (whatsappError.actionType) {
                            ActionType.ADDON -> {
                                whatsappError.addonPath?.let { addon ->
                                    bottomSheetFragment(
                                        whatsappError.actionText,
                                        WebViewFragment.newInstance(buildAddonUrl(addon), false),
                                        false,
                                        webViewMinusPadding = true
                                    )
                                }
                            }

                            ActionType.SUPPORT -> {
                                val intent = Intent(context, HubspotActivity::class.java)
                                startActivity(intent)
                            }

                            else -> {
                                findNavController().navigate(
                                    ReservationFragmentDirections
                                        .actionReservationFragmentToTemplatesFragment(
                                            vm.reservation, ChannelType.WHATSAPP,
                                            vm.messageTypes(channel = ChannelType.WHATSAPP)
                                                .toTypedArray()
                                        )
                                )
                            }
                        }
                    })
            }
        }
    }

    private val smsClickListener = View.OnClickListener {
        if (vm.canSendSms.value == true) {
            showSendMessageContainer(ChannelType.SMS)
        } else {
            vm.errorDetails.value?.sms?.let { smsError ->
                showAlert(
                    smsError.display,
                    smsError.tooltip, negativeButtonText = resources.getString(R.string.cancel),
                    positiveButtonText = smsError.actionText,
                    positiveButtonListener = {
                        when (smsError.actionType) {
                            ActionType.ADDON -> {
                                smsError.addonPath?.let { addon ->
                                    bottomSheetFragment(
                                        smsError.actionText,
                                        WebViewFragment.newInstance(buildAddonUrl(addon), false),
                                        false,
                                        webViewMinusPadding = true
                                    )
                                }
                            }

                            ActionType.SUPPORT -> {
                                val intent = Intent(context, HubspotActivity::class.java)
                                startActivity(intent)
                            }

                            else -> {
                                findNavController().navigate(
                                    ReservationFragmentDirections
                                        .actionReservationFragmentToTemplatesFragment(
                                            vm.reservation, ChannelType.SMS,
                                            vm.messageTypes(channel = ChannelType.SMS)
                                                .toTypedArray()
                                        )
                                )
                            }
                        }
                    })
            }
        }
    }

    private val emailClickListener = View.OnClickListener {
        if (vm.canSendEmail.value == true) {
            showSendMessageContainer(ChannelType.EMAIL)
        } else {
            vm.errorDetails.value?.email?.let { emailError ->
                showAlert(
                    emailError.display,
                    emailError.tooltip,
                    negativeButtonText = resources.getString(R.string.cancel),
                    positiveButtonText = emailError.actionText,
                    positiveButtonListener = {
                        when (emailError.actionType) {
                            ActionType.ADDON -> {
                                emailError.addonPath?.let { addon ->
                                    bottomSheetFragment(
                                        emailError.actionText,
                                        WebViewFragment.newInstance(buildAddonUrl(addon), false),
                                        false,
                                        webViewMinusPadding = true
                                    )
                                }
                            }

                            ActionType.SUPPORT -> {
                                val intent = Intent(context, HubspotActivity::class.java)
                                startActivity(intent)
                            }

                            else -> {
                                findNavController().navigate(
                                    ReservationFragmentDirections
                                        .actionReservationFragmentToTemplatesFragment(
                                            vm.reservation, ChannelType.EMAIL,
                                            vm.messageTypes(channel = ChannelType.EMAIL)
                                                .toTypedArray()
                                        )
                                )
                            }
                        }
                    })
            }
        }
    }
}