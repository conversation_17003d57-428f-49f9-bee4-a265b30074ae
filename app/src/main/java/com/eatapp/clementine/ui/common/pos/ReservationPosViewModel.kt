package com.eatapp.clementine.ui.common.pos

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.eatapp.clementine.data.network.response.pos.PosRecord
import com.eatapp.clementine.data.network.response.reservation.Reservation
import com.eatapp.clementine.data.repository.PosRepository
import com.eatapp.clementine.internal.asLiveData
import com.eatapp.clementine.internal.managers.EatManager
import com.eatapp.clementine.ui.base.BaseViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject

@HiltViewModel
class ReservationPosViewModel @Inject constructor(
    eatManager: EatManager,
    private val posRepository: PosRepository
) : BaseViewModel() {

    var currency: String? = eatManager.restaurant()?.currency ?: "AED"
    val posActive: Boolean = eatManager.restaurant()?.posActive == true

    private val _posRecord = MutableLiveData<PosRecord?>()
    val posRecord: LiveData<PosRecord?> by lazy {
        _posRecord.asLiveData()
    }

    private val _reservation = MutableLiveData<Reservation?>()
    val reservation: LiveData<Reservation?> by lazy {
        _reservation.asLiveData()
    }

    fun reservation(reservation: Reservation?) {

        _reservation.postValue(reservation)
    }

    fun posRecord(posRecord: PosRecord?) {

        _posRecord.postValue(posRecord)
    }

    fun fetchPosData(posRecordId: String) {

        launch({

            loading(true)

            val posRecordResponse = posRepository.posRecord(posRecordId)
            _posRecord.postValue(posRecordResponse.posRecord)

            loading(false)

        }, false)
    }
}