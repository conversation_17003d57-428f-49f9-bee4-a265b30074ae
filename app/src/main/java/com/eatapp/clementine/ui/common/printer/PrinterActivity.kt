package com.eatapp.clementine.ui.common.printer

import android.os.Bundle
import com.eatapp.clementine.R
import com.eatapp.clementine.databinding.PrinterActivityBinding
import com.eatapp.clementine.ui.base.BaseActivity
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class PrinterActivity : BaseActivity<PrinterActivityBinding>() {

    override fun getLayoutId() = R.layout.printer_activity

    override fun onCreated(savedInstanceState: Bundle?) {
        if (savedInstanceState == null) {
            supportFragmentManager.beginTransaction()
                .replace(
                    R.id.container,
                    PrinterFragment()
                )
                .commitNow()
        }

        binding.backBtnHC.setOnClickListener {
            finish()
        }
    }
}