package com.eatapp.clementine.ui.common.printer

import android.Manifest
import android.app.Activity
import android.bluetooth.BluetoothAdapter
import android.bluetooth.BluetoothManager
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.graphics.Bitmap
import android.os.Build
import android.os.Bundle
import android.view.View
import androidx.activity.result.contract.ActivityResultContracts
import androidx.activity.result.contract.ActivityResultContracts.StartActivityForResult
import androidx.annotation.RequiresApi
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import androidx.core.graphics.drawable.toBitmap
import com.eatapp.clementine.R
import com.eatapp.clementine.adapter.ChitPrintConfigAdapter
import com.eatapp.clementine.databinding.PrinterFragmentBinding
import com.eatapp.clementine.internal.Constants
import com.eatapp.clementine.internal.showErrorAlert
import com.eatapp.clementine.ui.base.BaseFragment
import com.eatapp.clementine.views.BottomSheetFragment
import com.eatapp.clementine.views.LoadingButton
import dagger.hilt.android.AndroidEntryPoint


@AndroidEntryPoint
class PrinterFragment : BaseFragment<PrinterViewModel, PrinterFragmentBinding>() {

    private lateinit var chitConfigAdapter: ChitPrintConfigAdapter

    companion object {
        fun newInstance(printMode: Boolean) = PrinterFragment().apply {
            arguments = Bundle().apply {
                putSerializable(Constants.PRINT_MODE, printMode)
            }
        }
    }

    override fun viewModelClass() = PrinterViewModel::class.java

    override fun inflateLayout() = PrinterFragmentBinding.inflate(layoutInflater)

    override fun viewCreated() {
        arguments?.getBoolean(Constants.PRINT_MODE)?.let { vm.printMode(it) }

        observe()

        val bluetoothManager = requireActivity().getSystemService(Context.BLUETOOTH_SERVICE) as BluetoothManager
        vm.bluetoothAdapter = bluetoothManager.adapter

        when (vm.bluetoothAdapter?.isEnabled == true) {
            true -> checkPermissions()
            false -> {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                    if (isBlueToothPermissionGranted()) {
                        val intent = Intent(BluetoothAdapter.ACTION_REQUEST_ENABLE)
                        startActivityIntent.launch(intent)
                    } else {
                        askForBlueToothPermission()
                    }
                } else {
                    val intent = Intent(BluetoothAdapter.ACTION_REQUEST_ENABLE)
                    startActivityIntent.launch(intent)
                }
            }
        }

        binding.connectBtn.state = LoadingButton.LoadingButtonState.Disabled

        binding.testPrint.setOnClickListener {

            if(!vm.printManager.isConnected) {
                requireContext().showErrorAlert(
                    resources.getString(R.string.no_printer_connected_title),
                    resources.getString(R.string.no_printer_connected_short_desc)
                )
                return@setOnClickListener
            }
//
//            val ch = ChitPrint(requireContext())
//
//            val params = LinearLayout.LayoutParams(
//                PrintManager.chitWidth,
//                LinearLayout.LayoutParams.WRAP_CONTENT, 1.0f
//            )
//            ch.mainCont.layoutParams = params

            val logo: Bitmap? =
                ContextCompat.getDrawable(requireContext(), R.drawable.ic_logo)?.toBitmap()
                    ?.let { Bitmap.createScaledBitmap(it, 120, 60, true) }

            vm.testPrint(null, logo)
        }

        binding.print.setOnClickListener {
            val parent: BottomSheetFragment? = parentFragment as? BottomSheetFragment
            parent?.dismiss()
            parent?.onDismiss?.let { d -> d() }
        }

        chitConfigAdapter = ChitPrintConfigAdapter { item ->

            if (!item.enabled) return@ChitPrintConfigAdapter

            val list = vm.chitPrintConfigDatasource.value?.map { it.copy() }?.toMutableList()
            val equivalent = list?.find { it.key == item.key }
            equivalent?.selected = item.selected.not()

            vm.updateList(list, equivalent)
        }

        binding.chitPrintConfigList.adapter = chitConfigAdapter
    }

    @RequiresApi(Build.VERSION_CODES.S)
    private fun isBlueToothPermissionGranted(): Boolean {
        return ActivityCompat.checkSelfPermission(
            requireContext(),
            Manifest.permission.BLUETOOTH_CONNECT
        ) == PackageManager.PERMISSION_GRANTED
    }

    @RequiresApi(Build.VERSION_CODES.S)
    private fun askForBlueToothPermission() {
        blueToothAdapterRequestPermissionLauncher.launch(Manifest.permission.BLUETOOTH_CONNECT)
    }

    private val blueToothAdapterRequestPermissionLauncher = registerForActivityResult(
        ActivityResultContracts.RequestPermission()
    ) { isGranted ->
        if (isGranted) {
            val intent = Intent(BluetoothAdapter.ACTION_REQUEST_ENABLE)
            startActivityIntent.launch(intent)
        } else {
            requireContext().showErrorAlert(getString(R.string.bluetooth_permission_title), getString(R.string.bluetooth_permission_description))
        }
    }

    private var startActivityIntent = registerForActivityResult(
        StartActivityForResult()
    ) {
        if (it.resultCode == Activity.RESULT_OK) {
            checkPermissions()
        }
    }

    private fun checkPermissions() {

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {

            if ((ActivityCompat.checkSelfPermission(requireContext(),
                    Manifest.permission.BLUETOOTH_SCAN) != PackageManager.PERMISSION_GRANTED) ||
                (ActivityCompat.checkSelfPermission(requireContext(),
                    Manifest.permission.BLUETOOTH_CONNECT) != PackageManager.PERMISSION_GRANTED)
            ) {
                requestBlueToothPermissions.launch(arrayOf(
                    Manifest.permission.BLUETOOTH_SCAN,
                    Manifest.permission.BLUETOOTH_CONNECT)
                )
            } else {
                vm.findDevices()
            }
        } else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {

            if ((ActivityCompat.checkSelfPermission(requireContext(),
                    Manifest.permission.ACCESS_FINE_LOCATION) != PackageManager.PERMISSION_GRANTED) ||
                (ActivityCompat.checkSelfPermission(requireContext(),
                    Manifest.permission.ACCESS_COARSE_LOCATION) != PackageManager.PERMISSION_GRANTED)
            ) {
                requestLocationPermissions.launch(arrayOf(
                    Manifest.permission.ACCESS_FINE_LOCATION,
                    Manifest.permission.ACCESS_COARSE_LOCATION)
                )
            } else {
                vm.findDevices()
            }
        } else {
            vm.findDevices()
        }
    }

    private val requestBlueToothPermissions =
        registerForActivityResult(ActivityResultContracts.RequestMultiplePermissions()) { permissions ->
            checkIfGranted(permissions)
        }

    private val requestLocationPermissions =
        registerForActivityResult(ActivityResultContracts.RequestMultiplePermissions()) { permissions ->
            checkIfGranted(permissions)
        }

    private fun checkIfGranted(permissions: Map<String, @JvmSuppressWildcards Boolean>) {
        repeat(permissions.entries.size) {
            val granted = permissions.entries.all {
                it.value
            }
            if (granted) {
                vm.findDevices()
            }
        }
    }

    override fun onShowLockdownView(view: View) {}

    override fun onHideLockdownView(view: View) {}

    private fun observe() {

        vm.loading.observe(viewLifecycleOwner) {
            when (it) {
                true -> binding.connectBtn.showLoading()
                else -> binding.connectBtn.hideLoading()
            }
        }

        vm.printerName.observe(viewLifecycleOwner) {
            when (it.lowercase().contains("printer")) {
                true -> binding.connectBtn.state = LoadingButton.LoadingButtonState.Disabled
                else -> binding.connectBtn.state = LoadingButton.LoadingButtonState.Available
            }
        }

        vm.chitPrintConfigDatasource.observe(viewLifecycleOwner) {
            chitConfigAdapter.submitList(it)
        }

        vm.askMeEveryTime.observe(viewLifecycleOwner) {
            binding.askMeSwitch.isEnabled
        }
    }
}
