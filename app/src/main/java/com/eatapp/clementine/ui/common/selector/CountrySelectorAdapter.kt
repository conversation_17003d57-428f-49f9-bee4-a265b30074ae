package com.eatapp.clementine.ui.common.selector

import android.annotation.SuppressLint
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.eatapp.clementine.R
import com.eatapp.clementine.data.network.response.apiresources.Country
import com.eatapp.clementine.databinding.ListItemCountryBinding

class CountrySelectorAdapter(
    private val clickListener: (Country) -> Unit
) :
    ListAdapter<Country, CountrySelectorAdapter.CountrySelectorViewHolder>(CountryDiffCallback()) {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): CountrySelectorViewHolder {
        return CountrySelectorViewHolder(
            ListItemCountryBinding.inflate(
                LayoutInflater.from(parent.context), parent, false
            )
        )
    }

    override fun onBindViewHolder(holder: CountrySelectorViewHolder, position: Int) {
        holder.bind(getItem(position))
    }

    inner class CountrySelectorViewHolder(private val binding: ListItemCountryBinding) :
        RecyclerView.ViewHolder(binding.root) {
        @SuppressLint("SetTextI18n")
        fun bind(country: Country) = with(binding) {
            country.icon?.let {
                ivFlag.setImageResource(it)
            }
            tvPhonePrefix.text = "+${country.phonePrefix}"
            tvCountryName.text = country.name

            root.setBackgroundColor(
                if (country.selected) {
                    ContextCompat.getColor(root.context, R.color.green50)
                } else {
                    ContextCompat.getColor(root.context, R.color.white)
                }
            )

            root.setOnClickListener {
                clickListener.invoke(country)
            }
        }
    }
}

private class CountryDiffCallback : DiffUtil.ItemCallback<Country>() {

    override fun areItemsTheSame(oldItem: Country, newItem: Country): Boolean {
        return oldItem.code == newItem.code
    }

    override fun areContentsTheSame(oldItem: Country, newItem: Country): Boolean {
        return oldItem == newItem
    }
}