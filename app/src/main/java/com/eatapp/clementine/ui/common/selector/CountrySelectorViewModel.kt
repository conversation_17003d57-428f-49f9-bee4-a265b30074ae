package com.eatapp.clementine.ui.common.selector

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.eatapp.clementine.data.network.response.apiresources.Country
import com.eatapp.clementine.internal.managers.EatManager
import com.eatapp.clementine.ui.base.BaseViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject

@HiltViewModel
class CountrySelectorViewModel @Inject constructor(
    private val eatManager: EatManager
) : BaseViewModel() {

    private val _countries = MutableLiveData<List<Country>>()
    val countries: LiveData<List<Country>> = _countries

    init {
        val countries = countries(null)

        _countries.value = countries.toList()
    }

    private fun countries(country: Country?): List<Country> {
        return eatManager.countries()
    }

    fun searchCountries(search: String) {
        val result = eatManager.countries().filter {
            it.name.contains(search, ignoreCase = true) || it.phoneCode.contains(
                search,
                ignoreCase = true
            ) || it.code.contains(search, ignoreCase = true)
        }
        _countries.value = result
    }
}