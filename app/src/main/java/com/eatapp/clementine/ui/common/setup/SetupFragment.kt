package com.eatapp.clementine.ui.common.setup

import android.media.RingtoneManager
import android.net.Uri
import android.view.View
import com.eatapp.clementine.databinding.SetupFragmentBinding
import com.eatapp.clementine.ui.base.BaseFragment
import com.eatapp.clementine.views.BottomSheetFragment
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class SetupFragment:
    BaseFragment<SetupViewModel, SetupFragmentBinding>() {

    override fun viewModelClass() = SetupViewModel::class.java

    override fun inflateLayout() = SetupFragmentBinding.inflate(layoutInflater)

    override fun viewCreated() {

        binding.mainAction.setOnClickListener {

            val parent: BottomSheetFragment? = parentFragment as? BottomSheetFragment
            parent?.dismiss()
            parent?.onDismiss?.let { d -> d() }

        }

        observe()
    }

    private fun observe() {

        vm.onboardingCompleted.observe(viewLifecycleOwner) {
            if (it) {
                try {
                    val notification: Uri =
                        RingtoneManager.getDefaultUri(RingtoneManager.TYPE_NOTIFICATION)
                    val r = RingtoneManager.getRingtone(requireContext().applicationContext, notification)
                    r.play()
                } catch (e: Exception) {
                    e.printStackTrace()
                }
            }
        }
    }

    override fun onShowLockdownView(view: View) {}

    override fun onHideLockdownView(view: View) {}

}