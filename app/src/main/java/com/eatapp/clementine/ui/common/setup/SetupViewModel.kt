package com.eatapp.clementine.ui.common.setup

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.eatapp.clementine.data.repository.FabricateRepository
import com.eatapp.clementine.internal.asLiveData
import com.eatapp.clementine.internal.managers.AnalyticsManager
import com.eatapp.clementine.internal.managers.DataManager
import com.eatapp.clementine.internal.managers.EatManager
import com.eatapp.clementine.internal.managers.FeatureFlagsManager
import com.eatapp.clementine.internal.managers.ShiftManager
import com.eatapp.clementine.ui.launch.FabricateViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject
import kotlin.concurrent.fixedRateTimer

@HiltViewModel
class SetupViewModel @Inject constructor(
    private val eatManager: EatManager,
    private val shiftManager: ShiftManager,
    private val dataManager: DataManager,
    private val analyticsManager: AnalyticsManager,
    private val fabricateRepository: FabricateRepository,
    private val featuresFlagsManager: FeatureFlagsManager
) : FabricateViewModel(
    eatManager,
    shiftManager,
    dataManager,
    analyticsManager,
    fabricateRepository
) {

    private val _onboardingCompleted = MutableLiveData<Boolean>()
    val onboardingCompleted: LiveData<Boolean> by lazy {
        _onboardingCompleted.asLiveData()
    }

    init {

        fixedRateTimer("default", false, 10000L, 10000L) {
            fetchPizzaSlicer()
        }

        _onboardingCompleted.postValue(false)
    }

    private fun fetchPizzaSlicer() {

        launch({

            loading(true)

            val response = fabricateRepository.pizzaSlicer()

            if (response.pizzaSlicer.onboardingFlowComplete || !featuresFlagsManager.onboardingQuickSetupEnabled) {

                rooms()
                shifts()

                _onboardingCompleted.postValue(true)
            }

        }, false)
    }

    override fun checkCompletion() {}
}