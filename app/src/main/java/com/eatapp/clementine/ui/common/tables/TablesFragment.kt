package com.eatapp.clementine.ui.common.tables

import android.content.Intent
import android.view.View
import androidx.core.view.doOnLayout
import androidx.recyclerview.widget.LinearLayoutManager
import com.eatapp.clementine.adapter.SelectorAdapter
import com.eatapp.clementine.data.network.response.closing.Closing
import com.eatapp.clementine.data.network.response.reservation.Reservation
import com.eatapp.clementine.data.network.response.room.Room
import com.eatapp.clementine.data.network.response.room.TableType
import com.eatapp.clementine.databinding.TablesFragmentBinding
import com.eatapp.clementine.internal.Constants
import com.eatapp.clementine.internal.managers.fieldOptionError
import com.eatapp.clementine.internal.parcelableArrayList
import com.eatapp.clementine.internal.visibleOrGone
import com.eatapp.clementine.ui.base.BaseFragment
import com.eatapp.clementine.ui.common.tables.conflict.ConflictItem
import com.eatapp.clementine.ui.common.tables.conflict.ConflictsRecyclerViewAdapter
import com.eatapp.clementine.ui.reservation.ReservationActivity
import com.eatapp.clementine.views.LoadingButton
import com.eatapp.clementine.views.TableState
import com.google.android.material.tabs.TabLayout
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class TablesFragment : BaseFragment<TablesViewModel, TablesFragmentBinding>() {

    lateinit var adapter: SelectorAdapter
    private lateinit var selectedTablesAdapter: SelectedTablesRecyclerViewAdapter
    private lateinit var conflictsAdapter: ConflictsRecyclerViewAdapter

    override fun inflateLayout() = TablesFragmentBinding.inflate(layoutInflater)

    override fun viewModelClass() = TablesViewModel::class.java

    override fun onShowLockdownView(view: View) {}

    override fun onHideLockdownView(view: View) {}

    override fun viewCreated() {

        activity?.intent?.parcelableArrayList<Closing>(Constants.CLOSINGS_EXTRA)?.let { cls ->
            vm.closings = cls.toList()
        }

        vm.reservations = TablesReservationDataHolder.reservations

        activity?.intent?.extras?.getParcelable<Reservation>(Constants.RESERVATION_EXTRA)?.let { res ->
            vm.reservation(res)
        }

        activity?.intent?.extras?.getString(Constants.RESTAURANT_ID_EXTRA)?.let {
            vm.restaurantId(it)
        }

        bindUI()
        observe()
    }

    fun bindUI() {

        vm.rooms()?.forEach { room ->
            binding.bottomSheetSelectedTables.tabLayout.addTab(binding.bottomSheetSelectedTables.tabLayout.newTab().setText(room.name).setTag(room))
        }

        binding.applyBtn.setOnClickListener {
            saveSelection()
        }

        vm.reloadTableStates(vm.room())

        binding.bottomSheetSelectedTables.tabLayout.addOnTabSelectedListener(object : TabLayout.OnTabSelectedListener{
            override fun onTabSelected(tab: TabLayout.Tab?) {
                vm.selectedRoom = (tab?.tag as Room?)!!.id
                vm.reloadTableStates(tab?.tag as Room?)
                loadTables()
            }
            override fun onTabUnselected(tab: TabLayout.Tab?) {}
            override fun onTabReselected(tab: TabLayout.Tab?) {}
        })

        loadTablesSelectorRecyclerView()

        loadSelectedTablesRecyclerView()

        loadConflictsRecyclerView()

        binding.roomView.listener = { state ->

            if (state.table.type == TableType.Table) {

                state.isSelected = state.isSelected.not()
                state.updateConfig()

                vm.updateSelectedTables(state)

                loadTables()
            }
        }

        binding.roomView.doOnLayout {
            loadTables()
        }
    }

    private fun loadConflictsRecyclerView() {
        conflictsAdapter = ConflictsRecyclerViewAdapter(true) {
            vm.removeConflict(it)
        }
        conflictsAdapter.submitList(emptyList())
        binding.recyclerConflicts.adapter = conflictsAdapter
    }

    private fun loadTablesSelectorRecyclerView() {
        adapter = SelectorAdapter(false)
        adapter.selectionListener = {
            (it.value as TableState).isSelected = it.isSelected
            (it.value as TableState).updateConfig()

            vm.updateSelectedTables(it.value as TableState)

            loadTables()
        }
        adapter.submitList(vm.tablesList())
        binding.bottomSheetSelectedTables.list.adapter = adapter
    }

    private fun loadSelectedTablesRecyclerView() {
        val layoutManager =
            LinearLayoutManager(requireContext(), LinearLayoutManager.HORIZONTAL, false)
        binding.bottomSheetSelectedTables.recyclerSelectedTables.layoutManager = layoutManager

        selectedTablesAdapter = SelectedTablesRecyclerViewAdapter {

            val tableInCurrentRoom =
                vm.tableStates().find { tableState -> tableState.table.id == it.id }

            if (tableInCurrentRoom != null) {
                tableInCurrentRoom.isSelected = false
                tableInCurrentRoom.updateConfig()
                vm.updateSelectedTables(tableInCurrentRoom)
                loadTables()
            } else {
                vm.removeSelectedTable(it)
                loadTables()
            }
        }
        selectedTablesAdapter.submitList(vm.selectedTables)

        binding.bottomSheetSelectedTables.recyclerSelectedTables.adapter = selectedTablesAdapter
    }

    private fun saveSelection() {
        val intent = Intent()
        intent.putParcelableArrayListExtra(Constants.TABLES_EXTRA, ArrayList(vm.selectedTables))
        activity?.setResult(Constants.TABLES_RESULT, intent)
        activity?.finish()
    }

    private fun loadTables() {
        activity?.runOnUiThread {
            binding.bottomSheetSelectedTables.recyclerSelectedTables.visibleOrGone = vm.selectedTables.isNotEmpty()
            binding.bottomSheetSelectedTables.labelNoTables.visibleOrGone = vm.selectedTables.isEmpty()
        }

        adapter.submitList(vm.tablesList())
        selectedTablesAdapter.submitList(vm.selectedTables.toMutableList())
        binding.roomView.loadFloor(vm.tableStates(), vm.room()?.backgroundImageId)
        vm.checkConflicts()
    }

    private fun observe() {
        vm.conflictList.observe(viewLifecycleOwner) {
            conflictsAdapter.submitList(it.toMutableList())
            updateButtonState(it)
        }
    }

    private fun updateButtonState(conflictItems: List<ConflictItem>) {
        if (conflictItems.firstOrNull {
                it.permission?.value == fieldOptionError
            } == null) binding.applyBtn.state = LoadingButton.LoadingButtonState.Available
        else binding.applyBtn.state = LoadingButton.LoadingButtonState.Disabled
    }
}

