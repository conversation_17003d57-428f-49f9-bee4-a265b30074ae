package com.eatapp.clementine.ui.common.tables.conflict

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.eatapp.clementine.databinding.ListItemConflictBinding
import com.eatapp.clementine.internal.managers.OptionType
import com.eatapp.clementine.internal.visibleOrGone

typealias RemoveConflictItemListener = (ConflictItem) -> Unit

class ConflictsRecyclerViewAdapter(
    val showCloseIcon: <PERSON>olean,
    val listener: RemoveConflictItemListener?
) :
    ListAdapter<ConflictItem, ConflictsRecyclerViewAdapter.ConflictViewHolder>(ConflictDiffCallback()) {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ConflictViewHolder {
        return ConflictViewHolder(
            ListItemConflictBinding.inflate(
                LayoutInflater.from(parent.context), parent, false
            )
        )
    }

    override fun onBindViewHolder(holder: ConflictViewHolder, position: Int) {
        val item = getItem(position)
        holder.bind(item, listener)
    }

    inner class ConflictViewHolder(val binding: ListItemConflictBinding) :
        RecyclerView.ViewHolder(binding.root) {
        fun bind(item: ConflictItem, listener: RemoveConflictItemListener?) = with(binding) {
            permission = item.permission
            iconClose.visibleOrGone = showCloseIcon && item.permission?.value == OptionType.Warning.type
            iconClose.setOnClickListener {
                listener?.invoke(item)
            }
            executePendingBindings()
        }
    }
}

private class ConflictDiffCallback : DiffUtil.ItemCallback<ConflictItem>() {

    override fun areItemsTheSame(oldItem: ConflictItem, newItem: ConflictItem): Boolean {
        return oldItem.permission?.id == newItem.permission?.id
    }

    override fun areContentsTheSame(oldItem: ConflictItem, newItem: ConflictItem): Boolean {
        return oldItem == newItem
    }
}