package com.eatapp.clementine.ui.common.templates

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.eatapp.clementine.data.network.response.message.ChannelType
import com.eatapp.clementine.data.network.response.reservation.Reservation
import com.eatapp.clementine.internal.Constants.GUEST_CUSTOM
import com.eatapp.clementine.internal.Status
import com.eatapp.clementine.internal.managers.EatManager
import com.eatapp.clementine.ui.base.BaseViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject

@HiltViewModel
class TemplatesViewModel @Inject constructor(
    val eatManager: EatManager
) : BaseViewModel() {

    private val _templates = MutableLiveData<List<Any>>()
    val templates: LiveData<List<Any>> = _templates

    fun createTemplates(
        reservation: Reservation,
        channel: ChannelType,
        messagesTypes: Array<String>?
    ) {
        val templates = if (channel == ChannelType.WHATSAPP)
            eatManager.whatsappTemplates() else
                eatManager.messageTemplates()

        if (templates?.isNotEmpty() == true) {

            val result = mutableListOf<Any>()

            // Filter templates by channel type, status and group by message type
            val filteredTemplates = templates.filter { template ->
                template.channel == channel && when (reservation.status) {
                    Status.Value.WAITLIST.code -> {
                        template.messageType  in listOf(
                            "wait_list_table_ready",
                            GUEST_CUSTOM
                        )
                    }
                    else -> {
                        template.messageType in listOf(
                            "reservation_confirmed",
                            "reservation_requested", 
                            "reservation_late",
                            "reservation_denied",
                            "reservation_canceled",
                            "reservation_no_show",
                            "reservation_finished",
                            GUEST_CUSTOM
                        )
                    }
                }
            }

            filteredTemplates.forEach {
                it.disabled = messagesTypes?.contains(it.messageType) == true
                        && it.messageType != GUEST_CUSTOM
            }

            val groupedTemplates = filteredTemplates
                .groupBy { it.messageType }
                .toSortedMap(compareByDescending { it })

            // Add each group with a header
            groupedTemplates.forEach { (messageType, templatesInGroup) ->
                // Add header for this message type
                messageType.let { type ->
                    result.add(type.replace("reservation_", "")
                        .replace("wait_list_", "")
                        .replace("_", " ")
                        .replaceFirstChar { it.uppercase() })
                    result.addAll(templatesInGroup)
                }
            }

            _templates.postValue(result)

        } else {

            _templates.postValue(emptyList())
        }
    }
} 