package com.eatapp.clementine.ui.common.voucher

import android.os.Bundle
import com.eatapp.clementine.R
import com.eatapp.clementine.databinding.RedeemVoucherActivityBinding
import com.eatapp.clementine.ui.base.BaseActivity
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class RedeemVoucherActivity : BaseActivity<RedeemVoucherActivityBinding>() {

    override fun getLayoutId() = R.layout.redeem_voucher_activity

    override fun onCreated(savedInstanceState: Bundle?) {
        if (savedInstanceState == null) {
            supportFragmentManager.beginTransaction()
                .replace(
                    R.id.container,
                    RedeemVoucherFragment()
                )
                .commitNow()
        }

        binding.backBtnRS.setOnClickListener {
            finish()
        }
    }
}
