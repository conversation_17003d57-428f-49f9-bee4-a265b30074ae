package com.eatapp.clementine.ui.common.voucher

import android.view.View
import com.eatapp.clementine.databinding.RedeemVoucherFragmentBinding
import com.eatapp.clementine.internal.showErrorAlert
import com.eatapp.clementine.ui.base.BaseFragment
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class RedeemVoucherFragment : BaseFragment<RedeemVoucherViewModel, RedeemVoucherFragmentBinding>() {

    override fun inflateLayout() = RedeemVoucherFragmentBinding.inflate(layoutInflater)

    override fun viewModelClass() = RedeemVoucherViewModel::class.java

    override fun viewCreated() {
        vm.alert.observe(viewLifecycleOwner) {
            requireContext().showErrorAlert(it.first, it.second)
        }

        vm.loading.observe(viewLifecycleOwner) {
            when (it) {
                true -> binding.redeemBtn.showLoading()
                else -> binding.redeemBtn.hideLoading()
            }
        }
    }

    override fun onShowLockdownView(view: View) {}

    override fun onHideLockdownView(view: View) {}
}
