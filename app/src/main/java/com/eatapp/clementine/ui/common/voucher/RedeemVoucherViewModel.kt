package com.eatapp.clementine.ui.common.voucher

import androidx.lifecycle.MutableLiveData
import com.eatapp.clementine.data.network.body.ReservationBody
import com.eatapp.clementine.data.network.response.guest.Guest
import com.eatapp.clementine.data.network.response.payment.Payment
import com.eatapp.clementine.data.network.response.reservation.Reservation
import com.eatapp.clementine.data.repository.ReservationsRepository
import com.eatapp.clementine.internal.formatNonLocal
import com.eatapp.clementine.internal.managers.EatManager
import com.eatapp.clementine.ui.base.BaseViewModel
import com.google.gson.Gson
import com.google.gson.internal.LinkedTreeMap
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject

@Suppress("UNCHECKED_CAST")
@HiltViewModel
class RedeemVoucherViewModel @Inject constructor(
    private val reservationsRepository: ReservationsRepository,
    private val eatManager: EatManager
) : BaseViewModel() {

    var code = MutableLiveData<String>()
    var alert = MutableLiveData<Pair<String, String>>()

    init {
        code.value = ""
    }

    fun redeemVoucher() {

        if (code.value.toString().length != 6) {
            alert.postValue(Pair("Invalid Voucher ID", "Please enter a 6 digit voucher code"))
            loading(false)
            return
        }

        launch({

            loading(true)

            val response = reservationsRepository.reservation(eatManager.restaurantId(), code.value.toString())

            val payments: MutableList<Payment> = mutableListOf()

            response.included.forEach { obj ->

                if ((obj as LinkedTreeMap<String, String>)["type"] == "guest") {

                    val guest: Guest? = Gson().fromJson(Gson().toJson(obj), Guest::class.java)
                    if (response.reservation.relationships?.guest?.data?.id == guest?.id) {
                        response.reservation.guest = guest
                    }
                }

                if ((obj)["type"] == "payment") {

                    val payment: Payment? = Gson().fromJson(Gson().toJson(obj), Payment::class.java)

                    response.reservation.relationships?.payments?.data?.find {
                        it.id == payment?.id
                    }.let {
                        payment?.let { pymt ->
                            payments.add(pymt)
                        }
                    }

                    response.reservation.payments = payments
                }
            }

            checkVoucherStatus(response.reservation)

        }, false)
    }

    private fun checkVoucherStatus(voucher: Reservation) {

        if (voucher.attributes.customTags.contains("voucher_redeemed")) {
            alert.postValue(Pair("Error", "This voucher is already redeemed"))
            loading(false)
            return
        }

        if (voucher.payments.find { it.status == "captured" } == null) {
            alert.postValue(Pair("Error", "This voucher is not purchased"))
            loading(false)
            return
        }

        launch({

            reservationsRepository.updateReservation(
                eatManager.restaurantId(),
                voucher.id,
                reservationBody(voucher))

            alert.postValue(Pair("Success", "Voucher " + voucher.key + " has been redeemed"))
            loading(false)

        }, false)
    }

    private fun reservationBody(reservation: Reservation): ReservationBody {

        return ReservationBody(
            reservation.covers,
            reservation.createdBy,
            reservation.customTags.plus("voucher_redeemed"),
            listOf(),
            listOf(),
            reservation.duration,
            reservation.guest?.id,
            reservation.tempName,
            reservation.notes,
            formatNonLocal().format(reservation.startTime).toString(),
            reservation.status,
            reservation.tables?.map { it.id },
            reservation.walkIn,
            reservation.waitQuote,
            reservation.waitlistQueuedAt,
            reservation.comments,
            false,
            reservation.attributes.customFields,
            reservation.attributes.editedBy,
            reservation.attributes.source
        )
    }
}