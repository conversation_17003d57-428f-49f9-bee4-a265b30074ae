package com.eatapp.clementine.ui.guest

import android.view.View
import androidx.viewpager2.widget.ViewPager2
import com.eatapp.clementine.R
import com.eatapp.clementine.adapter.EatPagerAdapter
import com.eatapp.clementine.data.network.response.guest.Guest
import com.eatapp.clementine.databinding.GuestFragmentBinding
import com.eatapp.clementine.internal.Constants
import com.eatapp.clementine.internal.visible
import com.eatapp.clementine.ui.base.BaseFragment
import com.google.android.material.tabs.TabLayoutMediator
import dagger.hilt.android.AndroidEntryPoint


@AndroidEntryPoint
class GuestFragment : BaseFragment<GuestViewModel, GuestFragmentBinding>() {

    private var simplifiedMode: Boolean = false
    private var firstName: String? = null
    private var lastName: String? = null
    private var phoneNumber: String? = null
    private var email: String? = null
    private var newFlow = false
    private var reservationId: String? = null

    private lateinit var adapter: EatPagerAdapter

    override fun inflateLayout() = GuestFragmentBinding.inflate(layoutInflater)

    override fun viewModelClass() = GuestViewModel::class.java

    override fun onShowLockdownView(view: View) {}

    override fun onHideLockdownView(view: View) {}

    override fun viewCreated() {
        activity?.intent?.extras?.let {
            try {
                it.getParcelable<Guest>(Constants.GUEST_EXTRA)?.let { g -> vm.guest.value = g }
                it.getBoolean(Constants.GUEST_SIMPLIFIED_MODE_EXTRA).let { s -> simplifiedMode = s }
                it.getBoolean(Constants.GUEST_CREATE_MODE_EXTRA).let { s -> vm.createMode.value = s }
                it.getString(Constants.GUEST_EXTRA_PHONE_NUMBER).let { s -> phoneNumber = s }
                it.getString(Constants.GUEST_EXTRA_FIRST_NAME).let { s -> firstName = s }
                it.getString(Constants.GUEST_EXTRA_LAST_NAME).let { s -> lastName = s }
                it.getString(Constants.GUEST_EXTRA_EMAIL).let { s -> email = s }
                it.getBoolean(Constants.GUEST_EXTRA_NEW_FLOW).let { b -> newFlow = b }
                reservationId = it.getString(Constants.GUEST_EXTRA_RESERVATION_ID)
            } catch (_: Exception) {
            }
        }

        bindUI()
    }

    private fun bindUI() {

        adapter = EatPagerAdapter(requireActivity())

        adapter.addFragment(
            GuestProfileFragment.newInstance(
                vm.guest.value,
                simplifiedMode,
                firstName,
                lastName,
                phoneNumber,
                email,
                reservationId,
                newFlow
            ), resources.getString(R.string.profile))

        if (vm.guest.value != null) {
            adapter.addFragment(
                GuestHistoryFragment.newInstance(vm.guest.value),
                resources.getString(R.string.guest_history)
            )
        }

        binding.viewPagerGF.adapter = adapter

        when (vm.guest.value) {
            null -> binding.tabLayoutGF.visibility = View.GONE
            else -> {
                TabLayoutMediator(binding.tabLayoutGF, binding.viewPagerGF) { tab, position ->
                    tab.text = adapter.getPageTitle(position)
                }.attach()
            }
        }
    }

    fun guest(): Pair<Int, Guest?> {
        val profileFr = adapter.fragmentList[0] as GuestProfileFragment
        return profileFr.guest()
    }

    fun updateGuest(guest: Guest) {
        vm.guest.value = guest

        binding.tabLayoutGF.visible = true
        TabLayoutMediator(binding.tabLayoutGF, binding.viewPagerGF) { tab, position ->
            tab.text = adapter.getPageTitle(position)
        }.attach()

        val guestProfileFragment = adapter.getItem(0) as? GuestProfileFragment
        guestProfileFragment?.updateGuest(guest)

        val guestHistoryFragment = adapter.getItem(1) as? GuestHistoryFragment

        if (guestHistoryFragment != null) {
            guestHistoryFragment.updateGuest(guest)
        } else {
            adapter.addFragment(
                GuestHistoryFragment.newInstance(guest),
                resources.getString(R.string.guest_history)
            )
            adapter.notifyItemInserted(1)
        }
    }
}
