package com.eatapp.clementine.ui.guest

import SingleLiveEvent
import androidx.lifecycle.MediatorLiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.eatapp.clementine.GuestVouchersHandler
import com.eatapp.clementine.VoucherUpdateState
import com.eatapp.clementine.VouchersHandler
import com.eatapp.clementine.data.network.response.custom_fields.CustomField
import com.eatapp.clementine.data.network.response.custom_fields.CustomFieldAttributes
import com.eatapp.clementine.data.network.response.custom_fields.CustomFieldComponent
import com.eatapp.clementine.data.network.response.guest.Guest
import com.eatapp.clementine.data.network.response.restaurant.AccountStateType
import com.eatapp.clementine.data.network.response.tag.Tag
import com.eatapp.clementine.data.network.response.tagging.Tagging
import com.eatapp.clementine.data.network.response.tagging.TaggingAttributes
import com.eatapp.clementine.data.network.response.user.Permission
import com.eatapp.clementine.data.network.response.vouchers.Voucher
import com.eatapp.clementine.data.network.response.vouchers.VoucherAssignment
import com.eatapp.clementine.data.network.response.vouchers.VoucherType
import com.eatapp.clementine.data.repository.GuestsRepository
import com.eatapp.clementine.internal.EatException
import com.eatapp.clementine.internal.ItemsGroup
import com.eatapp.clementine.internal.SelectorItem
import com.eatapp.clementine.internal.customFieldType
import com.eatapp.clementine.internal.labelized
import com.eatapp.clementine.internal.managers.AnalyticsManager
import com.eatapp.clementine.internal.managers.EatManager
import com.eatapp.clementine.internal.managers.TagType
import com.eatapp.clementine.internal.managers.manageGuests
import com.eatapp.clementine.internal.managers.manageReservations
import com.eatapp.clementine.ui.base.BaseViewModel
import com.eatapp.clementine.ui.reservation.ReservationDetailsViewModel
import com.eatapp.clementine.views.StepperView
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.launch
import org.json.JSONObject
import java.util.Calendar
import java.util.Date
import javax.inject.Inject

@HiltViewModel
class GuestProfileViewModel @Inject constructor(
    private val eatManager: EatManager,
    val analyticsManager: AnalyticsManager,
    private val guestsRepository: GuestsRepository,
    @GuestVouchersHandler
    private val vouchersHandler: VouchersHandler
) : BaseViewModel() {

    enum class GuestStatus {
        None,
        Updated,
        Added,
        Deleted
    }

    val isFreemium = eatManager.accountState() == AccountStateType.IN_HOUSE
    val isMarketingVisible = eatManager.restaurant()?.marketingOptInVisibility ?: false

    var currency: String? = eatManager.restaurant()?.currency ?: "AED"
    val posActive: Boolean = eatManager.restaurant()?.posActive == true
    val loyaltyActive: Boolean = eatManager.restaurant()?.loyaltyEnabled == true

    var permissionReservation: Permission = eatManager.permission(identifier = manageReservations)

    val permissionGuest = MediatorLiveData(eatManager.permission(identifier = manageGuests))

    var guest = MutableLiveData<Guest>()
    var guestStatus = GuestStatus.None

    var tagsAvailable: Boolean = eatManager.tagSelectors(null, TagType.Guest).size > 0

    var update = SingleLiveEvent<Boolean>()
    var create = SingleLiveEvent<Boolean>()
    var deleteCompleted = SingleLiveEvent<Boolean>()
    var deleteInProgress = SingleLiveEvent<Boolean>()

    private val _voucherState = MutableStateFlow<VoucherUpdateState<Guest>>(VoucherUpdateState.Idle)
    val voucherState: StateFlow<VoucherUpdateState<Guest>> = _voucherState

    var call = MutableLiveData<String>()

    var simplifiedMode = MutableLiveData<Boolean>()
    var createMode = MutableLiveData<Boolean>()

    private var taggings: MutableList<Tagging>? = mutableListOf()

    private var reservationId: String? = null

    init {
        permissionGuest.addSource(guest) {
            permissionGuest.value = eatManager.permission(identifier = manageGuests)
        }
    }

    fun setGuest(g: Guest) {
        taggings = g.taggings
        guest.value = g
    }

    fun setCreateMode() {
        createMode.value = true
        val g = Guest()
        g.marketingAccepted = eatManager.restaurant()?.marketingOptInDefaultValue ?: false
        guest.value = g
    }

    fun onUpdateGuest() {

        if (validate()) return

        analyticsManager.trackEditGuestProfile(JSONObject())

        launch({

            update.postValue(true)

            val response = guestsRepository.updateGuest(
                guest.value?.id!!, guest.value!!.toGuestBody(
                    eatManager.addedTags(taggings, guest.value?.taggings),
                    eatManager.removedTags(taggings, guest.value?.taggings)
                )
            )

            guest.postValue(response.guest)
            guestStatus = GuestStatus.Updated

            update.postValue(false)

        }, false)
    }

    fun onCreateGuest() {

        if (validate()) return

        launch({

            create.postValue(true)

            val response = guestsRepository.createGuest(
                guest.value!!.toGuestBody(
                    eatManager.addedTags(taggings, guest.value?.taggings),
                    eatManager.removedTags(taggings, guest.value?.taggings)
                )
            )

            guest.postValue(response.guest)
            guestStatus = GuestStatus.Added

            create.postValue(false)

        }, false)
    }

    fun onDeleteGuest() {

        analyticsManager.trackDeleteGuestProfile()

        viewModelScope.launch {
            deleteInProgress.postValue(true)

            try {
                guestsRepository.deleteGuest(guest.value?.id!!)
                guestStatus = GuestStatus.Deleted
                deleteCompleted.postValue(true)
            } catch (e: Exception) {
                setError(EatException(e, false))
            } finally {
                deleteInProgress.postValue(false)
            }
        }
    }

    fun onPhoneGuest() {
        call.postValue(guest.value?.phone)
    }

    fun updateDate(date: GuestProfileFragment.Dates, year: Int, month: Int, day: Int) {

        val c = Calendar.getInstance()

        when (date) {
            GuestProfileFragment.Dates.BIRTHDAY -> c.time = guest.value?.birthday ?: Date()
            GuestProfileFragment.Dates.ANNIVERSARY -> c.time = guest.value?.anniversary ?: Date()
        }

        c.set(Calendar.YEAR, year)
        c.set(Calendar.MONTH, month)
        c.set(Calendar.DAY_OF_MONTH, day)

        when (date) {
            GuestProfileFragment.Dates.BIRTHDAY -> guest.value?.birthday = c.time
            GuestProfileFragment.Dates.ANNIVERSARY -> guest.value?.anniversary = c.time
        }
    }

    fun phoneNumber(s: String) {
        guest.value?.phone = s
    }

    fun firstName(s: String) {
        guest.value?.firstName = s
    }

    fun lastName(s: String) {
        guest.value?.lastName = s
    }

    fun email(email: String) {
        guest.value?.email = email
    }

    fun updateNote(note: String?) {
        guest.value?.notes = note
    }

    fun taggingsList(): MutableList<SelectorItem>? {

        return eatManager.taggingSelectors(
            addItem = tagsList().size > 0,
            taggings = taggings
        )
    }

    fun tagsList(): MutableList<ItemsGroup> {

        return eatManager.tagSelectors(
            taggings = taggings,
            tagType = TagType.Guest
        )
    }

    fun removeTag(tag: SelectorItem) {

        val list = taggings?.toMutableList()

        list?.remove(taggings!!
            .find { it.name == tag.name })

        taggings = list
    }

    fun updateTags(list: MutableList<ItemsGroup>?) {

        taggings = mutableListOf()

        list?.forEach { group ->
            taggings?.addAll(group.items.filter { it.isSelected }.map { item ->
                val tag = item.value as Tag
                Tagging(
                    TaggingAttributes(tag.category?.name, tag.icon, tag.name, ""),
                    tag.id,
                    "",
                    tag.category?.color
                )
            }.toMutableList())
        }
    }

    fun customFields(): List<CustomField> {
        val customFields = eatManager.customFields()?.filter {
            it.attributes.component == CustomFieldComponent.GUEST
        }?.toMutableList() ?: mutableListOf()

        customFields.forEach {
            it.value = guest.value?.attributes?.customFields?.get(it.attributes.name)
        }

        guest.value?.attributes?.customFields?.filterNot { customField ->
            customField.key in customFields.map { it.attributes.name }
        }?.let { deletedFields ->
            customFields.addAll(deletedFields.map {
                CustomField(
                    it.key,
                    CustomFieldAttributes(
                        CustomFieldComponent.GUEST,
                        it.key,
                        "",
                        it.key.labelized,
                        it.value.customFieldType,
                        it.value as? List<String>
                    ),
                    deleted = true,
                    it.value
                )
            })
        }

        return customFields
    }

    fun updateCustomFields(field: Pair<String, Any>) {
        guest.value?.let { guest ->
            when (val fieldValue = field.second) {
                is Boolean -> {
                    if (fieldValue) {
                        guest.attributes?.customFields?.set(field.first, fieldValue)
                    } else {
                        guest.attributes?.customFields?.remove(field.first)
                    }
                }

                is String -> {
                    if (fieldValue.isNotEmpty()) {
                        guest.attributes?.customFields?.set(field.first, fieldValue)
                    } else {
                        guest.attributes?.customFields?.remove(field.first)
                    }
                }

                is Int -> {
                    updateCustomFieldCount(fieldValue, field.first)
                }

                is ArrayList<*> -> {
                    updateCustomFieldMulti(fieldValue as ArrayList<String>, field.first)
                }

                else -> {}
            }
        }
    }

    fun updateCustomFields(customFieldName: String, stepper: StepperView.Step) {
        guest.value?.let { reservation ->

            var count = reservation.attributes?.customFields?.get(customFieldName) as? Int ?: 0
            when (stepper) {
                StepperView.Step.PLUS -> count++
                StepperView.Step.MINUS -> count--
            }

            if (count < 0 || count > ReservationDetailsViewModel.MAX_COVERS) return

            updateCustomFieldCount(count, customFieldName)
        }
    }

    fun vouchers(): List<Voucher> {
        return eatManager.vouchers() ?: emptyList()
    }

    fun vouchersEnabled(): Boolean {
        return eatManager.restaurant()?.vouchersEnabled == true
    }

    fun guestVouchers(): List<Voucher> {
        val vouchers = eatManager.vouchers()
        return vouchers?.filter { voucher ->
            val context = voucher.attributes.context ?: emptyList()
            context.contains(VoucherType.GUEST)
        } ?: emptyList()
    }

    fun updateVoucherAssignments(vouchers: List<Voucher>) {
        viewModelScope.launch {
            vouchersHandler
                .updateVoucherAssignmentsFlow<Guest>(guest.value!!.id, editedBy = null, vouchers)
                .collect {
                    handleVoucherResponse(it)
                    _voucherState.value = it
                }
        }
    }

    fun redeemVoucher(assignment: VoucherAssignment) {
        viewModelScope.launch {
            vouchersHandler.redeemVoucherFlow<Guest>(
                guest.value!!.id,
                reservationId,
                editedBy = null,
                assignment
            ).collect {
                handleVoucherResponse(it)
                _voucherState.value = it
            }
        }
    }

    private fun handleVoucherResponse(it: VoucherUpdateState<Guest>) {
        if (it is VoucherUpdateState.Error) {
            setError(it.exception)
        } else if (it is VoucherUpdateState.Success) {
            setGuest(it.data)
        }
    }

    fun updateReservationId(reservationId: String?) {
        this.reservationId = reservationId
    }

    private fun updateCustomFieldCount(count: Int, customFieldName: String) {
        if (count == 0) {
            guest.value?.attributes?.customFields?.remove(customFieldName)
        } else {
            guest.value?.attributes?.customFields?.set(customFieldName, count)
        }
    }

    private fun updateCustomFieldMulti(values: ArrayList<String>, customFieldName: String) {
        if (values.isEmpty()) {
            guest.value?.attributes?.customFields?.remove(customFieldName)
        } else {
            guest.value?.attributes?.customFields?.set(customFieldName, values)
        }
    }

    private fun validate(): Boolean {

        return if (guest.value?.firstName.isNullOrBlank()) {

            setError(
                EatException("Validation", "First name can not be empty")
            )
            true

        } else false
    }

    fun counterList(customFieldName: String): MutableList<SelectorItem>? {
        val times = mutableListOf<SelectorItem>()

        for (i in 1..ReservationDetailsViewModel.MAX_COVERS) {

            val isSelected =
                i == guest.value?.attributes?.customFields?.get(customFieldName) as? Int

            val item = SelectorItem(
                "", i.toString(), i,
                isSelected = isSelected,
                isHeader = false,
                isDisabled = false
            )
            times.add(item)
        }

        return times
    }
}