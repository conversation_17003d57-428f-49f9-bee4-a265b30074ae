package com.eatapp.clementine.ui.guest

import androidx.lifecycle.MediatorLiveData
import androidx.lifecycle.MutableLiveData
import com.eatapp.clementine.data.network.response.guest.Guest
import com.eatapp.clementine.internal.managers.EatManager
import com.eatapp.clementine.internal.managers.manageGuests
import com.eatapp.clementine.ui.base.BaseViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject

@HiltViewModel
class GuestViewModel @Inject constructor(
    eatManager: EatManager
) : BaseViewModel() {

    var permission = MediatorLiveData(eatManager.permission(identifier = manageGuests))

    val guest = MutableLiveData<Guest>()
    val createMode = MutableLiveData<Boolean>()

    init {
        permission.addSource(guest) {
            permission.value = eatManager.permission(identifier =  manageGuests)
        }
    }
}
