package com.eatapp.clementine.ui.home.overflow

import android.os.Bundle
import android.view.View
import androidx.lifecycle.LifecycleOwner
import com.eatapp.clementine.R
import com.eatapp.clementine.adapter.ClosingsAdapter
import com.eatapp.clementine.databinding.BlockHoursFragmentBinding
import com.eatapp.clementine.internal.Constants
import com.eatapp.clementine.internal.showConfirmationAlert
import com.eatapp.clementine.internal.showErrorAlert
import com.eatapp.clementine.ui.base.BaseFragment
import dagger.hilt.android.AndroidEntryPoint
import java.util.Date

@AndroidEntryPoint
class BlockHoursFragment : BaseFragment<BlockHoursViewModel, BlockHoursFragmentBinding>() {

    lateinit var adapter: ClosingsAdapter

    enum class ClosingTime {
        START,
        END
    }

    companion object {
        fun newInstance(date: Date) = BlockHoursFragment().apply {
            arguments = Bundle().apply {
                putSerializable(Constants.DATE, date)
            }
        }
    }

    override fun inflateLayout() = BlockHoursFragmentBinding.inflate(layoutInflater)

    override fun viewModelClass() = BlockHoursViewModel::class.java

    override fun onShowLockdownView(view: View) {}

    override fun onHideLockdownView(view: View) {}

    override fun viewCreated() {
        arguments?.getSerializable(Constants.DATE)?.let { date -> vm.date(date as Date) }

        observe()
        bindUI()
    }

    fun observe() {

        vm.closings.observe(viewLifecycleOwner, androidx.lifecycle.Observer {
            adapter.submitList(it)
        })

        vm.create.observe(viewLifecycleOwner, androidx.lifecycle.Observer {

            when (it) {
                true -> binding.createBtn.showLoading()
                else -> {
                    binding.createBtn.hideLoading()
                }
            }
        })

        vm.invalidTimes.observe(
            this@BlockHoursFragment as LifecycleOwner
        ) {
            if (it) {
                requireContext().showErrorAlert(
                    resources.getString(R.string.invalid_times_title),
                    resources.getString(R.string.invalid_times_desc)
                )
            }
        }

        vm.loading.observe(viewLifecycleOwner) {
            if (!it) {
                binding.createBtn.hideLoading()
            }
        }
    }

    fun bindUI() {

        adapter = ClosingsAdapter(vm.date, listOf(), vm.permission) { closing, position ->

            requireContext().showConfirmationAlert(R.string.delete_closing_title, R.string.delete_closing_desc) {
                adapter.notifyItemChanged(position)
                vm.deleteClosing(closing)
            }
        }

        binding.closingsList.adapter = adapter

        binding.startBlockTime.setOnClickListener {
            showTimesList(ClosingTime.START)
        }

        binding.startBlockTime.listener = {
            vm.updateTime(ClosingTime.START, it)
        }

        binding.endBlockTime.setOnClickListener {
            showTimesList(ClosingTime.END)
        }

        binding.endBlockTime.listener = {
            vm.updateTime(ClosingTime.END, it)
        }
    }

    private fun showTimesList(time: ClosingTime) {

        val list = vm.timesList(time)
        bottomSheetDialog(list, "", true) {
            vm.updateTime(time, list?.first { it.isSelected }?.value as Date)
        }
    }
}