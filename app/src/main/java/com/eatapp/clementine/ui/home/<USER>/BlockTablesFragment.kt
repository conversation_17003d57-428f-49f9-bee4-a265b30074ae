package com.eatapp.clementine.ui.home.overflow

import android.app.DatePickerDialog
import android.os.Bundle
import android.view.View
import com.eatapp.clementine.R
import com.eatapp.clementine.adapter.ClosingsAdapter
import com.eatapp.clementine.adapter.TagAdapter
import com.eatapp.clementine.databinding.BlockTablesFragmentBinding
import com.eatapp.clementine.internal.Constants
import com.eatapp.clementine.internal.showConfirmationAlert
import com.eatapp.clementine.internal.showErrorAlert
import com.eatapp.clementine.ui.base.BaseFragment
import dagger.hilt.android.AndroidEntryPoint
import java.util.Calendar
import java.util.Date

@AndroidEntryPoint
class BlockTablesFragment : BaseFragment<BlockTablesViewModel, BlockTablesFragmentBinding>() {

    enum class ClosingTime {
        START,
        END
    }

    companion object {
        fun newInstance(date: Date) = BlockTablesFragment().apply {
            arguments = Bundle().apply {
                putSerializable(Constants.DATE, date)
            }
        }
    }

    lateinit var blockingsAdapter: ClosingsAdapter
    lateinit var tablesAdapter: TagAdapter

    override fun viewModelClass(): Class<BlockTablesViewModel> {
        return BlockTablesViewModel::class.java
    }

    override fun inflateLayout() = BlockTablesFragmentBinding.inflate(layoutInflater)

    override fun onShowLockdownView(view: View) {}

    override fun onHideLockdownView(view: View) {}

    override fun viewCreated() {
        arguments?.getSerializable(Constants.DATE)?.let { date -> vm.date(date as Date) }

        observe()
        bindUI()
    }

    fun observe() {

        vm.tablesToBlock.observe(viewLifecycleOwner) {
            tablesAdapter.submitList(it)
        }

        vm.closings.observe(viewLifecycleOwner) {
            blockingsAdapter.submitList(it)
        }

        vm.create.observe(viewLifecycleOwner) {

            when (it) {
                true -> binding.createBtn.showLoading()
                else -> {
                    binding.createBtn.hideLoading()
                }
            }
        }

        vm.invalidTimes.observe(viewLifecycleOwner) {
            if (it) {
                requireContext().showErrorAlert(
                    resources.getString(R.string.invalid_times_title),
                    resources.getString(R.string.invalid_times_desc)
                )
            }
        }

        vm.loading.observe(viewLifecycleOwner) {

            if (!it) {
                binding.createBtn.hideLoading()
            }
        }
    }

    fun bindUI() {

        bindTablesAdapter()

        binding.tablesCont.setOnClickListener { showTableList() }

        binding.dateFrom.cont.setOnClickListener(DateClickListener(ClosingTime.START))
        binding.dateTo.cont.setOnClickListener(DateClickListener(ClosingTime.END))

        binding.timeFrom.listener = {
            vm.updateTime(ClosingTime.START, it)
        }

        binding.timeFrom.setOnClickListener {
            showTimesList(ClosingTime.START)
        }

        binding.timeTo.listener = {
            vm.updateTime(ClosingTime.END, it)
        }

        binding.timeTo.setOnClickListener {
            showTimesList(ClosingTime.END)
        }

        blockingsAdapter =
            ClosingsAdapter(vm.date, vm.eatManager.rooms(), vm.permission) { closing, position ->

                requireContext().showConfirmationAlert(R.string.delete_closing_title, R.string.delete_closing_desc) {
                    blockingsAdapter.notifyItemChanged(position)
                    vm.deleteClosing(closing)
                }
            }

        binding.closingsList.adapter = blockingsAdapter

    }

    private fun showTableList() {

        val list = vm.tablesList()
        bottomSheetGroupDialog(list, resources.getString(R.string.add_tables), false) {
            vm.updateTables(list.flatMap { it.items }.toMutableList())
        }
    }

    private fun bindTablesAdapter() {

        tablesAdapter = TagAdapter(true, addTagListener = {}, removeTagListener = {
            vm.removeTable(it)
        })

        binding.tablesList.adapter = tablesAdapter
    }

    private fun showTimesList(time: ClosingTime) {

        val list = vm.timesList(time)
        bottomSheetDialog(list, "", true) {
            vm.updateTime(time, list?.first { it.isSelected }?.value as Date)
        }
    }

    private inner class DateClickListener(val type: ClosingTime) : View.OnClickListener {

        override fun onClick(view: View) {

            val c = Calendar.getInstance()

            c.time = when (type == ClosingTime.START) {
                true -> vm.closingBody.value?.closingStart
                else -> vm.closingBody.value?.closingEnd
            } ?: Date()

            val datePicker = DatePickerDialog(requireContext(), { _, y, m, d ->

                vm.updateDate(type, y, m, d)

            }, c.get(Calendar.YEAR), c.get(Calendar.MONTH), c.get(Calendar.DAY_OF_MONTH))

            datePicker.show()
        }
    }
}