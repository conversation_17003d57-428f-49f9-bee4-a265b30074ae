package com.eatapp.clementine.ui.home.overflow

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.eatapp.clementine.data.network.body.ClosingBody
import com.eatapp.clementine.data.network.response.closing.Closing
import com.eatapp.clementine.data.network.response.room.TableType
import com.eatapp.clementine.data.network.response.user.Permission
import com.eatapp.clementine.data.repository.ClosingsRepository
import com.eatapp.clementine.internal.ItemsGroup
import com.eatapp.clementine.internal.SelectorItem
import com.eatapp.clementine.internal.asLiveData
import com.eatapp.clementine.internal.ceilToQuarter
import com.eatapp.clementine.internal.isNumeric
import com.eatapp.clementine.internal.managers.EatManager
import com.eatapp.clementine.internal.managers.manageClosingPeriod
import com.eatapp.clementine.internal.startOfTheEatDay
import com.eatapp.clementine.ui.base.BaseViewModel
import com.eatapp.clementine.views.StepperView
import dagger.hilt.android.lifecycle.HiltViewModel
import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Date
import java.util.Locale
import javax.inject.Inject

@HiltViewModel
class BlockTablesViewModel @Inject constructor(
    val eatManager: EatManager,
    private val closingsRepository: ClosingsRepository
): BaseViewModel() {

    var permission: Permission = eatManager.permission(identifier = manageClosingPeriod)

    lateinit var date: Date

    var create = MutableLiveData<Boolean>()
    var invalidTimes = MutableLiveData<Boolean>()
    var ordersActive = MutableLiveData<Boolean>()

    enum class ClosingTypes(val type: String) {
        ONLINE("online"),
        IN_HOUSE("in_house")
    }
    
    private val _closingBody = MutableLiveData<ClosingBody>()
    val closingBody: LiveData<ClosingBody> by lazy {
        _closingBody.asLiveData()
    }

    private val _closings = MutableLiveData<List<Closing>>()
    val closings: LiveData<List<Closing>> by lazy {
        _closings.asLiveData()
    }

    private val _tablesToBlock = MutableLiveData<MutableList<SelectorItem>>()
    val tablesToBlock: LiveData<MutableList<SelectorItem>> by lazy {
        _tablesToBlock.asLiveData()
    }

    init {
        ordersActive.postValue(eatManager.restaurant()?.ordersActive ?: false)
        _closingBody.postValue(ClosingBody())
    }

    fun date(date: Date) {

        this.date = date

        launch ({

            val response = closingsRepository.closings(date)
            _closings.postValue(response.closings.filter { it.tableIds.isNotEmpty() })
        })
    }

    fun updateDate(time: BlockTablesFragment.ClosingTime, year: Int, month: Int, day: Int) {

        val c = Calendar.getInstance()

        c.time = when (time == BlockTablesFragment.ClosingTime.START) {
            true -> closingBody.value?.closingStart
            else -> closingBody.value?.closingEnd
        } ?: Date()

        c.set(Calendar.YEAR, year)
        c.set(Calendar.MONTH, month)
        c.set(Calendar.DAY_OF_MONTH, day)

        when (time == BlockTablesFragment.ClosingTime.START) {
            true -> closingBody.value?.closingStart = ceilToQuarter(c.time)
            else -> closingBody.value?.closingEnd = ceilToQuarter(c.time)
        }
    }

    fun updateTime(time: BlockTablesFragment.ClosingTime, stepper: StepperView.Step) {

        val c = Calendar.getInstance()

        c.time = when (time) {
            BlockTablesFragment.ClosingTime.START -> _closingBody.value?.timeRangeBegin ?: startOfTheEatDay(date)
            BlockTablesFragment.ClosingTime.END -> _closingBody.value?.timeRangeEnd ?: startOfTheEatDay(date)
        }

        val increment = 15

        when(stepper) {
            StepperView.Step.PLUS -> c.add(Calendar.MINUTE, increment)
            StepperView.Step.MINUS -> c.add(Calendar.MINUTE, -increment)
        }

        updateTime(time, c.time)
    }

    fun updateTime(time: BlockTablesFragment.ClosingTime, date: Date) {

        when (time) {
            BlockTablesFragment.ClosingTime.START -> _closingBody.value?.closingStart = date
            BlockTablesFragment.ClosingTime.END -> _closingBody.value?.closingEnd = date
        }
    }

    fun tablesList(): MutableList<ItemsGroup> {

        val groups: MutableList<ItemsGroup> = mutableListOf()

        eatManager.rooms()?.forEach { room ->

            val tables = mutableListOf<SelectorItem>()

            room.tables?.sortedBy { if (isNumeric(it.number)) it.number.toInt() else 9999 }
                ?.filter { it.type == TableType.Table }
                ?.forEach { table ->
                val selected = tablesToBlock.value?.find { it.id == table.id } is SelectorItem
                val tag = SelectorItem(
                    table.id,
                    table.number,
                    null,
                    isSelected = selected,
                    isHeader = false,
                    isDisabled = false
                )
                tables.add(tag)
            }

            groups.add(ItemsGroup(room.name, "#F9F9FB", tables, checkable = true, checked = false))

        }

        return groups
    }

    fun updateTables(list: MutableList<SelectorItem>?) {

        _tablesToBlock.value = list?.filter { it.isSelected } as MutableList<SelectorItem>?
        _closingBody.value?.tableIds = _tablesToBlock.value?.map { it.id } as MutableList<String>

    }

    fun timesList(time: BlockTablesFragment.ClosingTime): MutableList<SelectorItem>? {

        val formatter = SimpleDateFormat("hh:mm a", Locale.US)

        val cMin = Calendar.getInstance()

        cMin.time = when (time) {
            BlockTablesFragment.ClosingTime.START -> _closingBody.value?.timeRangeBegin ?: date
            BlockTablesFragment.ClosingTime.END -> _closingBody.value?.timeRangeEnd ?: date
        }

        cMin.add(Calendar.HOUR, -4)
        cMin.set(Calendar.HOUR_OF_DAY, 0)
        cMin.set(Calendar.MINUTE, 0)
        cMin.set(Calendar.SECOND, 0)

        val times = mutableListOf<SelectorItem>()

        val selectedDate = when(time) {
            BlockTablesFragment.ClosingTime.START -> _closingBody.value?.closingStart
            BlockTablesFragment.ClosingTime.END ->  _closingBody.value?.closingEnd
        }

        for (i in 0..95) {

            val date = Calendar.getInstance()
            date.time = cMin.time
            date.add(Calendar.HOUR, 4)
            cMin.add(Calendar.MINUTE, 15)
            cMin.set(Calendar.SECOND, 0)

            val item = SelectorItem(
                "",
                formatter.format(date.time),
                date.time,
                isSelected = date.time == selectedDate,
                isHeader = false,
                isDisabled = false
            )
            times.add(item)
        }

        return times

    }

    fun onOnlineChanged(check: Boolean) {
        when (check) {
            true -> _closingBody.value?.addType(ClosingTypes.ONLINE.type)
            false -> _closingBody.value?.removeType(ClosingTypes.ONLINE.type)
        }
    }

    fun onInHouseChanged(check: Boolean) {
        when (check) {
            true -> _closingBody.value?.addType(ClosingTypes.IN_HOUSE.type)
            false -> _closingBody.value?.removeType(ClosingTypes.IN_HOUSE.type)
        }
    }

    fun onCreateClosing() {

        if (_closingBody.value?.closingStart?.after(_closingBody.value?.closingEnd) == true) {
            invalidTimes.postValue(true)
            return
        }

        launch({

            create.postValue(true)

            closingsRepository.insertClosing(_closingBody.value!!)
            date(date)

            create.postValue(false)

        })
    }

    fun deleteClosing(closing: Closing) {

        launch({

            closingsRepository.deleteClosing(closing.id)
            _closings.postValue(_closings.value?.filter { it.id != closing.id })

        })
    }

    fun removeTable(tag: SelectorItem) {
        _tablesToBlock.value = tablesToBlock.value?.filter { it.id != tag.id }?.toMutableList()
        _closingBody.value?.tableIds = _tablesToBlock.value?.map { it.id } as MutableList<String>
    }
}