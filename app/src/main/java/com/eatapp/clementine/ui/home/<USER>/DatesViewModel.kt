package com.eatapp.clementine.ui.home.overview

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.eatapp.clementine.internal.asLiveData
import com.eatapp.clementine.ui.base.BaseViewModel
import java.util.Calendar
import java.util.Date

open class DatesViewModel() : BaseViewModel() {

    private val _date = MutableLiveData<Date>()
    val date: LiveData<Date> by lazy {
        _date.asLiveData()
    }

    fun minusDay() {

        val c = Calendar.getInstance()
        c.time = _date.value
        c.add(Calendar.DAY_OF_MONTH, -1)
        setDate(c.time)
    }

    fun plusDay() {

        val c = Calendar.getInstance()
        c.time = _date.value
        c.add(Calendar.DAY_OF_MONTH, 1)
        setDate(c.time)
    }

    fun updateDate(year: Int, month: Int, day: Int) {

        val c = Calendar.getInstance()
        c.time = _date.value
        c.set(Calendar.YEAR, year)
        c.set(Calendar.MONTH, month)
        c.set(Calendar.DAY_OF_MONTH, day)
        setDate(c.time)
    }

    fun setDate(date: Date) {
        if (_date.value == date) return
        _date.value = date
    }
}
