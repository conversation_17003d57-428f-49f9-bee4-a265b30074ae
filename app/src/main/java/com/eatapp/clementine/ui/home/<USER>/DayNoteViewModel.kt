package com.eatapp.clementine.ui.home.overflow

import android.util.Log
import androidx.lifecycle.MutableLiveData
import com.eatapp.clementine.data.network.response.daynote.DayNoteData
import com.eatapp.clementine.data.network.response.user.Permission
import com.eatapp.clementine.data.repository.DayNoteRepository
import com.eatapp.clementine.internal.managers.DataManager
import com.eatapp.clementine.internal.managers.EatManager
import com.eatapp.clementine.internal.managers.manageDayNotes
import com.eatapp.clementine.ui.base.BaseViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Job
import java.util.Date
import javax.inject.Inject
import kotlin.concurrent.fixedRateTimer

@HiltViewModel
class DayNoteViewModel @Inject constructor(
    eatManager: EatManager,
    val dataManager: DataManager,
    private val dayNoteRepository: DayNoteRepository
) : BaseViewModel() {

    var permission: Permission = eatManager.permission(identifier = manageDayNotes)

    private var job: Job? = null

    private var dayNote: DayNoteData? = null

    val note = MutableLiveData<String>()
    var update = MutableLiveData<Boolean>()

    var date: Date? = null

    init {
        initTimers()
    }

    fun initTimers() {

        if (!dataManager.isSubscribed() && !dataManager.isHydraEnabled()) {

            timers.add(fixedRateTimer("default", false, 8000L, 5000L) {
                getDayNote(true)
            })
        }
    }

    fun date(newDate: Date?) {

        if (date == newDate) return

        job?.cancel()

        date = newDate

        getDayNote(false)
    }

    fun getDayNote(poll: Boolean) {

        if ((paused && poll) || date == null) return

        Log.v("day_note_api", "day_notes")

        job = launch({

            val response = dayNoteRepository.dayNote(date!!)

            dayNote = if (response.data.isEmpty()) {
                note.postValue("")
                null
            } else {
                note.postValue(response.data[0].content)
                response.data[0]
            }

        }, poll)
    }

    fun onUpdateNotes() {

        paused = true

        launch({

            update.postValue(true)

            dayNote = when (dayNote) {
                null -> dayNoteRepository.insertDayNote(date!!, note.value.toString()).data
                else -> dayNoteRepository.updateDayNote(dayNote?.id!!, note.value.toString()).data
            }

            paused = false

            update.postValue(false)

        }, false)
    }
}