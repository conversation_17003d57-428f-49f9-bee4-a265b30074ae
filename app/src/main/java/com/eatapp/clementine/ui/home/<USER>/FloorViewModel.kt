package com.eatapp.clementine.ui.home.floor

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.eatapp.clementine.data.network.response.reservation.Reservation
import com.eatapp.clementine.data.network.response.room.Room
import com.eatapp.clementine.data.repository.FabricateRepository
import com.eatapp.clementine.data.repository.ReservationsRepository
import com.eatapp.clementine.internal.Status
import com.eatapp.clementine.internal.asLiveData
import com.eatapp.clementine.internal.managers.DataManager
import com.eatapp.clementine.internal.managers.EatManager
import com.eatapp.clementine.internal.managers.FeatureFlagsManager
import com.eatapp.clementine.ui.home.overview.DatesViewModel
import com.eatapp.clementine.views.TableState
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import javax.inject.Inject

@HiltViewModel
class FloorViewModel  @Inject constructor(
    val dataManager: DataManager,
    val eatManager: EatManager,
    val featureFlagsManager: FeatureFlagsManager,
    private val reservationsRepository: ReservationsRepository,
    private val fabricateRepository: FabricateRepository
) : DatesViewModel() {

    private lateinit var tableStates: List<TableState>

    var selectedRoom: String = ""

    private var dateJob: Job? = null
    private var roomsJob: Job? = null

    private val _reservations = MutableLiveData<List<Reservation>>()
    val reservations: LiveData<List<Reservation>> by lazy {
        _reservations.asLiveData()
    }

    private val _coversCount = MutableLiveData<Int>()
    val coversCount: LiveData<Int> by lazy {
        _coversCount.asLiveData()
    }

    private val _roomUpdated = MutableLiveData<Boolean>()
    val roomUpdated: LiveData<Boolean> by lazy {
        _roomUpdated.asLiveData()
    }

    init {
        loading(true)
        reservations()
    }

    fun rooms(): List<Room>? {
        return eatManager.rooms()
    }

    fun tableStates(): List<TableState> {
        return tableStates
    }

    fun room(id: String = selectedRoom): Room? {
        if (eatManager.rooms().isNullOrEmpty()) {
            return null
        }
        return eatManager.rooms()?.find { it.id == selectedRoom } ?: eatManager.rooms()?.first()
    }

    fun createTableStates(room: Room?) {
        tableStates = room?.tables?.map { table ->
            val server = eatManager.servers()?.firstOrNull { it.id == table.restaurantServerId }
            val reservations = reservations.value?.filter { it.tables?.map { t -> t.id }
                ?.contains(table.id) == true }?.toSet() ?: emptySet()
            TableState(table = table, server = server, reservations = reservations, isFloor = true)
        } ?: listOf()
    }

    private fun updateTableStates(reservations: List<Reservation>) {
        tableStates.forEach { state ->
            val updatedReservations = reservations.filter { it.tables?.map { t -> t.id }?.contains(state.table.id) == true }.toSet()
            state.shouldUpdateView = (updatedReservations == state.reservations).not()
            state.reservations = updatedReservations
            state.updateConfig()
        }
    }

    fun updateDate() {
        dateJob?.cancel()
        reservations()
    }

    fun reservations() {

        if (date.value == null) return

        dateJob = launch({

            val response = reservationsRepository.reservations(date.value!!)

            eatManager.processReservations(response)

           reservations(response.reservations)

            var coversCount = 0

            response.reservations.filter { it.status != Status.Value.WAITLIST.code }.forEach { reservation ->
                if (Status.isSeated(reservation.status) || Status.isUpcomingUi(reservation.status)
                    || Status.isFinished(reservation.status)
                ) coversCount += reservation.covers
            }

            _coversCount.postValue(coversCount)

            loading(false)

        }, false)
    }

    fun fetchRooms() {

        roomsJob?.cancel()

        roomsJob = launch({

            delay(100)

            val response = fabricateRepository.rooms(eatManager.restaurantId())
            eatManager.rooms(rooms = response.rooms)

            tables()

        }, false)
    }

    fun tables() {

        launch({

            val response = fabricateRepository.tables(eatManager.restaurantId())
            eatManager.tables(tables = response.tables)

            eatManager.rooms()?.map { room ->
                room.tables = response.tables.filter { table ->
                    room.id == table.roomId
                }
            }

            createTableStates(room())

            _roomUpdated.postValue(true)

        }, false)
    }

    fun reservations(reservations: List<Reservation>) {

        val rvsrs = reservations.filter { it.status != Status.Value.WAITLIST.code }
            .sortedBy { it.startTime }

        updateTableStates(rvsrs)
        _reservations.postValue(rvsrs)
    }
}