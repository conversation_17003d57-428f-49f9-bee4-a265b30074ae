package com.eatapp.clementine.ui.home.overview

import android.view.View
import com.eatapp.clementine.adapter.PosRecordsAdapter
import com.eatapp.clementine.data.network.response.reservation.Reservation
import com.eatapp.clementine.databinding.GuestSpendFragmentBinding
import com.eatapp.clementine.ui.base.BaseFragment
import com.eatapp.clementine.ui.common.pos.ReservationPosFragment
import dagger.hilt.android.AndroidEntryPoint
import java.util.Date
import java.util.Timer
import kotlin.concurrent.schedule

@AndroidEntryPoint
class GuestSpendFragment : BaseFragment<GuestSpendViewModel, GuestSpendFragmentBinding>() {

    lateinit var adapter: PosRecordsAdapter

    override fun inflateLayout() = GuestSpendFragmentBinding.inflate(layoutInflater)

    override fun viewModelClass() = GuestSpendViewModel::class.java

    override fun viewCreated() {
        bindUI()
        observe()
    }

    private fun bindUI() {

        adapter = PosRecordsAdapter {
            bottomSheetFragment(
                it.reservation?.guestName,
                ReservationPosFragment.newInstance(it)
            )
        }

        binding.posRecordsList.adapter = adapter
    }

    private fun observe() {

        vm.loading.observe(viewLifecycleOwner) {
            when (it) {
                true -> binding.progress.visibility = View.VISIBLE
                else -> binding.progress.visibility = View.GONE
            }
        }

        vm.posRecords.observe(viewLifecycleOwner) { posRecords ->

            if (vm.loading.value == false && (posRecords == null || posRecords.isEmpty())) {
                binding.emptyList.visibility = View.VISIBLE
                binding.posRecordsList.visibility = View.INVISIBLE
                adapter.submitList(null)
            } else {
                binding.emptyList.visibility = View.GONE
                binding.posRecordsList.visibility = View.VISIBLE
                adapter.submitList(posRecords.toMutableList())
            }

            Timer().schedule(100) {
                binding.posRecordsList.smoothScrollToPosition(0)
            }
        }

        vm.searchActive.observe(viewLifecycleOwner) { active ->

            when (active) {
                true -> {}
                else -> {
                    binding.searchEditText.clearFocus()
                    hideKeyboard()
                }
            }
        }
    }

    override fun onShowLockdownView(view: View) {}

    override fun onHideLockdownView(view: View) {}

    fun updateDate(date: Date, reservations: List<Reservation>, loading: (Boolean) -> Unit) {
        try {
            vm.updateDate(date, reservations, loading)
        } catch (_: UninitializedPropertyAccessException) {}
    }
}