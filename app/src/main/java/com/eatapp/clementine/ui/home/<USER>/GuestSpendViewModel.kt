package com.eatapp.clementine.ui.home.overview

import android.util.Log
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.eatapp.clementine.data.network.response.pos.PosRecord
import com.eatapp.clementine.data.network.response.reservation.Reservation
import com.eatapp.clementine.data.repository.PosRepository
import com.eatapp.clementine.internal.PosSection
import com.eatapp.clementine.internal.asLiveData
import com.eatapp.clementine.internal.managers.EatManager
import com.eatapp.clementine.ui.base.BaseViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Job
import java.util.Date
import java.util.Locale
import java.util.Timer
import javax.inject.Inject
import kotlin.concurrent.schedule

@HiltViewModel
class GuestSpendViewModel @Inject constructor(
    private val eatManager: EatManager,
    private val posRepository: PosRepository
) : BaseViewModel() {

    private var job: Job? = null
    private var searchQuery: String = ""
    private var sectionList: List<PosSection> = listOf()
    private var all: MutableList<Any?> = mutableListOf()

    var date: Date? = null
    var reservations: List<Reservation>? = null
    var tempPosRecords: MutableList<PosRecord?>? = null

    private val _sortHighToLow = MutableLiveData<Boolean>()
    val sortHighToLow: LiveData<Boolean> by lazy {
        _sortHighToLow.asLiveData()
    }

    private val _searchActive = MutableLiveData<Boolean>()
    val searchActive: LiveData<Boolean> by lazy {
        _searchActive.asLiveData()
    }

    private val _posRecords = MutableLiveData<List<Any?>>()
    val posRecords: LiveData<List<Any?>> by lazy {
        _posRecords.asLiveData()
    }

    init {
        _sortHighToLow.value = true
        _searchActive.value = false
        loading(true)
    }

    fun onTextChanged(s: CharSequence, start: Int, before: Int, count: Int) {

        if (s.toString().isEmpty()) {
            _searchActive.value = false
        }

        searchQuery = s.toString()
        filterPosRecords(null)
    }

    private fun filterPosRecords(l: ((Boolean) -> Unit)?) {

        val posRecords: MutableList<Any?> = all

        if (searchQuery.isBlank()) {
            Timer().schedule(100) { _posRecords.postValue(posRecords) }
            loading(false)
            l?.let { it(false) }
            return
        }

        val pr = posRecords.filter { item ->
            item is PosSection || (item is PosRecord &&
                    ((item.reservation?.guest?.firstName?.lowercase(Locale.ROOT)?.contains(searchQuery.lowercase(Locale.ROOT).trim()) == true) ||
                            (item.reservation?.guest?.lastName?.lowercase(Locale.ROOT)?.contains(searchQuery.lowercase(Locale.ROOT).trim()) == true) ||
                            (String.format("%s %s", item.reservation?.guest?.firstName?.lowercase(Locale.ROOT),
                                item.reservation?.guest?.lastName?.lowercase(Locale.ROOT)).contains(searchQuery.lowercase(Locale.ROOT).trim())) ||
                            (item.ticketId?.lowercase(Locale.ROOT)?.contains(searchQuery.lowercase(Locale.ROOT).trim()) == true)))
        }

        loading(false)
        l?.let { it(false) }

        Timer().schedule(50) { _posRecords.postValue(pr) }
    }

    fun sortPosRecords() {
        _sortHighToLow.value = sortHighToLow.value!!.not()
        makeFinalList(null)
    }

    fun updateDate(newDate: Date?, res: List<Reservation>, loading: (Boolean) -> Unit) {

        if (date == newDate) return

        job?.cancel()

        date = newDate
        reservations = res

        posRecords(loading)
    }

    private fun posRecords(l: ((Boolean) -> Unit)?) {

        if (date == null || reservations == null) return

        Log.v("pos_api", "pos_records")

        job = launch({

            val response = posRepository.posRecords(date!!)

            tempPosRecords = reservations?.mapNotNull { res ->
                response.posRecords.firstOrNull { pr ->
                    res.relationships?.posRecord?.data?.id == pr.id
                }.also { pr -> pr?.reservation = res }
            }?.sortedBy { pr ->
                pr.createdAt
            }?.toMutableList()

            val missingRecords: List<String>? = reservations?.let {
                it.mapNotNull { res ->
                    res.relationships?.posRecord?.data?.id
                }.filter { id ->
                    id !in response.posRecords.map{ pr -> pr.id }
                }
            }

            fetchMissingRecords(missingRecords)

            sectionList = eatManager.groupPosRecords(tempPosRecords)

            makeFinalList(l)

        }, false)
    }

    private fun fetchMissingRecords(missingRecords: List<String>?) {

        Log.v("pos_api", "pos_record")

        val records: MutableList<PosRecord> = mutableListOf()

        job = launch({

            missingRecords?.forEach {

                val response = posRepository.posRecord(it)

                records.add(response.posRecord)

                if (records.size%5 == 0 || missingRecords.size - records.size < 5) {

                    tempPosRecords = reservations?.mapNotNull { res ->
                        records.firstOrNull { pr ->
                            res.relationships?.posRecord?.data?.id == pr.id
                        }.also { pr -> pr?.reservation = res }
                    }?.sortedBy { pr ->
                        pr.createdAt
                    }?.toMutableList()

                    sectionList = eatManager.groupPosRecords(tempPosRecords)
                    makeFinalList(null)
                }
            }
        }, false)
    }

    private fun makeFinalList(l: ((Boolean) -> Unit)?) {

        all.clear()

        sectionList.forEach { section ->
            all.add(section)
            when (_sortHighToLow.value) {
                true -> all.addAll(section.items!!.sortedByDescending { it?.commercialTotal })
                false -> all.addAll(section.items!!.sortedBy { it?.commercialTotal })
                else -> {}
            }

        }

        filterPosRecords(l)
    }
}