package com.eatapp.clementine.ui.home.more

import android.app.Activity.RESULT_OK
import android.content.Intent
import android.view.View
import androidx.navigation.Navigation
import com.eatapp.clementine.R
import com.eatapp.clementine.databinding.MoreFragmentBinding
import com.eatapp.clementine.internal.Constants
import com.eatapp.clementine.internal.isTablet
import com.eatapp.clementine.internal.showConfirmationAlert
import com.eatapp.clementine.ui.base.BaseFragment
import com.eatapp.clementine.ui.common.hubspot.HubspotActivity
import com.eatapp.clementine.ui.common.printer.PrinterActivity
import com.eatapp.clementine.ui.common.selector.RestaurantsActivity
import com.eatapp.clementine.ui.common.voucher.RedeemVoucherActivity
import com.eatapp.clementine.ui.home.more.guests.GuestsActivity
import com.eatapp.clementine.ui.omnisearch.OmniSearchActivity
import dagger.hilt.android.AndroidEntryPoint
import androidx.navigation.findNavController

@AndroidEntryPoint
class MoreFragment : BaseFragment<MoreViewModel, MoreFragmentBinding>() {

    override fun inflateLayout() = MoreFragmentBinding.inflate(layoutInflater)

    override fun viewModelClass() = MoreViewModel::class.java

    override fun viewCreated() {

        vm.isTablet.value = context?.isTablet == true

        if (vm.settings[0].arrow) {

            binding.restaurant.cont.setOnClickListener {
                val intent = Intent(context, RestaurantsActivity::class.java)
                startActivityForResult(intent, Constants.RESTAURANTS_REQUEST)
            }
        }

        binding.guestManagement.cont.setOnClickListener {
            val intent = Intent(context, GuestsActivity::class.java)
            startActivity(intent)
        }

        binding.redeem.cont.setOnClickListener {
            val intent = Intent(context, RedeemVoucherActivity::class.java)
            startActivity(intent)
        }

        binding.logout.cont.setOnClickListener {
            requireContext().showConfirmationAlert(R.string.logout_title, R.string.logout_desc) {
                vm.unRegisterUser()
                redirectToLogin()
            }
        }

        binding.support.cont.setOnClickListener {
            val intent = Intent(context, HubspotActivity::class.java)
            startActivity(intent)
        }

        binding.printSettings.cont.setOnClickListener {
            val intent = Intent(context, PrinterActivity::class.java)
            startActivity(intent)
        }

        binding.reservationListMode.cont.setOnClickListener {
            Intent(context, ReservationViewModeActivity::class.java).apply {
                startActivity(this)
            }
        }

        binding.omnisearch.cont.setOnClickListener {
            Intent(context, OmniSearchActivity::class.java).apply {
                startActivity(this)
            }
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)

        if (requestCode == Constants.RESTAURANTS_REQUEST) {

            if (resultCode == RESULT_OK) {
                val navController = requireActivity().findNavController(R.id.nav_host_fragment)
                navController.navigate(R.id.navigation_overview)
            }
        }
    }

    override fun onShowLockdownView(view: View) {}

    override fun onHideLockdownView(view: View) {}
}