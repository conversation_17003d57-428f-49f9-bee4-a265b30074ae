package com.eatapp.clementine.ui.home.more

import androidx.lifecycle.MutableLiveData
import com.eatapp.clementine.data.network.response.restaurant.AccountStateType
import com.eatapp.clementine.enums.SettingsEntry
import com.eatapp.clementine.internal.managers.AnalyticsManager
import com.eatapp.clementine.internal.managers.Commands
import com.eatapp.clementine.internal.managers.DataManager
import com.eatapp.clementine.internal.managers.EatManager
import com.eatapp.clementine.ui.base.BaseViewModel
import com.google.firebase.messaging.FirebaseMessaging
import com.google.firebase.remoteconfig.FirebaseRemoteConfig
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject

@HiltViewModel
class MoreViewModel @Inject constructor(
    private val eatManager: EatManager,
    private val dataManager: DataManager,
    private val analyticsManager: AnalyticsManager
) : BaseViewModel() {

    val settings: List<SettingsEntry> = SettingsEntry.list
    val showRedeemVoucherEntry = MutableLiveData<Boolean>()
    val isTablet = MutableLiveData<Boolean>()
    val isFreemium = eatManager.accountState() == AccountStateType.IN_HOUSE

    init {

        showRedeemVoucherEntry.value =
            FirebaseRemoteConfig.getInstance().getBoolean("voucher_enabled")
                    && eatManager.restaurant()?.uiPreferences?.contains("vouchers_enabled") ?: false

        settings[0].title = eatManager.restaurant()?.name
        settings[0].arrow = ((eatManager.restaurants()?.size ?: 0) > 1)

        analyticsManager.trackViewSettingsView()
    }

    fun unRegisterUser() {
        FirebaseMessaging.getInstance().unsubscribeFromTopic(
            "restaurant_${eatManager.restaurantId()}"
        )
        dataManager.updateSubscription(Commands.Unsubscribe.type, eatManager.restaurantId())
        dataManager.disconnect()
        eatManager.flush()
        analyticsManager.unregisterUser()
    }
}