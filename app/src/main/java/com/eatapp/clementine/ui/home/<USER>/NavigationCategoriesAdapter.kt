package com.eatapp.clementine.ui.home.tab_bar

import android.annotation.SuppressLint
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.eatapp.clementine.R
import com.eatapp.clementine.data.network.response.apiresources.NavigationItemCategory
import com.eatapp.clementine.data.network.response.apiresources.NavigationItemModel
import com.eatapp.clementine.data.network.response.apiresources.NavigationItemSubCategory
import com.eatapp.clementine.databinding.ListItemNavigationCategoryBinding
import com.eatapp.clementine.internal.getDrawable
import com.eatapp.clementine.internal.visibleOrGone


class NavigationCategoriesAdapter(
    private var sideBarExpanded: Boolean = false,
    private var isPopup: Boolean = false,
    val itemClickListener: (NavigationItemCategory, NavigationItemModel) -> Unit,
    val categoryClickListener: (NavigationItemCategory, View) -> Unit
) :
    ListAdapter<NavigationItemCategory, RecyclerView.ViewHolder>(
        NavigationCategoryDiffCallback()
    ) {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        return CategoryViewHolder(
            ListItemNavigationCategoryBinding.inflate(
                LayoutInflater.from(parent.context), parent, false
            )
        )
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        (holder as CategoryViewHolder).bind(getItem(position))
    }

    fun reloadItem(categoryName: String) {
        val position = currentList.indexOfFirst { it.title == categoryName }
        notifyItemChanged(position)
    }

    @SuppressLint("NotifyDataSetChanged")
    fun toggleSideBarState(expanded: Boolean) {
        this.sideBarExpanded = expanded
        this.notifyDataSetChanged()
    }

    inner class CategoryViewHolder(private val binding: ListItemNavigationCategoryBinding) :
        RecyclerView.ViewHolder(binding.root) {

        private var adapter: NavigationItemsAdapter = NavigationItemsAdapter(isPopup = isPopup)

        fun bind(category: NavigationItemCategory) = with(binding) {
            tvTitle.text = category.title

            loadIcon(category)

            rvSubcategories.adapter = adapter

            itemView.setOnClickListener {
                categoryClickListener.invoke(category, binding.root)
            }

            if (!isPopup) {
                containerData.setBackgroundResource(
                    if (category.selected) {
                        R.drawable.shape_rounded_bcg_grey900
                    } else if (category.expanded) {
                        R.drawable.shape_rounded_bcg_grey700
                    } else android.R.color.transparent
                )
            }

            ivChevron.setImageResource(if (category.expanded) R.drawable.ic_icon_arrow_up else R.drawable.ic_icon_arrow_down)

            tvTitle.visibleOrGone = sideBarExpanded || isPopup
            ivChevron.visibleOrGone = sideBarExpanded && !isPopup
            ivIconCollapsed.visibleOrGone = !sideBarExpanded && !isPopup

            rvSubcategories.visibleOrGone = (category.expanded && sideBarExpanded) || isPopup

            if (isPopup || (category.expanded && sideBarExpanded)) {
                loadNavigationItems(category)
            }
            adapter.itemClickListener = {
                itemClickListener.invoke(category, it)
            }
        }

        private fun loadNavigationItems(category: NavigationItemCategory) {
            category.subCategories?.let {
                val items = flattenNavigationItems(category)
                adapter.submitList(items)
            } ?: run {
                adapter.submitList(category.items as List<Any>?)
            }
        }

        private fun flattenNavigationItems(category: NavigationItemCategory): List<Any>? {
            val items = category.subCategories?.flatMap { subCategory: NavigationItemSubCategory ->
                val nestedItems = subCategory.items.orEmpty()
                listOf(subCategory) + nestedItems
            }
            return items
        }

        private fun loadIcon(category: NavigationItemCategory) {
            category.icon?.let {
                try {
                    val image = binding.root.context.getDrawable("ic_navigation_${it}")
                    binding.ivIcon.setImageDrawable(image)
                } catch (e: Exception) {
                    binding.ivIcon.setImageResource(R.drawable.ic_navigation_unknown)
                }
            }
        }
    }
}

private class NavigationCategoryDiffCallback : DiffUtil.ItemCallback<NavigationItemCategory>() {

    override fun areItemsTheSame(
        oldItem: NavigationItemCategory,
        newItem: NavigationItemCategory
    ): Boolean {
        return oldItem.title == newItem.title
    }

    override fun areContentsTheSame(
        oldItem: NavigationItemCategory,
        newItem: NavigationItemCategory
    ): Boolean {
        return oldItem == newItem
    }
}