package com.eatapp.clementine.ui.home.tab_bar

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.eatapp.clementine.data.network.response.restaurant.Restaurant
import com.eatapp.clementine.databinding.ListItemNavigationRestaurantBinding
import com.eatapp.clementine.internal.visibleOrGone

class NavigationRestaurantSelectorAdapter(
    val restaurantClickListener: ((String) -> Unit)
) : ListAdapter<Restaurant, NavigationRestaurantSelectorAdapter.RestaurantViewHolder>(
    RestaurantDiffCallback()
) {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RestaurantViewHolder {
        return RestaurantViewHolder(
            ListItemNavigationRestaurantBinding.inflate(
                LayoutInflater.from(parent.context), parent, false
            )
        )
    }

    override fun onBindViewHolder(holder: RestaurantViewHolder, position: Int) {
        holder.bind(getItem(position))
    }

    inner class RestaurantViewHolder(
        private val binding: ListItemNavigationRestaurantBinding
    ) : RecyclerView.ViewHolder(binding.root) {
        fun bind(restaurant: Restaurant) = with(binding) {
            tvRestaurantName.text = restaurant.name
            ivChecked.visibleOrGone = restaurant.isSelected
            root.setOnClickListener {
                restaurantClickListener.invoke(restaurant.id)
            }
        }
    }
}

private class RestaurantDiffCallback : DiffUtil.ItemCallback<Restaurant>() {

    override fun areItemsTheSame(
        oldItem: Restaurant,
        newItem: Restaurant
    ): Boolean {
        return oldItem.id == newItem.id
    }

    override fun areContentsTheSame(
        oldItem: Restaurant,
        newItem: Restaurant
    ): Boolean {
        return oldItem == newItem
    }
}