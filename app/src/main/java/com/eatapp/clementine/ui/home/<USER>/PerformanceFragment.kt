package com.eatapp.clementine.ui.home.reports

import android.view.View
import com.eatapp.clementine.data.network.response.reports.ReportsResponse
import com.eatapp.clementine.databinding.PerformanceFragmentBinding
import com.eatapp.clementine.ui.base.BaseFragment
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class PerformanceFragment : BaseFragment<PerformanceViewModel, PerformanceFragmentBinding>() {

    override fun viewModelClass() = PerformanceViewModel::class.java

    override fun inflateLayout() = PerformanceFragmentBinding.inflate(layoutInflater)

    override fun viewCreated() {
        progress = binding.progress

        vm.shifts.observe(viewLifecycleOwner) { list ->
            binding.totalCoversMain.text = list.first().total.toString()
            binding.cont.visibility = View.VISIBLE
            binding.shift.setData(list)
        }

        vm.source.observe(viewLifecycleOwner) { list ->
            binding.source.setData(list)
        }

        vm.type.observe(viewLifecycleOwner) { list ->
            binding.type.setData(list)
        }

        vm.status.observe(viewLifecycleOwner) { list ->
            binding.status.setData(list)
            //showProgress(false)
        }
    }

    override fun onShowLockdownView(view: View) {}

    override fun onHideLockdownView(view: View) {}

    fun updateReports(reports: ReportsResponse) {
        try {
            vm.updateReports(reports)
        } catch (ex: UninitializedPropertyAccessException) {
        }
    }
}
