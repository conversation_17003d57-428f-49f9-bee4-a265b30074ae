package com.eatapp.clementine.ui.home.reports

import android.os.Handler
import android.os.Looper
import android.view.View
import androidx.core.content.ContextCompat
import com.eatapp.clementine.R
import com.eatapp.clementine.databinding.PhoenixReportsFragmentBinding
import com.eatapp.clementine.databinding.ReportsFragmentBinding
import com.eatapp.clementine.ui.base.BaseFragment
import com.eatapp.clementine.ui.common.webview.WebViewFragment
import com.google.android.material.tabs.TabLayout
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class PhoenixReportsFragment : BaseFragment<PhoenixReportsViewModel, PhoenixReportsFragmentBinding>() {

    override fun inflateLayout() = PhoenixReportsFragmentBinding.inflate(layoutInflater)

    override fun viewModelClass() = PhoenixReportsViewModel::class.java

    override fun viewCreated() {
        if (vm.featureFlagsManager.badgerEnabled) {
            checkLockdown(vm.eatManager, ScreenType.Reports)
        }

        bindUI()
    }

    override fun onShowLockdownView(view: View) {
        binding.container.addView(view)
    }

    override fun onHideLockdownView(view: View) {
        view.animate().alpha(0f).withEndAction {
            binding.container.removeView(view)
        }
    }

    override fun reloadRestaurant() {
        Handler(Looper.getMainLooper()).postDelayed({
            vm.restaurant()
        }, 3000)
    }

    private fun bindUI() {
        openReservations()
        binding.tabLayout.addOnTabSelectedListener(object: TabLayout.OnTabSelectedListener{
            override fun onTabSelected(tab: TabLayout.Tab?) {
                if (tab?.position == 0) {
                    openReservations()
                } else if (tab?.position == 1) {
                    openGuests()
                } else {
                    openReviews()
                }
            }

            override fun onTabUnselected(tab: TabLayout.Tab?) {}

            override fun onTabReselected(tab: TabLayout.Tab?) {}
        })
    }

    private fun openReservations() {
        childFragmentManager.beginTransaction()
            .replace(
                R.id.main_container,
                WebViewFragment.newInstance(vm.loadReservationsReportsUrl(), false, isReports = true)
            )
            .commitNow()
    }

    private fun openGuests() {
        childFragmentManager.beginTransaction()
            .replace(
                R.id.main_container,
                WebViewFragment.newInstance(vm.loadGuestsReportsUrl(), false, isReports = true)
            )
            .commitNow()
    }

    private fun openReviews() {
        childFragmentManager.beginTransaction()
            .replace(
                R.id.main_container,
                ReviewsFragment()
            )
            .commitNow()
    }
}
