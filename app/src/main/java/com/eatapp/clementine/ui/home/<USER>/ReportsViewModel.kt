package com.eatapp.clementine.ui.home.reports

import android.util.Log
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.eatapp.clementine.data.network.response.reports.ReportsResponse
import com.eatapp.clementine.data.repository.FabricateRepository
import com.eatapp.clementine.data.repository.ReportsRepository
import com.eatapp.clementine.internal.asLiveData
import com.eatapp.clementine.internal.managers.DataManager
import com.eatapp.clementine.internal.managers.EatManager
import com.eatapp.clementine.internal.managers.FeatureFlagsManager
import com.eatapp.clementine.ui.home.overview.DatesViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Job
import java.util.Calendar
import javax.inject.Inject

@HiltViewModel
open class ReportsViewModel @Inject constructor(
    val dataManager: DataManager,
    val eatManager: EatManager,
    val featureFlagsManager: FeatureFlagsManager,
    private val reportsRepository: ReportsRepository,
    private val fabricateRepository: FabricateRepository
) : DatesViewModel() {

    private var job: Job? = null

    private val _reports = MutableLiveData<ReportsResponse>()
    val reports: LiveData<ReportsResponse> by lazy {
        _reports.asLiveData()
    }

    init {
        loading(true)
    }

    fun updateDate() {

        job?.cancel()

        Log.v("current_date: ", date.value.toString())

        val c = Calendar.getInstance()
        c.time = date.value
        c.add(Calendar.DAY_OF_YEAR, 1)

        reports()
    }

    fun reports() {

        if (date.value == null) return

        job = launch({

            val response = reportsRepository.reports(date.value!!, date.value!!)
            _reports.postValue(response)

            loading(false)

        }, false)
    }

    fun restaurant() {

        launch({

            val response = fabricateRepository.restaurant(eatManager.restaurantId())
            eatManager.restaurant(response)

        }, true)
    }
}