package com.eatapp.clementine.ui.home.overview

import android.content.Intent
import android.graphics.Bitmap
import android.os.Bundle
import android.util.Log
import android.view.View
import android.widget.TextView
import androidx.appcompat.widget.PopupMenu
import androidx.core.content.ContextCompat
import androidx.core.graphics.drawable.toBitmap
import androidx.recyclerview.widget.RecyclerView
import androidx.recyclerview.widget.RecyclerView.OnScrollListener
import androidx.recyclerview.widget.SimpleItemAnimator
import com.eatapp.clementine.R
import com.eatapp.clementine.adapter.ReservationAdapter
import com.eatapp.clementine.data.network.response.reservation.Reservation
import com.eatapp.clementine.databinding.ReservationsFragmentBinding
import com.eatapp.clementine.internal.Constants
import com.eatapp.clementine.internal.Status
import com.eatapp.clementine.internal.isTablet
import com.eatapp.clementine.internal.managers.ActionType
import com.eatapp.clementine.internal.parcelableArrayList
import com.eatapp.clementine.internal.showErrorAlert
import com.eatapp.clementine.ui.base.BaseFragment
import com.eatapp.clementine.ui.common.printer.PrinterFragment
import com.eatapp.clementine.ui.home.HomeActivity
import com.eatapp.clementine.ui.home.overview.ReservationsViewModel.StatusType.FLOOR
import com.eatapp.clementine.ui.reservation.ReservationActivity
import dagger.hilt.android.AndroidEntryPoint
import java.util.Date
import java.util.Timer
import kotlin.concurrent.schedule

@AndroidEntryPoint
class ReservationsFragment : BaseFragment<ReservationsViewModel, ReservationsFragmentBinding>() {

    var adapter: ReservationAdapter? = null

    companion object {

        fun newInstance(g: ReservationsViewModel.StatusType?, r: List<Reservation>? = null, t: String? = null) = ReservationsFragment().apply {
            arguments = Bundle().apply {
                putSerializable(Constants.STATUS_TYPE, g)
                putString(Constants.TABLE_ID_EXTRA, t)
                putParcelableArrayList(Constants.RESERVATIONS_EXTRA, r?.let { ArrayList(it) })
            }
        }
    }

    override fun viewModelClass() = ReservationsViewModel::class.java

    override fun inflateLayout() = ReservationsFragmentBinding.inflate(layoutInflater)

    override fun viewCreated() {
        arguments?.getSerializable(Constants.STATUS_TYPE)
            ?.let { type -> vm.type(type as ReservationsViewModel.StatusType) }

        if (vm.type.value == ReservationsViewModel.StatusType.RESERVATION) {
            vm.analyticsManager.trackViewReservationView()
            vm.trackReservationViewMode()
        } else if (vm.type.value == FLOOR) {
            arguments?.parcelableArrayList<Reservation>(Constants.RESERVATIONS_EXTRA)?.let { r ->
                vm.reservations(r.toList(), null)
            }
            arguments?.getString(Constants.TABLE_ID_EXTRA)
                ?.let { id -> vm.tableId = id }
        }

        bindUI()
        observe()
    }

    override fun onDestroy() {
        super.onDestroy()
        adapter?.stopTimers()
    }

    override fun onResume() {
        super.onResume()
        vm.refreshConversations()
    }

    override fun onShowLockdownView(view: View) {}

    override fun onHideLockdownView(view: View) {}

    private fun bindUI() {
        progress = binding.progressGroup

        adapter = ReservationAdapter(
            ReservationAdapter.ListType.OVERVIEW,
            vm.posActive,
            vm.eatManager.openedReservations,
            vm.paymentsActive,
            vm.timerPermission,
            onSectionGroupClickListener = { sectionGroup ->
                vm.expandCollapseGroup(sectionGroup)
                hideKeyboard()
            },
            onReservationClickListener = {
                reservationSelected(it)
            },
            onSectionClickListener = { section ->
                vm.expandCollapseSection(section)
            },
            onStatusClickListener = {
                if (!vm.permission.boolValue) {
                    requireContext().showErrorAlert(
                        resources.getString(R.string.update_reservation),
                        vm.permission.errorMessage
                    )
                } else {
                    showStatusSelector(it)
                }
            }
        )
        binding.reservationsList.adapter = adapter
        binding.reservationsList.addOnScrollListener(object: OnScrollListener() {
            override fun onScrollStateChanged(recyclerView: RecyclerView, newState: Int) {
                if (newState == RecyclerView.SCROLL_STATE_DRAGGING) {
                    hideKeyboard()
                }
            }
        })

        (binding.reservationsList.itemAnimator as SimpleItemAnimator).supportsChangeAnimations = false

        binding.filtersIcon.setOnClickListener {
            showConfigurations()
        }

        binding.shiftsFilter.setOnClickListener {
            showShiftsMenu(it)
        }

        binding.createBtn.setOnClickListener {
            if (!vm.permission.boolValue) {
                requireContext().showErrorAlert(
                    resources.getString(R.string.create_reservation),
                    vm.permission.errorMessage
                )
                return@setOnClickListener
            }

            vm.analyticsManager.trackAddReservationFromTabBar()

            val intent = Intent(context, ReservationActivity::class.java)
            intent.putExtra(Constants.TABLE_ID_EXTRA, vm.tableId)
            (requireActivity() as? HomeActivity)?.startActivityIntent?.launch(intent)
        }
    }

    private fun showStatusSelector(reservation: Reservation) {
        val list = Status.statusesList(reservation.status, vm.isFreemium, vm.date ?: Date())
        bottomSheetDialog(list, "", true) {
            val oldStatus = reservation.status
            reservation.status = list.firstOrNull { it.isSelected }?.value as String
            vm.updateReservationStatus(reservation)

            printReservation(oldStatus, reservation)
        }
    }

    private fun observe() {

        vm.searchActive.observe(viewLifecycleOwner) { active ->
            showSearch(active)
        }

        vm.listItems.observe(viewLifecycleOwner) { items ->
            adapter?.submitList(
                items.toMutableList(),
                vm.filterType.value!!,
                vm.reservationCompactMode
            )
        }

        vm.coversCount.observe(viewLifecycleOwner) { covers ->
             activity?.findViewById<TextView>(R.id.totalCovers)?.let {
                 val totalCovers: TextView = it
                 totalCovers.text = covers.toString()
             }
        }

        vm.dataManager.update.observe(viewLifecycleOwner) { update ->
            if (update.contains(ActionType.Conversation.type)) {
                vm.conversations()
            }
        }

        vm.loading.observe(viewLifecycleOwner)  { isLoading ->
            val isListEmpty = vm.listItems.value.isNullOrEmpty()
            binding.run {
                when {
                    isLoading && isListEmpty -> {
                        emptyList.root.visibility = View.GONE
                        reservationsList.visibility = View.INVISIBLE
                        progressGroup.visibility = View.VISIBLE
                    }
                    isLoading && !isListEmpty -> {
                        progressGroup.visibility = View.GONE
                    }
                    !isLoading && isListEmpty -> {
                        emptyList.root.visibility = View.VISIBLE
                        reservationsList.visibility = View.INVISIBLE
                        progressGroup.visibility = View.GONE
                    }
                    else -> {
                        emptyList.root.visibility = View.GONE
                        reservationsList.visibility = View.VISIBLE
                        progressGroup.visibility = View.GONE
                    }
                }
            }
        }

        vm.conversations.observe(viewLifecycleOwner) { conversations ->
            conversations.forEach { conversation ->
                vm.listItems.value?.forEach { item ->
                    if (item is Reservation) {
                        if (item.guest?.id == conversation.guestId && conversation.unreadMessagesCount != item.guest?.unreadMessagesCount) {
                            item.guest?.unreadMessagesCount = conversation.unreadMessagesCount
                            adapter?.notifyItemChanged(vm.listItems.value?.indexOf(item) ?: -1)
                        }
                    }
                }
            }
        }

        vm.scrollTo.observe(viewLifecycleOwner) {
            val targetPosition = if (it > 5) {
                minOf(it + 3, (vm.listItems.value?.size ?: 0) - 1)
            } else { it }

            binding.reservationsList.smoothScrollToPosition(targetPosition)
            Log.d("highlight position", "$targetPosition")

            Timer().schedule(1000) {
                activity?.runOnUiThread {
                    val viewHolder = binding.reservationsList.findViewHolderForAdapterPosition(it)
                    if (viewHolder is ReservationAdapter.ReservationViewHolder) {
                        viewHolder.binding.highlightView.alpha = 1f
                        viewHolder.binding.highlightView.animate()
                            .alpha(0f)
                            .setDuration(1000)
                            .start()
                    }
                }
            }
        }
    }

    fun showSearch(show: Boolean) = with(binding) {
        activity?.runOnUiThread {
            if (show) {
                searchEditText.visibility = View.VISIBLE
                searchIcon.setImageResource(R.drawable.ic_icon_cancel)
                searchEditText.requestFocus()
                showKeyboard()
            } else {
                searchEditText.visibility = View.GONE
                searchIcon.setImageResource(R.drawable.ic_icon_search)
                searchEditText.setText("")
                searchEditText.clearFocus()
                hideKeyboard()
            }
        }
    }

    private fun reservationSelected(reservation: Reservation) {
        if (!vm.permission.boolValue) {
            requireContext().showErrorAlert(
                resources.getString(R.string.edit_reservation),
                vm.permission.errorMessage
            )
            return
        }

        val intent = Intent(context, ReservationActivity::class.java)
        intent.putExtra(Constants.RESERVATION_EXTRA, reservation)
        (requireActivity() as? HomeActivity)?.startActivityIntent?.launch(intent)
    }

    private fun showConfigurations() {
        val list = vm.configurations(requireContext().isTablet)
        bottomSheetDialog(list, "", true) {
            vm.updateConfiguration(it?.value as String)
        }
    }

    fun reservations(reservations: List<Reservation>, date: Date?) {
        try {
            vm.reservations(reservations, date)
        } catch (_: UninitializedPropertyAccessException) {}
    }

    fun reservationForResult(reservation: Reservation?, resultCode: Int) {
        when(resultCode) {
            Constants.RESERVATION_RESULT_UPDATED -> {
                vm.updateReservation(reservation)
            }
            Constants.RESERVATION_RESULT_ADDED -> {
                vm.addReservation(reservation)
            }
            Constants.RESERVATION_RESULT_DELETED -> {
                vm.deleteReservation(reservation)
            }
        }
    }

    private fun showShiftsMenu(view: View) {
        val popupMenu = PopupMenu(requireContext(), view)

        popupMenu.menu.add(R.string.all_shifts)

        vm.shiftsForDay().forEach {
            popupMenu.menu.add(it.name)
        }

        popupMenu.setOnMenuItemClickListener { menuItem ->
            binding.shiftsFilter.title.text = menuItem.toString()
            vm.updateShiftFilter(menuItem.toString())
            true
        }

        popupMenu.show()
    }

    private fun printReservation(oldStatus: String? = null, reservation: Reservation) {

        if (!vm.eatManager.printWhenSeated) return

        if ((oldStatus == null && !Status.isSeated(reservation.status)) || // creating non seated
            (oldStatus != null && Status.isSeated(oldStatus) && Status.isSeated(reservation.status)) || // updating from seated to seated
            (oldStatus != null && !Status.isSeated(reservation.status))) // updating to non seated
            return

        if(!vm.printManager.isConnected) {
            requireContext().showErrorAlert(
                resources.getString(R.string.no_printer_connected_title),
                resources.getString(R.string.no_printer_connected_desc)
            )
            return
        }

        if (vm.eatManager.showChitPrintConfig) {
            bottomSheetFragment(
                resources.getString(R.string.chit_print_config),
                PrinterFragment.newInstance(true), onDismiss = {
                    print(reservation)
                }
            )
        } else {
            print(reservation)
        }
    }

    private fun print(reservation: Reservation) {

        val logo: Bitmap? =
            ContextCompat.getDrawable(requireContext(), R.drawable.ic_logo)?.toBitmap()
                ?.let { Bitmap.createScaledBitmap(it, 120, 60, true) }

        vm.printReservation(reservation, logo)
    }
}
