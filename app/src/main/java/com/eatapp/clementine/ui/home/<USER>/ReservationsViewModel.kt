package com.eatapp.clementine.ui.home.overview

import SingleLiveEvent
import android.graphics.Bitmap
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.eatapp.clementine.data.network.response.conversation.Conversation
import com.eatapp.clementine.data.network.response.reservation.Reservation
import com.eatapp.clementine.data.network.response.restaurant.AccountStateType
import com.eatapp.clementine.data.network.response.shift.Shift
import com.eatapp.clementine.data.network.response.user.Permission
import com.eatapp.clementine.data.repository.MessagingRepository
import com.eatapp.clementine.data.repository.ReservationsRepository
import com.eatapp.clementine.data.repository.ServersRepository
import com.eatapp.clementine.enums.Configuration
import com.eatapp.clementine.internal.ReservationGroupSection
import com.eatapp.clementine.internal.ReservationSection
import com.eatapp.clementine.internal.SelectorItem
import com.eatapp.clementine.internal.Status
import com.eatapp.clementine.internal.asLiveData
import com.eatapp.clementine.internal.ceilToQuarter
import com.eatapp.clementine.internal.managers.AnalyticsManager
import com.eatapp.clementine.internal.managers.DataManager
import com.eatapp.clementine.internal.managers.EatManager
import com.eatapp.clementine.internal.managers.PrintManager
import com.eatapp.clementine.internal.managers.manageReservations
import com.eatapp.clementine.internal.managers.waitListTimer
import com.eatapp.clementine.ui.base.BaseViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import java.util.Date
import java.util.Timer
import javax.inject.Inject
import kotlin.concurrent.schedule

@Suppress("UNCHECKED_CAST")
@HiltViewModel
class ReservationsViewModel @Inject constructor(
    val eatManager: EatManager,
    val dataManager: DataManager,
    val analyticsManager: AnalyticsManager,
    val printManager: PrintManager,
    private val reservationsRepository: ReservationsRepository,
    private val messagingRepository: MessagingRepository,
    private val serversRepository: ServersRepository
) : BaseViewModel() {

    private var reservations: List<Reservation> = mutableListOf()
    private var sectionList: List<ReservationGroupSection>? = mutableListOf()
    var date: Date? = null
    var tableId: String? = null

    private var searchQuery: String = ""

    val posActive: Boolean = eatManager.restaurant()?.posActive == true
    val paymentsActive: Boolean = eatManager.restaurant()?.paymentsActivated == true
    val isFreemium = eatManager.accountState() == AccountStateType.IN_HOUSE

    var permission: Permission = eatManager.permission(identifier = manageReservations)
    var timerPermission: Permission = eatManager.permission(identifier = waitListTimer)

    val reservationCompactMode: Boolean
        get() = eatManager.reservationCompactMode

    enum class FilterType {
        ALL, SEATED, UPCOMING, SEATED_UPCOMING
    }

    enum class StatusType {
        RESERVATION, WAITLIST, FLOOR
    }

    private val _type = MutableLiveData<StatusType>()
    val type: LiveData<StatusType> by lazy {
        _type.asLiveData()
    }

    private val _filterType = MutableLiveData<FilterType>()
    val filterType: LiveData<FilterType> by lazy {
        _filterType.asLiveData()
    }

    private val _searchActive = MutableLiveData<Boolean>()
    val searchActive: LiveData<Boolean> by lazy {
        _searchActive.asLiveData()
    }

    private val _listItems = MutableLiveData<List<Any>>()
    val listItems: LiveData<List<Any>> by lazy {
        _listItems.asLiveData()
    }

    private val _coversCount = MutableLiveData<Int>()
    val coversCount: LiveData<Int> by lazy {
        _coversCount.asLiveData()
    }

    private val _conversations = MutableLiveData<List<Conversation>>()
    val conversations: LiveData<List<Conversation>> by lazy {
        _conversations.asLiveData()
    }

    private val _scrollTo = SingleLiveEvent<Int>()
    val scrollTo: LiveData<Int> by lazy {
        _scrollTo.asLiveData()
    }

    private var all: MutableList<Any> = mutableListOf()
    private var seated: List<Any> = listOf()
    private var upcoming: List<Any> = listOf()
    private var seatedupcoming: List<Any> = listOf()

    private var closedGroups = mutableListOf<ReservationGroupSection>()
    private var closedSections = mutableListOf<ReservationSection>()
    private var pendingActions = mutableListOf<Reservation>()

    init {

        _type.value = StatusType.RESERVATION
        _searchActive.value = false
        _filterType.value = FilterType.ALL

        servers()
        conversations()

        Timer().schedule(300) {
            if (type.value != StatusType.FLOOR) loading(true)
        }
    }

    fun type(t: StatusType) {
        _type.value = t
    }

    fun reservations(reservations: List<Reservation>, date: Date?) {

        this.date = date

        this.reservations = reservations.map { reservation ->
            pendingActions.find { it.id == reservation.id } ?: reservation
        }

        pendingActions.removeAll { pending ->
            reservations.any { it.id == pending.id && it.status == pending.status }
        }

        Timer().schedule(100) {
            createGroups()
        }
    }

    private fun createGroups() {
        if (_type.value == StatusType.WAITLIST) {

            all.clear()

            all.addAll(reservations.filter { it.status == Status.Value.WAITLIST.code }
                .sortedBy { it.attributes.waitlistPosition })

        } else {

            val rvsrs = reservations.filter { it.status != Status.Value.WAITLIST.code }
                .sortedBy { it.startTime }

            sectionList = if (!eatManager.reservationsConfig.groupByActivated) {

                all.clear()

                all.addAll(eatManager.sortReservations(rvsrs))
                null

            } else {

                when (type.value) {
                    StatusType.RESERVATION -> eatManager.groupReservations(
                        rvsrs,
                        date,
                        eatManager.reservationsConfig.groupBy,
                        closedGroups,
                        closedSections
                    )

                    else -> eatManager.groupReservations(
                        rvsrs,
                        date,
                        Configuration.LIFECYCLE,
                        closedGroups,
                        closedSections
                    )
                }
            }

            try {
                makeFinalList()
            } catch (_: ConcurrentModificationException) {}
        }

        filterReservations()
    }

    private fun makeFinalList() {

        sectionList?.let {

            all.clear()

            it.forEach { sectionGroup ->

                all.add(sectionGroup)

                if (sectionGroup.expanded) {
                    if (type.value == StatusType.FLOOR) {
                        all.addAll(sectionGroup.items.flatMap { eatManager.sortReservations(it.items) })
                    } else {
                        all.addAll(sectionGroup.items.flatMap {
                            listOf(it) + if (it.expanded) eatManager.sortReservations(it.items) else emptyList()
                        })
                    }
                }
            }
        }

        seated = all.filter { item ->
            (item is ReservationSection && item.items.find { Status.isSeated(it.status) } != null)
                    || (item is Reservation && Status.isSeated(item.status) || (item is ReservationGroupSection && item.items.any { it.items.any { Status.isSeated(it.status) } }))
        }

        upcoming = all.filter { item ->
            (item is ReservationSection && item.items.find { Status.isUpcomingUi(it.status) } != null)
                    || (item is Reservation && Status.isUpcomingUi(item.status) || (item is ReservationGroupSection && item.items.any { it.items.any { Status.isUpcomingUi(it.status) } }))
        }

        seatedupcoming = all.filter { item ->
            (item is ReservationSection && (
                    (item.items.find { Status.isSeated(it.status) } != null) ||
                            (item.items.find { Status.isUpcomingUi(it.status) } != null))
                    || (item is Reservation && (Status.isSeated(item.status) || Status.isUpcomingUi(
                item.status
            ))) || (item is ReservationGroupSection && ((item.items.any{ it.items.any { Status.isUpcomingUi(it.status)}}) || (item.items.any{ it.items.any { Status.isSeated(it.status)}}))))
        }
    }

    private fun filterReservations() {

        lateinit var items: MutableList<Any>

        when (_filterType.value) {
            FilterType.ALL -> items = all.toMutableList()
            FilterType.SEATED -> items = seated.toMutableList()
            FilterType.UPCOMING -> items = upcoming.toMutableList()
            FilterType.SEATED_UPCOMING -> items = seatedupcoming.toMutableList()
            else -> {}
        }

        if (eatManager.reservationsConfig.shiftFilter.isNotEmpty() &&
            eatManager.reservationsConfig.shiftFilter != Configuration.ALL_SHIFTS.title) {

            val filteredShift: Shift? = eatManager.shiftForDayWithReservations(reservations, date).firstOrNull {
                it.name == eatManager.reservationsConfig.shiftFilter
            }

            items = items.filter { item ->
                when (item) {
                    is ReservationSection -> {
                        item.items.any { filteredShift?.reservations?.contains(it) ?: false }
                    }
                    is Reservation -> {
                        filteredShift?.reservations?.contains(item) ?: false
                    }
                    is ReservationGroupSection -> {
                        item.items.any { it.items.any { filteredShift?.reservations?.contains(it) ?: false } }
                    }
                    else -> false
                }
            }.toMutableList()
        }

        if (searchQuery.isBlank()) {
            _listItems.postValue(items)
            updateCovers(items)
            loading(false)
            return
        }

        items = items.filter { item ->
            when (item) {
                is ReservationSection -> {
                    item.items = item.items.filter { applyFilter(it) }
                    item.items.isNotEmpty()
                }
                is Reservation -> {
                    applyFilter(item)
                }
                is ReservationGroupSection -> {
                    item.items.any { it.items.any { applyFilter(it) } }
                }
                else -> false
            }
        }.toMutableList()

        updateCovers(items)

        _listItems.postValue(items)
        loading(false)
    }

    private fun applyFilter(it: Reservation): Boolean {
        val guest = it.guest
        val fullName = "${guest?.firstName} ${guest?.lastName}".lowercase()
        val tempName = it.guestName.lowercase()
        val phone = guest?.phone?.lowercase()
        val email = guest?.email?.lowercase()
        val key = it.key?.lowercase()
        val tables = it.tableS.lowercase()

        val searchLowerCase = searchQuery.lowercase().trim()

        return fullName.contains(searchLowerCase) ||
                tempName.contains(searchLowerCase) ||
                phone?.contains(searchLowerCase) ?: false ||
                email?.contains(searchLowerCase) ?: false ||
                key?.contains(searchLowerCase) ?: false ||
                tables.contains(searchLowerCase)
    }

    private fun updateCovers(items: MutableList<Any>) {

        if (_type.value == StatusType.WAITLIST) return

        var coversCount = 0

        val reservations = if (eatManager.reservationsConfig.groupByActivated) {
            items.filterIsInstance<ReservationGroupSection>()
                .flatMap { it.items }
                .flatMap { it.items }
        } else {
            items.filterIsInstance<Reservation>()
        }

        reservations.forEach { reservation ->
            coversCount = incrementCoverCount(reservation, coversCount)
        }

        _coversCount.postValue(coversCount)
    }

    private fun incrementCoverCount(reservation: Reservation, covers: Int): Int {

        var count = covers

        when (_filterType.value) {
            FilterType.ALL -> if (Status.isSeated(reservation.status) || Status.isUpcomingUi(reservation.status)
                || Status.isFinished(reservation.status)
            ) count += reservation.covers
            FilterType.SEATED -> if (Status.isSeated(reservation.status)) count += reservation.covers
            FilterType.UPCOMING -> if (Status.isUpcomingUi(reservation.status)) count += reservation.covers
            FilterType.SEATED_UPCOMING -> if (Status.isSeated(reservation.status)
                || Status.isUpcomingUi(reservation.status)
            ) count += reservation.covers
            else -> {}
        }

        return count
    }

    fun onTextChanged(s: CharSequence, start: Int, before: Int, count: Int) {
        searchQuery = s.toString()
        createGroups()
    }

    fun statusFilterChange(type: FilterType) {

        if (_filterType.value == FilterType.SEATED && type == FilterType.UPCOMING) {
            _filterType.value = FilterType.SEATED_UPCOMING
        } else if (_filterType.value == FilterType.UPCOMING && type == FilterType.SEATED) {
            _filterType.value = FilterType.SEATED_UPCOMING
        } else if (_filterType.value == FilterType.SEATED_UPCOMING && type == FilterType.UPCOMING) {
            _filterType.value = FilterType.SEATED
        } else if (_filterType.value == FilterType.SEATED_UPCOMING && type == FilterType.SEATED) {
            _filterType.value = FilterType.UPCOMING
        } else if (_filterType.value == FilterType.ALL) {
            _filterType.value = type
        } else {
            _filterType.value = FilterType.ALL
        }

        filterReservations()
    }

    fun searchBtnClick() {
        _searchActive.value = _searchActive.value!!.not()
    }

    fun expandCollapseGroup(groupSection: ReservationGroupSection) {
        val index = closedGroups.indexOfFirst { groupSection.title == it.title && groupSection.fakeId == it.fakeId }
        if (index != -1) {
            closedGroups.removeAt(index)
        } else {
            closedGroups.add(groupSection)
        }
        sectionList = sectionList?.map { section ->
            if (section.title == groupSection.title && section.fakeId == groupSection.fakeId) {
                section.copy(
                    expanded = !section.expanded
                )
            } else {
                section.copy()
            }
        }

        all.clear()
        makeFinalList()
        filterReservations()
    }

    fun expandCollapseSection(section: ReservationSection) {
        val index = closedSections.indexOfFirst { section.groupName == it.groupName && section.fakeId == it.fakeId && section.title == it.title }

        if (index != -1) {
            closedSections.removeAt(index)
        } else {
            closedSections.add(section)
        }
        
        val sectionGroup = sectionList?.firstOrNull { it.title == section.groupName && it.fakeId == section.groupId }
        if (sectionGroup == null) {
            return
        }

        sectionGroup.items = sectionGroup.items.map { reservationSection ->
            if (reservationSection.title == section.title && reservationSection.fakeId == section.fakeId) {
                reservationSection.copy(expanded = !reservationSection.expanded)
            } else {
                reservationSection.copy()
            }
        }.toMutableList()

        makeFinalList()
        filterReservations()
    }

    fun configurations(isTablet: Boolean): MutableList<SelectorItem> {

        val group = mutableListOf<SelectorItem>()

        group(
            Configuration.groupBy, "Group by", group,
            if (eatManager.reservationsConfig.groupByActivated) {
                eatManager.reservationsConfig.groupBy.value
            } else {
                Configuration.NONE.value
            }
        )
        group(Configuration.sortBy, "Sort by", group, eatManager.reservationsConfig.sortBy.value)

        if (isTablet) {
            val selected =
                if (eatManager.reservationCompactMode) Configuration.RESERVATION_COMPACT_MODE.value else Configuration.RESERVATION_DETAIL_MODE.value
            group(Configuration.reservationViewMode, "Reservation view mode", group, selected)
        }

        return group
    }

    private fun group(
        group: ArrayList<Configuration>,
        header: String,
        items: MutableList<SelectorItem>,
        selected: String?
    ) {

        val item = SelectorItem(
            "",
            header,
            null,
            isSelected = false,
            isHeader = true,
            isDisabled = false
        )
        items.add(item)

        group.forEach { s ->
            val item = SelectorItem(
                "",
                s.title,
                s.value,
                null,
                color = 0,
                isSelected = s.value == selected,
                isHeader = false,
                isDisabled = false
            )
            items.add(item)
        }
    }

    fun updateConfiguration(s: String) {

        Configuration.groupBy.firstOrNull { it.value == s }?.let {

            if (it == Configuration.NONE) {
                eatManager.reservationsConfig.groupByActivated = false
            } else {
                eatManager.reservationsConfig.groupByActivated = true
                eatManager.reservationsConfig.groupBy = it
            }
        }

        Configuration.sortBy.firstOrNull { it.value == s }?.let {
            eatManager.reservationsConfig.sortBy = it
        }

        Configuration.reservationViewMode.firstOrNull { it.value == s }?.let {
            eatManager.reservationCompactMode = it == Configuration.RESERVATION_COMPACT_MODE
        }

        reservations(reservations, date)
    }

    fun updateShiftFilter(s: String) {
        eatManager.reservationsConfig.shiftFilter = s
        reservations(reservations, date)
    }

    fun trackReservationViewMode() {
        if (!eatManager.reservationDetailModeTracked) {
            eatManager.reservationDetailModeTracked = true
            analyticsManager.reservationListViewDetailMode()
        }
    }

    fun updateReservation(reservation: Reservation?) {
        reservation?.let { r ->
            val rsrv = reservations.toMutableList()
                .map { if (it.id == r.id) reservation else it }
            reservations(rsrv, date)
        }
    }

    fun addReservation(reservation: Reservation?) {
        reservation?.let {
            val rsrv = reservations.toMutableList()
            rsrv.add(reservation)
            reservations(rsrv, date)

            Timer().schedule(200) {
                _listItems.value?.indexOf(reservation)?.let {
                    if (it != -1)
                        _scrollTo.postValue(_listItems.value?.indexOf(reservation))
                }

            }
        }
    }

    fun deleteReservation(reservation: Reservation?) {
        reservation?.let {
            val rsrv = reservations.toMutableList().filter { it.id != reservation.id }
            reservations(rsrv, date)
        }
    }

    fun updateReservationStatus(reservation: Reservation) {

        updateStatusLocally(reservation)

        if (reservation.status == Status.Value.WAITLIST.code) reservation.waitQuote.let {
            eatManager.waitQuote(
                it
            )
        }

        if (Status.isSeated(reservation.status)) reservation.startTime = ceilToQuarter(Date())

        val body = reservation.toReservationBody(
            emptyList(),
            emptyList()
        )

        launch({
            reservationsRepository.updateReservation(
                eatManager.restaurantId(),
                reservationId = reservation.id,
                body)
            analyticsManager.trackReservationStatus()
        })
    }

    fun shiftsForDay() : List<Shift> {
        return eatManager.shiftForDay(date ?: Date()).filter {
            it.id != "empty"
        }
    }

    fun printReservation(reservation: Reservation, logo: Bitmap?) {
        printManager.printText(reservation, eatManager.servers(), logo, eatManager)
    }

    fun servers() {

        launch({

            val response = serversRepository.servers()
            eatManager.servers(servers = response.servers)

        }, false)
    }

    fun conversations(restaurantId: String = eatManager.restaurantId()) {

        launch({

            val response = messagingRepository.conversations()
            eatManager.conversations(restaurantId, response.conversations)

            _conversations.postValue(response.conversations)

        }, true)
    }

    fun refreshConversations() {
        eatManager.conversations()?.let {
            _conversations.postValue(it)
        }
    }

    private fun updateStatusLocally(reservation: Reservation) {
        reservations.firstOrNull { it.id == reservation.id }?.status = reservation.status
        createGroups()
        pendingActions.add(reservation)
    }
}