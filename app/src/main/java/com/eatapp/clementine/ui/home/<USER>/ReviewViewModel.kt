package com.eatapp.clementine.ui.home.reports

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.eatapp.clementine.data.network.response.reservation.Reservation
import com.eatapp.clementine.internal.asLiveData
import com.eatapp.clementine.ui.base.BaseViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject

@HiltViewModel
open class ReviewViewModel @Inject constructor() : BaseViewModel() {

    private val _reservation = MutableLiveData<Reservation>()
    val reservation: LiveData<Reservation> by lazy {
        _reservation.asLiveData()
    }

    fun reservation(reservation: Reservation) {
        _reservation.postValue(reservation)
    }
}
