package com.eatapp.clementine.ui.home.reports

import android.util.Log
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.eatapp.clementine.data.network.response.reservation.Reservation
import com.eatapp.clementine.data.repository.ReservationsRepository
import com.eatapp.clementine.internal.asLiveData
import com.eatapp.clementine.internal.managers.DataManager
import com.eatapp.clementine.internal.managers.EatManager
import com.eatapp.clementine.internal.startOfTheEatDay
import com.eatapp.clementine.ui.base.BaseViewModel
import com.eatapp.clementine.ui.home.overview.DatesViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Job
import java.util.*
import javax.inject.Inject
import kotlin.concurrent.fixedRateTimer

@HiltViewModel
open class ReviewsViewModel @Inject constructor(
    val dataManager: DataManager,
    private val reservationsRepository: ReservationsRepository
) : DatesViewModel() {

    private var job: Job? = null
    private var searchQuery: String = ""

    private var all: MutableList<Reservation> = mutableListOf()

    private val _reservations = MutableLiveData<Pair<Boolean, List<Reservation>>>()
    val reservations: LiveData<Pair<Boolean, List<Reservation>>> by lazy {
        _reservations.asLiveData()
    }

    init {

        loading(true)
        initTimers()
    }

    fun initTimers() {

        if (!dataManager.isSubscribed() && !dataManager.isHydraEnabled()) {

            timers.add(fixedRateTimer("default", false, 5000L, 20000L) {
                reservations(true)
            })
        }
    }

    fun updateDate() {

        job?.cancel()

        Log.v("current_date: ", date.value.toString())

        reservations(false)
    }

    fun reservations(poll: Boolean) {

        if ((paused && poll)) return

        job = launch({

            val response = reservationsRepository.reservations(date.value!!)

            response.reservations.filter {
                it.review?.capturedAt != null
            }.also {
                all = it.toMutableList()
                filterReviews()
            }

        }, poll)
    }

    private fun filterReviews() {

        loading(false)

        val items: MutableList<Reservation> = all.toMutableList()

        val res = items.filter { item ->
            (item.guest?.firstName?.lowercase(Locale.ROOT)?.contains(searchQuery.lowercase(Locale.ROOT).trim()) == true)
                    || (item.guest?.lastName?.lowercase(Locale.ROOT)
                ?.contains(searchQuery.lowercase(Locale.ROOT).trim()) == true) ||
                    (String.format(
                        "%s %s", item.guest?.firstName?.lowercase(Locale.ROOT),
                        item.guest?.lastName?.lowercase(Locale.ROOT)
                    ).contains(searchQuery.lowercase(Locale.ROOT).trim())) ||
                    (item.guest?.phone?.lowercase(Locale.ROOT)?.contains(searchQuery.lowercase(Locale.ROOT).trim()) == true) ||
                    (item.guest?.email?.lowercase(Locale.ROOT)?.contains(searchQuery.lowercase(Locale.ROOT).trim()) == true)
        }

        _reservations.postValue(Pair(items.isEmpty(), res.sortedBy { it.startTime }))
    }

    fun onTextChanged(s: CharSequence, start: Int, before: Int, count: Int) {
        searchQuery = s.toString()
        filterReviews()
    }
}
