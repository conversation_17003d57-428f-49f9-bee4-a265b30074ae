package com.eatapp.clementine.ui.home.more.guests

import android.os.Bundle
import android.view.View
import com.eatapp.clementine.R
import com.eatapp.clementine.databinding.ActivityGuestsBinding
import com.eatapp.clementine.ui.base.BaseActivity
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class GuestsActivity : BaseActivity<ActivityGuestsBinding>() {

    override fun getLayoutId() = R.layout.activity_guests

    override fun onCreated(savedInstanceState: Bundle?) {
        binding.backBtnRS.setOnClickListener {
            finish()
        }
    }

    override fun onResume() {
        super.onResume()

        val navHostFragment = supportFragmentManager.findFragmentById(R.id.nav_host_fragment)
        val fragment = navHostFragment?.childFragmentManager?.fragments?.get(0) as GuestsFragment
        fragment.vm.reservationMode.value = false
        fragment.binding.appBarLayout.visibility = View.GONE
    }
}