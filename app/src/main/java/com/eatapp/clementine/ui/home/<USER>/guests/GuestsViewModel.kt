package com.eatapp.clementine.ui.home.more.guests

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.eatapp.clementine.data.network.response.guest.Guest
import com.eatapp.clementine.data.network.response.guest.GuestsResponse
import com.eatapp.clementine.data.network.response.user.Permission
import com.eatapp.clementine.data.repository.FabricateRepository
import com.eatapp.clementine.data.repository.GuestsRepository
import com.eatapp.clementine.internal.Constants
import com.eatapp.clementine.internal.asLiveData
import com.eatapp.clementine.internal.managers.AnalyticsManager
import com.eatapp.clementine.internal.managers.DataManager
import com.eatapp.clementine.internal.managers.EatManager
import com.eatapp.clementine.internal.managers.FeatureFlagsManager
import com.eatapp.clementine.internal.managers.manageGuests
import com.eatapp.clementine.ui.base.BaseViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.delay
import javax.inject.Inject

@HiltViewModel
class GuestsViewModel @Inject constructor(
    private val eatManager: EatManager,
    val dataManager: DataManager,
    val analyticsManager: AnalyticsManager,
    val featureFlagsManager: FeatureFlagsManager,
    private val guestsRepository: GuestsRepository,
    private val fabricateRepository: FabricateRepository
) : BaseViewModel() {

    var em: EatManager = eatManager
    var permission: Permission = eatManager.permission(identifier = manageGuests)

    private var page = 1
    private var maxPage = 1

    private var paging = false
    private var refreshing = false
    private var reservationId: String = ""

    var preselectGuest: Boolean = false

    private var list: MutableList<Guest> = mutableListOf()

    var search = MutableLiveData<String>()
    var reservationMode = MutableLiveData(false)

    var searchText = "initial"

    private val _guests = MutableLiveData<MutableList<Guest>>()
    val guests: LiveData<MutableList<Guest>> by lazy {
        _guests.asLiveData()
    }

    private val _empty = MutableLiveData<Boolean>()
    val empty: LiveData<Boolean> by lazy {
        _empty.asLiveData()
    }

    init {
        analyticsManager.trackViewGuestView()
        reservationId = eatManager.restaurantId()
        search.postValue("")
    }

    fun paginateGuests() {
        page++
        getGuests(searchText, 30, true)
    }

    fun reloadList() {

        if (reservationId != eatManager.restaurantId()) {
            searchGuests("")
            reservationId = eatManager.restaurantId()
        }
    }

    fun refreshGuests() {

        if (page > 3) return

        page = 1

        guests.value?.let {
            getGuests(searchText, it.count { g -> !g.isHeader }, false)
            refreshing = true
        }
    }

    private fun getGuests(query: String, limit: Int, isPagination: Boolean) {

        if (paging) {
            page--
            return
        }

        if (refreshing || maxPage in 1 until page) return

        paging = isPagination

        var queryText: String? = query.trim()

        if (guests.value == null || guests.value?.isEmpty() == true) loading(true)

        searchText = queryText ?: ""

        launch ({

            if (!paging) delay(1000)  //debounce timeOut

            if (queryText != searchText) return@launch

            if (query.isNotBlank() && !paging) analyticsManager.trackSearchGuests()

            if (queryText == "") queryText = null

            val lim = if (limit == 0) 30 else limit

            val response = guestsRepository.guests(queryText, page, lim)
            processGuests(response)

            loading(false)

        }, false)
    }

    private fun processGuests(response: GuestsResponse) {
        if (refreshing) {
            page = response.guests.count()/30
            maxPage = response.guests.count()/30 + 1
            list.clear()
        } else {
            maxPage = response.meta.totalPages
        }

        list.addAll(response.guests)

        mapGuests()

        paging = false
        refreshing = false
    }

    private fun mapGuests() {

        val listG = list.toMutableList()

        if (searchText != "") {
            _guests.postValue(listG)
            _empty.postValue(listG.isEmpty())
            return
        }

        //listG.sortWith(compareBy { it.firstName?.trim()?.lowercase() })

        val mapped = listG.groupBy { guest -> guest.firstName?.firstOrNull()?.uppercaseChar() }

        val listF = mutableListOf<Guest>()

        for (map in mapped) {

            listF.add(Guest(map.key.toString(), true))
            map.value.forEach { g -> listF.add(g) }
        }

        _guests.postValue(listF)
        _empty.postValue(listF.isEmpty())
    }

    fun searchGuests(query: String) {

        page = 1
        list.clear()

        _guests.postValue(mutableListOf())
        _empty.postValue(false)

        getGuests(query, 30, false)
    }

    fun updateGuest(resultCode: Int, updatedGuest: Guest?) {

        list.forEachIndexed { i, guest ->
            if (updatedGuest?.id == guest.id) {
                when (resultCode) {
                    Constants.GUEST_RESULT_UPDATED -> {
                        list[i] = updatedGuest
                    }
                    Constants.GUEST_RESULT_ADDED -> list.add(updatedGuest)
                    Constants.GUEST_RESULT_DELETED -> list.remove(guest)
                }
            }
        }

        mapGuests()
    }

    fun restaurant() {

        launch ({

            val response = fabricateRepository.restaurant(eatManager.restaurantId())
            eatManager.restaurant(response)

        }, true)
    }
}
