package com.eatapp.clementine.ui.launch

import android.content.Intent
import android.view.View
import androidx.activity.OnBackPressedCallback
import com.eatapp.clementine.databinding.FabricateFragmentBinding
import com.eatapp.clementine.internal.Constants.RESERVATION_KEY_EXTRA
import com.eatapp.clementine.ui.base.BaseFragment
import com.eatapp.clementine.ui.home.HomeActivity
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class FabricateFragment : BaseFragment<FabricateViewModel, FabricateFragmentBinding>() {

    override fun inflateLayout() = FabricateFragmentBinding.inflate(layoutInflater)

    override fun viewModelClass() = FabricateViewModel::class.java

    override fun viewCreated() {
        vm.initData()
        observe()

        // Prevents leaving screen while resources are loading
        requireActivity()
            .onBackPressedDispatcher
            .addCallback(viewLifecycleOwner, object : OnBackPressedCallback(true) {
                override fun handleOnBackPressed() {}
            })
    }

    override fun onShowLockdownView(view: View) {}

    override fun onHideLockdownView(view: View) {}

    private fun observe() {
        progress = binding.progress

        vm.loading.observe(viewLifecycleOwner) {
            if (!it) {
                navigateToHome()
            }
        }
    }

    private fun navigateToHome() {
        val intent = Intent(activity, HomeActivity::class.java)

        (activity as? LaunchActivity)?.intent?.getStringExtra(RESERVATION_KEY_EXTRA)?.let {
            intent.putExtra(RESERVATION_KEY_EXTRA, it)
        }

        startActivity(intent)
        requireActivity().finish()

    }
}
