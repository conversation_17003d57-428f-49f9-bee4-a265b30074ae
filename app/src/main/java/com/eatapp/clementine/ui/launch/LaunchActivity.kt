package com.eatapp.clementine.ui.launch

import android.annotation.SuppressLint
import android.os.Bundle
import android.view.View
import androidx.core.content.ContextCompat
import com.eatapp.clementine.R
import com.eatapp.clementine.databinding.ActivityLaunchBinding
import com.eatapp.clementine.internal.Constants.RESERVATION_KEY_EXTRA
import com.eatapp.clementine.ui.base.BaseActivity
import dagger.hilt.android.AndroidEntryPoint

@SuppressLint("CustomSplashScreen")
@AndroidEntryPoint
class LaunchActivity : BaseActivity<ActivityLaunchBinding>() {

    override fun getLayoutId() = R.layout.activity_launch

    override fun onCreated(savedInstanceState: Bundle?) {
        (binding.root.parent as? View)?.background =
            ContextCompat.getDrawable(applicationContext, R.drawable.gradient_green_bcg)
    }

    @Deprecated("Deprecated in Java")
    @SuppressLint("MissingSuperCall")
    override fun onBackPressed() {}
}
