package com.eatapp.clementine.ui.launch

import android.view.View
import androidx.lifecycle.ViewModelProviders
import androidx.navigation.fragment.findNavController
import com.eatapp.clementine.R
import com.eatapp.clementine.adapter.EatPagerAdapter
import com.eatapp.clementine.databinding.OnboardingFragmentBinding
import com.eatapp.clementine.ui.base.BaseFragment
import com.google.android.material.tabs.TabLayoutMediator
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class OnboardingFragment : BaseFragment<OnboardingViewModel, OnboardingFragmentBinding>() {

    override fun viewModelClass() = OnboardingViewModel::class.java

    override fun inflateLayout() = OnboardingFragmentBinding.inflate(layoutInflater)

    override fun viewCreated() {

        observe()

        if (vm.checkToken()) return

        val formAdapter = EatPagerAdapter(requireActivity())
        formAdapter.addFragment(SignUpFragment(), resources.getString(R.string.sign_up))
        formAdapter.addFragment(SignInFragment(), resources.getString(R.string.sign_in))

        binding.formPager.adapter = formAdapter
        TabLayoutMediator(binding.formTabLayout, binding.formPager) { tab, position ->
            tab.text = formAdapter.getPageTitle(position)
        }.attach()

        val carouselAdapter = EatPagerAdapter(requireActivity())
        carouselAdapter.addFragment(
            CarouselFragment.newInstance(
                R.string.carousel_title_one,
                R.string.carousel_desc_one, R.drawable.carousel_one
            ), ""
        )
        carouselAdapter.addFragment(
            CarouselFragment.newInstance(
                R.string.carousel_title_two,
                R.string.carousel_desc_two, R.drawable.carousel_two
            ), ""
        )
        carouselAdapter.addFragment(
            CarouselFragment.newInstance(
                R.string.carousel_title_three,
                R.string.carousel_desc_three, R.drawable.carousel_three
            ), ""
        )
        carouselAdapter.addFragment(
            CarouselFragment.newInstance(
                R.string.carousel_title_four,
                R.string.carousel_desc_four, R.drawable.carousel_four
            ), ""
        )
        carouselAdapter.addFragment(
            CarouselFragment.newInstance(
                R.string.carousel_title_five,
                R.string.carousel_desc_five, R.drawable.carousel_five
            ), ""
        )

        binding.run {
            carouselPager.adapter = carouselAdapter
            TabLayoutMediator(carouselTabLayout, carouselPager) { tab, position ->
                tab.text = carouselAdapter.getPageTitle(position)
            }.attach()

            main.viewTreeObserver.addOnGlobalLayoutListener {

                when (main.rootView.height - main.height) {
                    in 0..500 -> {
                        carousel.animate().alpha(1f)
                        carousel.visibility = View.VISIBLE
                    }
                    else -> {
                        carousel.alpha = 0f
                        carousel.visibility = View.GONE
                        onboardingScrollview.smoothScrollTo(0, 0)
                    }
                }
            }
        }
    }

    override fun onShowLockdownView(view: View) {}

    override fun onHideLockdownView(view: View) {}

    private fun observe() {

        vm.authentication.observe(viewLifecycleOwner) { state ->
            checkAuthStatus(state)
        }

        ViewModelProviders.of(requireActivity()).get(SharedLaunchViewModel::class.java)
            .authentication.observe(viewLifecycleOwner) { state ->
                checkAuthStatus(state)
            }
    }

    private fun checkAuthStatus(state: SharedLaunchViewModel.AuthenticationState?) {

        when (state) {
            SharedLaunchViewModel.AuthenticationState.AUTHENTICATED -> {
                navigateToFabricate()
            }
            SharedLaunchViewModel.AuthenticationState.UNAUTHENTICATED -> {
                binding.onboardingScrollview.animate().setDuration(500.toLong()).alpha(1f)
                binding.topShape.animate().setDuration(500.toLong()).alpha(1f)
            }
            else -> {}
        }
    }

    private fun navigateToFabricate() {

        vm.registerUser()

        val action = OnboardingFragmentDirections.fabricateAction()
        action.let { this.findNavController().navigate(it) }
    }
}
