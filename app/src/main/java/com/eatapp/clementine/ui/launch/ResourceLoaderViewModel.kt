package com.eatapp.clementine.ui.launch

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.eatapp.clementine.data.network.response.apiresources.ApiResource
import com.eatapp.clementine.data.repository.ApiResourcesRepository
import com.eatapp.clementine.internal.BookingSource
import com.eatapp.clementine.internal.Endpoints
import com.eatapp.clementine.internal.asLiveData
import com.eatapp.clementine.internal.managers.EatManager
import com.eatapp.clementine.ui.base.BaseViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject

@HiltViewModel
class ResourceLoaderViewModel @Inject constructor(
    private val resourcesRepository: ApiResourcesRepository,
    private val eatManager: EatManager
) : BaseViewModel() {

    private val _isResourceLoaded = MutableLiveData<Boolean>()
    val isResourceLoaded: LiveData<Boolean> by lazy {
        _isResourceLoaded.asLiveData()
    }

    init {
        loadResources()
    }

    private fun loadResources() {

        loading(true)

        launch({
            val response = resourcesRepository.resources().apiResource

            saveData(response = response)

            _isResourceLoaded.postValue(true)
        })
    }

    private fun saveData(response: ApiResource) {

        eatManager.navItems = response.attributes.navigation
        eatManager.countries(response.attributes.countries)

        BookingSource.sources = response.attributes.sources

        Endpoints.restaurantEndpoint =
            response.attributes.endpoints.restaurantEndpoint.v2.baseUrl + "/"
        Endpoints.messagingEndpoint =
            response.attributes.endpoints.messagingEndpoint.v2.baseUrl + "/"
        Endpoints.paymentsEndPoint =
            response.attributes.endpoints.paymentServicesEndpoint.v2.baseUrl + "/"
        Endpoints.hubspotEndpoint =
            response.attributes.endpoints.hubspotServicesEndpoint.v2.baseUrl + "/"
        Endpoints.posEndPoint =
            response.attributes.endpoints.posServicesEndpoint.v2.baseUrl + "/"
        Endpoints.websocketConnectionEndpoint =
            response.attributes.endpoints.websocketEndpoint.connectionUrl
        Endpoints.canaryEndpoint =
            response.attributes.endpoints.websocketEndpoint.canaryUrl.replace("canary", "")
        Endpoints.hydraEndpoint =
            response.attributes.endpoints.websocketEndpoint.hydraUrl.replace("hydra", "")
        Endpoints.adminEndpoint =
            response.attributes.endpoints.adminEndpoint.baseUrl
    }
}