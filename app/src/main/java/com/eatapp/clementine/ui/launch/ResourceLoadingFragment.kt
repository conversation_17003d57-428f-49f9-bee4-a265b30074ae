package com.eatapp.clementine.ui.launch

import android.view.View
import androidx.navigation.fragment.findNavController
import com.eatapp.clementine.databinding.FragmentResourceLoadingBinding
import com.eatapp.clementine.ui.base.BaseFragment
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class ResourceLoadingFragment :
    BaseFragment<ResourceLoaderViewModel, FragmentResourceLoadingBinding>() {

    override fun inflateLayout() = FragmentResourceLoadingBinding.inflate(layoutInflater)

    override fun viewModelClass() = ResourceLoaderViewModel::class.java

    override fun onShowLockdownView(view: View) {}

    override fun onHideLockdownView(view: View) {}

    override fun viewCreated() {
        vm.isResourceLoaded.observe(viewLifecycleOwner) {
            navigateToOnboarding()
        }
    }

    private fun navigateToOnboarding() {
        val action = ResourceLoadingFragmentDirections.onboardingAction()
        action.let { this.findNavController().navigate(it) }
    }
}