package com.eatapp.clementine.ui.launch

import android.text.method.LinkMovementMethod
import android.view.View
import androidx.fragment.app.activityViewModels
import com.eatapp.clementine.R
import com.eatapp.clementine.data.network.response.apiresources.Country
import com.eatapp.clementine.databinding.SignUpFragmentBinding
import com.eatapp.clementine.internal.showErrorAlert
import com.eatapp.clementine.ui.base.BaseFragment
import com.eatapp.clementine.views.LoadingButton
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class SignUpFragment : BaseFragment<SignUpViewModel, SignUpFragmentBinding>() {

    private val sharedLaunchViewModel by activityViewModels<SharedLaunchViewModel>()

    override fun viewModelClass() = SignUpViewModel::class.java

    override fun inflateLayout() = SignUpFragmentBinding.inflate(layoutInflater)

    override fun viewCreated() {
        binding.tvPrivacy.movementMethod = LinkMovementMethod.getInstance()

        binding.zone.setOnClickListener {
            val list = vm.timeZones()
            bottomSheetDialog(list, "", true) {
                list.firstOrNull { it.isSelected }?.let {
                    vm.timeZone(Pair(it.name, it.value as String))
                }
            }
        }

        binding.phonePrefixCont.setOnClickListener {
            val list = vm.countries()
            bottomSheetDialog(list, "", true) {
                list.firstOrNull { it.isSelected }?.let {
                    vm.prefixAndCode(it.value as? Country)
                }
            }
        }

        vm.captchaError.observe(viewLifecycleOwner) {
            if (it) {
                requireContext().showErrorAlert(resources.getString(R.string.error_message),
                    getString(R.string.captcha_error_description))
            }
        }

        observe()
    }

    override fun onShowLockdownView(view: View) {}

    override fun onHideLockdownView(view: View) {}

    private fun observe() {

        vm.authentication.observe(viewLifecycleOwner) { state ->
            sharedLaunchViewModel.authentication(state)
        }

        vm.loading.observe(viewLifecycleOwner) {

            when (it) {
                true -> binding.btnRegister.showLoading()
                else -> binding.btnRegister.hideLoading()
            }
        }

        vm.signInEnabled.observe(viewLifecycleOwner) {
            if (it) {
                binding.btnRegister.state = LoadingButton.LoadingButtonState.Available
            } else {
                binding.btnRegister.state = LoadingButton.LoadingButtonState.Disabled
            }
        }
    }
}