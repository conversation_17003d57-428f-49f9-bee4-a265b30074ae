package com.eatapp.clementine.ui.omnisearch

import android.os.Bundle
import com.eatapp.clementine.R
import com.eatapp.clementine.databinding.ActivityOmniSearchBinding
import com.eatapp.clementine.ui.base.BaseActivity
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class OmniSearchActivity : BaseActivity<ActivityOmniSearchBinding>() {

    override fun getLayoutId() = R.layout.activity_omni_search

    override fun onCreated(savedInstanceState: Bundle?) {
        binding.buttonBack.setOnClickListener {
            finish()
        }
    }
}