package com.eatapp.clementine.ui.omnisearch

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.eatapp.clementine.R
import com.eatapp.clementine.data.network.response.guest.Guest
import com.eatapp.clementine.data.network.response.omnisearch.OmniSearchItemType
import com.eatapp.clementine.data.network.response.omnisearch.OmniSearchSection
import com.eatapp.clementine.data.network.response.reservation.Reservation
import com.eatapp.clementine.data.network.response.restaurant.Restaurant
import com.eatapp.clementine.databinding.OmnisearchSectionLayoutBinding
import com.eatapp.clementine.ui.omnisearch.guests.OmniSearchGuestsAdapter
import com.eatapp.clementine.ui.omnisearch.guests.OmniSearchReservationsAdapter

class OmniSearchSectionsAdapter(
    private val groupRestaurants: MutableList<Restaurant>?
) : ListAdapter<OmniSearchSection, OmniSearchSectionsAdapter.OmniSearchSectionViewHolder>(
    OmniSearchSectionDiffCallback()
) {

    var sectionClickListener: ((OmniSearchItemType) -> Unit)? = null
    var guestClickListener: ((Guest) -> Unit)? = null
    var reservationClickListener: ((Reservation) -> Unit)? = null

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): OmniSearchSectionViewHolder {
        return OmniSearchSectionViewHolder(
            OmnisearchSectionLayoutBinding.inflate(
                LayoutInflater.from(parent.context),
                parent,
                false
            )
        )
    }

    override fun onBindViewHolder(holder: OmniSearchSectionViewHolder, position: Int) {
        holder.bind(getItem(position))
    }

    inner class OmniSearchSectionViewHolder(val binding: OmnisearchSectionLayoutBinding) :
        RecyclerView.ViewHolder(binding.root) {

        private val reservationsAdapter = OmniSearchReservationsAdapter(groupRestaurants)
        private val guestsAdapter = OmniSearchGuestsAdapter()

        fun bind(section: OmniSearchSection) {
            binding.title.text = section.name
            binding.imageChevron.setImageResource(if (section.expanded) R.drawable.ic_icon_arrow_up else R.drawable.ic_icon_arrow_down)

            binding.titleContainer.setOnClickListener {
                sectionClickListener?.invoke(section.type)
            }

            when (section.type) {
                OmniSearchItemType.GUEST -> {
                    binding.recyclerItems.adapter = guestsAdapter
                    guestsAdapter.guestClickListener = {
                        guestClickListener?.invoke(it)
                    }
                    guestsAdapter.submitList(if (section.expanded) section.items as? List<Guest> else emptyList())
                }

                OmniSearchItemType.RESERVATION -> {
                    binding.recyclerItems.adapter = reservationsAdapter
                    reservationsAdapter.reservationClickListener = {
                        reservationClickListener?.invoke(it)
                    }
                    reservationsAdapter.submitList(if (section.expanded) section.items as? List<Reservation> else emptyList())
                }
            }
        }
    }
}

class OmniSearchSectionDiffCallback : DiffUtil.ItemCallback<OmniSearchSection>() {

    override fun areItemsTheSame(oldItem: OmniSearchSection, newItem: OmniSearchSection): Boolean {
        return oldItem.name == newItem.name
    }

    override fun areContentsTheSame(
        oldItem: OmniSearchSection,
        newItem: OmniSearchSection
    ): Boolean {
        return oldItem == newItem
    }
}