package com.eatapp.clementine.ui.omnisearch

import SingleLiveEvent
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.eatapp.clementine.data.network.response.guest.Guest
import com.eatapp.clementine.data.network.response.omnisearch.OmniSearchFilterType
import com.eatapp.clementine.data.network.response.omnisearch.OmniSearchItemType
import com.eatapp.clementine.data.network.response.omnisearch.OmniSearchSection
import com.eatapp.clementine.data.network.response.reservation.Reservation
import com.eatapp.clementine.data.network.response.reservation.ReservationResponse
import com.eatapp.clementine.data.network.response.user.Permission
import com.eatapp.clementine.data.repository.FabricateRepository
import com.eatapp.clementine.data.repository.OmniSearchRepository
import com.eatapp.clementine.data.repository.ReservationsRepository
import com.eatapp.clementine.internal.Constants
import com.eatapp.clementine.internal.managers.AnalyticsManager
import com.eatapp.clementine.internal.managers.DataManager
import com.eatapp.clementine.internal.managers.EatManager
import com.eatapp.clementine.internal.managers.FeatureFlagsManager
import com.eatapp.clementine.internal.managers.ShiftManager
import com.eatapp.clementine.internal.managers.manageReservations
import com.eatapp.clementine.ui.launch.FabricateViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.joinAll
import javax.inject.Inject

@HiltViewModel
class OmniSearchViewModel @Inject constructor(
    private val omniSearchRepository: OmniSearchRepository,
    val eatManager: EatManager,
    val featureFlagsManager: FeatureFlagsManager,
    private val reservationsRepository: ReservationsRepository,
    shiftManager: ShiftManager,
    analyticsManager: AnalyticsManager,
    dataManager: DataManager,
    fabricateRepository: FabricateRepository
) : FabricateViewModel(
    eatManager,
    shiftManager,
    dataManager,
    analyticsManager,
    fabricateRepository
) {

    private var searchJob: Job? = null
    private val debounceTime = 500L

    private val results = MutableLiveData<List<OmniSearchSection>>()

    private val _filteredResults = MutableLiveData<List<OmniSearchSection>>()
    val filteredResults: LiveData<List<OmniSearchSection>> = _filteredResults

    private val _fetchingRestaurantData = SingleLiveEvent<Pair<Reservation, Boolean>>()
    val fetchingRestaurantData: LiveData<Pair<Reservation, Boolean>> = _fetchingRestaurantData

    private val _fetchingPermissions = SingleLiveEvent<Pair<Guest, Boolean>>()
    val fetchingPermissions: LiveData<Pair<Guest, Boolean>> = _fetchingPermissions

    private val _searchInProgress = MutableLiveData<Boolean>()
    val searchInProgress: LiveData<Boolean> = _searchInProgress

    private var activeFilter = OmniSearchFilterType.ALL
    private var searchText = ""

    fun search(query: String) {

        searchJob?.cancel()
        searchJob = launch({

            delay(debounceTime)

            searching(true)
            searchText = query
            val response = omniSearchRepository.search(query)
            val finalList: MutableList<OmniSearchSection> = mutableListOf()

            finalList.add(
                OmniSearchSection(
                    "Guests",
                    items = response.guests?.guests,
                    type = OmniSearchItemType.GUEST
                )
            )

            finalList.add(
                OmniSearchSection(
                    "Reservations",
                    items = response.reservations?.reservations,
                    type = OmniSearchItemType.RESERVATION
                )
            )

            results.value = finalList
            _filteredResults.value = finalList
            applyFilter()
            searching(false)
        })
    }

    fun filterItems(type: OmniSearchFilterType) {
        activeFilter = type
        applyFilter()
    }

    fun toggleSectionExpanded(type: OmniSearchItemType) {
        // Stupid thing to prevent adapter keeping reference to list
        val list = filteredResults.value?.map { it.copy() }!!
        val section = list.firstOrNull { it.type == type }

        section?.let {
            it.expanded = !it.expanded
            _filteredResults.value = list
        }
    }

    fun updateGuest() {
        search(searchText)
    }

    fun updateReservation(resultCode: Int, updatedReservation: Reservation?) {
        val sections = filteredResults.value?.map { it.copy() }?.toMutableList()!!
        val reservationSection = sections.first { it.type == OmniSearchItemType.RESERVATION }
        val reservations = (reservationSection.items as? List<Reservation>)?.toMutableList()

        when (resultCode) {
            Constants.RESERVATION_RESULT_UPDATED -> {
                updatedReservation?.let {
                    val index = reservations?.indexOfFirst { it.id == updatedReservation.id }
                    if (index != -1) {
                        reservations?.set(index!!, updatedReservation)
                    }
                }
            }

            Constants.RESERVATION_RESULT_DELETED -> reservations?.remove(updatedReservation!!)
        }

        reservationSection.items = reservations
        val index = sections.indexOfFirst { it.type == OmniSearchItemType.RESERVATION }
        sections[index] = reservationSection

        _filteredResults.value = sections
    }

    fun loadPermissions(guest: Guest) {
        searching(true)

        launch({
            val jobs = listOf(
                tags(guest.attributes?.restaurantId ?: ""),
                user(guest.attributes?.restaurantId ?: "")
            )

            jobs.joinAll()
            _fetchingPermissions.value = Pair(guest, true)
            searching(false)
        })
    }

    private var reservationDetails: Reservation? = null

    fun loadRestaurantData(reservation: Reservation) {

        searching(true)

        val restaurantId = reservation.attributes.restaurantId
        val reservationId = reservation.id

        launch({

            // Needed since reservation in omnisearch doesn't contain relationships
            reservationDetails = reservationsRepository.reservation(restaurantId, reservationId).reservation

            if (restaurantId != eatManager.restaurantId()) {

                fetchRestaurantData(restaurantId)

            } else {

                _fetchingRestaurantData.value = Pair(reservationDetails!!, true)
                searching(false)
            }
        })
    }

    override fun restaurantDataFetched() {
        super.restaurantDataFetched()

        _fetchingRestaurantData.value = Pair(reservationDetails!!, true)
        searching(false)
    }

    fun reservationPermission(restaurantId: String): Permission {
        return eatManager.permission(restaurantId, manageReservations)
    }

    private fun applyFilter() {
        when (activeFilter) {
            OmniSearchFilterType.ALL -> {
                _filteredResults.value = results.value
            }

            OmniSearchFilterType.GUEST -> {
                _filteredResults.value =
                    results.value?.filter { it.type == OmniSearchItemType.GUEST }
            }

            OmniSearchFilterType.RESERVATION -> {
                _filteredResults.value =
                    results.value?.filter { it.type == OmniSearchItemType.RESERVATION }
            }
        }
    }

    private fun searching(inProgress: Boolean) {
        _searchInProgress.value = inProgress
    }
}