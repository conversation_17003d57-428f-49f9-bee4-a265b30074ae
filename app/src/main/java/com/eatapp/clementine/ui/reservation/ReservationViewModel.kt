package com.eatapp.clementine.ui.reservation

import SingleLiveEvent
import android.graphics.Bitmap
import androidx.lifecycle.LiveData
import androidx.lifecycle.viewModelScope
import com.eatapp.clementine.data.network.response.reservation.Reservation
import com.eatapp.clementine.data.repository.ReservationsRepository
import com.eatapp.clementine.internal.asLiveData
import com.eatapp.clementine.internal.managers.EatManager
import com.eatapp.clementine.internal.managers.PrintManager
import com.eatapp.clementine.ui.base.BaseViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class ReservationViewModel @Inject constructor(
    val printManager: PrintManager,
    val eatManager: EatManager,
    val reservationsRepository: ReservationsRepository
) : BaseViewModel() {

    private val _reservation = SingleLiveEvent<Reservation?>()
    val reservation: LiveData<Reservation?> by lazy {
        _reservation.asLiveData()
    }

    private val _fetchReservationError = SingleLiveEvent<Boolean>()
    val fetchReservationError: LiveData<Boolean> = _fetchReservationError

    fun reservation(reservation: Reservation?) {
        _reservation.value = reservation
    }

    fun printReservation(logo: Bitmap?) {
        printManager.printText(reservation.value, eatManager.servers(), logo, eatManager)
    }

    fun loadReservation(id: String) {
        loading(true)
        viewModelScope.launch {
            try {
                val res =
                    reservationsRepository.reservation(eatManager.restaurantId(), id).reservation
                _reservation.postValue(res)
                loading(false)
            } catch (e: Exception) {
                _fetchReservationError.postValue(true)
                loading(false)
            }
        }
    }
}
