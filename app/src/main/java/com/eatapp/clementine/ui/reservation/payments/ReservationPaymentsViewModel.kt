package com.eatapp.clementine.ui.reservation.payments

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.eatapp.clementine.data.network.response.payment.Payment
import com.eatapp.clementine.data.network.response.payment.PaymentStatus
import com.eatapp.clementine.data.network.response.reservation.Reservation
import com.eatapp.clementine.data.repository.ReservationsRepository
import com.eatapp.clementine.internal.managers.DataManager
import com.eatapp.clementine.internal.managers.EatManager
import com.eatapp.clementine.ui.base.BaseViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject

@HiltViewModel
class ReservationPaymentsViewModel @Inject constructor(
    private val reservationsRepository: ReservationsRepository,
    val dataManager: DataManager,
    val eatManager: EatManager
) : BaseViewModel() {

    private val _reservation = MutableLiveData<Reservation>()
    val reservation: LiveData<Reservation> = _reservation

    private val _payments = MutableLiveData<MutableList<Payment>>()
    val payments: LiveData<MutableList<Payment>> = _payments

    fun reservation(reservation: Reservation) {
        _reservation.value = reservation
    }

    fun payments(payments: List<Payment>) {
        _payments.value = payments.toMutableList()
    }

    fun loadPayments() {
        launch({
            reservation.value?.let {
                val response = reservationsRepository.reservation(it.attributes.restaurantId, it.id)
                reservation(response.reservation)
                payments(response.reservation.payments)
            }
        })
    }

    fun calculateAmount(): Double {

        val amount = payments.value?.filter {
            it.status in setOf(
                PaymentStatus.REQUESTED.toString(),
                PaymentStatus.AUTHORIZED.toString(),
                PaymentStatus.PAID.toString(),
                PaymentStatus.PARTIALLY_PAID.toString(),
                PaymentStatus.CAPTURED.toString(),
            )
        }?.sumOf { it.attributes.amount } ?: 0.0

        val refundBalance = payments.value?.filter {
            it.status == PaymentStatus.PARTIALLY_REFUNDED.toString()
        }?.sumOf {
            it.attributes.refundBalance
        } ?: 0.0

        return amount + refundBalance
    }
}