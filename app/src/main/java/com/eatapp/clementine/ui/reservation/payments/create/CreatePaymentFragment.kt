package com.eatapp.clementine.ui.reservation.payments.create

import android.app.DatePickerDialog
import android.app.TimePickerDialog
import android.text.Html
import android.text.InputType
import android.view.View
import androidx.navigation.fragment.findNavController
import com.eatapp.clementine.R
import com.eatapp.clementine.data.network.response.payment.PaymentChargeType
import com.eatapp.clementine.data.network.response.payment.PaymentType
import com.eatapp.clementine.data.network.response.reservation.Reservation
import com.eatapp.clementine.databinding.CreatePaymentFragmentBinding
import com.eatapp.clementine.internal.Constants
import com.eatapp.clementine.internal.SelectorItem
import com.eatapp.clementine.internal.visibleOrGone
import com.eatapp.clementine.ui.base.BaseFragment
import com.eatapp.clementine.ui.reservation.ReservationActivity
import com.eatapp.clementine.views.LoadingButton
import dagger.hilt.android.AndroidEntryPoint
import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Date
import java.util.Locale


@AndroidEntryPoint
class CreatePaymentFragment : BaseFragment<CreatePaymentViewModel, CreatePaymentFragmentBinding>() {

    override fun viewModelClass() = CreatePaymentViewModel::class.java
    override fun onShowLockdownView(view: View) {}
    override fun onHideLockdownView(view: View) {}
    override fun inflateLayout() = CreatePaymentFragmentBinding.inflate(layoutInflater)

    override fun viewCreated() {
        (activity as? ReservationActivity)?.reservation
            ?.let { reservation ->
                vm.reservation(reservation)
            }

        arguments?.let {
            vm.payment(CreatePaymentFragmentArgs.fromBundle(it).payment)
        }

        (activity as? ReservationActivity)?.let {
            it.updateToolbarTitle(
                if (vm.isInEditMode.value == true) getString(
                    R.string.edit_payment_screen_title
                ) else getString(R.string.create_payment_screen_title)
            )
            it.showToolbarButtons(false)
        }

        bindUI()
        observe()
    }

    override fun onDestroy() {
        super.onDestroy()

        (activity as? ReservationActivity)?.let {
            it.setDefaultToolbarTitle()
            it.showToolbarButtons(true)
        }
    }

    fun bindUI() = with(binding) {

        textChargeTypeDescription.text =
            Html.fromHtml(getString(R.string.payment_amount_charge_type_description))

        containerPaymentTotal.setCurrency(vm.eatManager.restaurant()?.currency ?: "")
        containerPaymentTotal.setTitle(
            getString(
                R.string.payment_total_title,
                vm.eatManager.restaurant()?.attributes?.bookingFeeRate ?: 0.0
            )
        )

        containerPaymentType.setText(PaymentType.LUMP.readableValue)
        containerPaymentChargeType.setText(PaymentChargeType.SMART.readableValue)

        containerPaymentAmount.setInputType(InputType.TYPE_CLASS_NUMBER or InputType.TYPE_NUMBER_FLAG_DECIMAL)
        containerPaymentAmount.onTextChanged {
            vm.validateAmount(it)
            updateTotalAmount(
                PaymentType.fromReadableValue(binding.containerPaymentType.getText())
                    ?: PaymentType.LUMP
            )
        }

        binding.containerPaymentInternalNotes.onTextChanged {
            vm.updateInternalNotes(it)
        }

        containerPaymentType.setOnClickListener {
            showPaymentTypeSelector()
        }

        containerPaymentChargeType.setOnClickListener {
            showChargeTypeSelector()
        }

        containerPaymentAutoCancellationDate.setOnClickListener {
            showAutoCancellationDatePicker()
        }

        containerPaymentAutoCancellationTime.setOnClickListener {
            showAutoCancellationTimePicker()
        }

        containerPaymentRules.setOnClickListener {
            showPaymentRulesSelector()
        }

        btnCreatePayment.setOnClickListener {
            if (vm.isInEditMode.value == true) {
                vm.editPayment(
                    vm.amountWithoutFee(
                        binding.containerPaymentAmount.getText().toDoubleOrNull() ?: 0.0,
                        PaymentType.fromReadableValue(binding.containerPaymentType.getText())
                            ?: PaymentType.LUMP
                    ),
                    PaymentChargeType.fromReadableValue(binding.containerPaymentChargeType.getText())
                        ?: PaymentChargeType.SMART
                )
            } else {
                vm.createPayment(
                    vm.amountWithoutFee(
                        binding.containerPaymentAmount.getText().toDoubleOrNull() ?: 0.0,
                        PaymentType.fromReadableValue(binding.containerPaymentType.getText())
                            ?: PaymentType.LUMP
                    ),
                    PaymentChargeType.fromReadableValue(binding.containerPaymentChargeType.getText())
                        ?: PaymentChargeType.SMART
                )
            }
        }

        updateAutoCancellationTimeState()
    }

    private fun updateAutoCancellationTimeState() {
        binding.containerPaymentAutoCancellationTime.isEnabled =
            vm.autoCancellationDate.value != null
    }

    fun observe() {
        vm.validationError.observe(viewLifecycleOwner) {
            binding.btnCreatePayment.state = if (it) LoadingButton.LoadingButtonState.Disabled else LoadingButton.LoadingButtonState.Available
        }

        vm.activePaymentRules.observe(viewLifecycleOwner) {
            binding.containerPaymentRules.setText(it[0].attributes.name)
        }
        vm.createdPayment.observe(viewLifecycleOwner) {
            findNavController().navigateUp()
        }

        vm.payment.observe(viewLifecycleOwner) {
            it?.let {
                binding.run {
                    containerPaymentAmount.setText(it.attributes.amount.toString())
                    textPaymentDescription.setText(it.attributes.description)
                    containerPaymentInternalNotes.setText(it.attributes.notes ?: "")
                    it.attributes.autoCancelAt?.let { autoCancelDate ->
                        updateAutoCancellationDateLabel(
                            parseAutoCancellationDate(autoCancelDate) ?: Date()
                        )
                        updateAutoCancellationTimeLabel(
                            parseAutoCancellationDate(autoCancelDate) ?: Date()
                        )
                    }
                }
            }
        }
    }

    private fun showPaymentTypeSelector() {
        val selectorItems = arrayListOf<SelectorItem>()
        PaymentType.entries.forEach {
            selectorItems.add(
                SelectorItem(
                    it.readableValue,
                    it.readableValue,
                    it,
                    it.readableValue == binding.containerPaymentType.getText(),
                    isHeader = false,
                    isDisabled = false
                )
            )
        }

        bottomSheetDialog(selectorItems, getString(R.string.payment_type_label), true) {
            binding.containerPaymentType.setText(it!!.name)
            binding.containerPaymentRules.visibleOrGone =
                it.name == PaymentType.PAYMENT_PACKAGE.readableValue
            binding.containerPaymentAmount.visibleOrGone =
                it.name != PaymentType.PAYMENT_PACKAGE.readableValue

            updateTotalAmount((it.value as? PaymentType) ?: PaymentType.LUMP)
        }
    }

    private fun showChargeTypeSelector() {
        val selectorItems = arrayListOf<SelectorItem>()
        PaymentChargeType.entries.forEach {
            selectorItems.add(
                SelectorItem(
                    it.readableValue,
                    it.readableValue,
                    it,
                    it.readableValue == binding.containerPaymentChargeType.getText(),
                    isHeader = false,
                    isDisabled = false
                )
            )
        }

        bottomSheetDialog(selectorItems, getString(R.string.payment_type_label), true) {
            binding.containerPaymentChargeType.setText(it!!.name)
        }
    }

    private fun showAutoCancellationDatePicker() {
        val c = Calendar.getInstance()
        vm.payment.value?.attributes?.autoCancelAt?.let {
            c.time = parseAutoCancellationDate(it) ?: Date()
        }
        val datePicker = DatePickerDialog(
            requireContext(), { _, y, m, d ->
                c.set(Calendar.YEAR, y)
                c.set(Calendar.MONTH, m)
                c.set(Calendar.DAY_OF_MONTH, d)

                updateAutoCancellationDateLabel(c.time)
                vm.updateAutoCancellationDate(y, m, d)
                updateAutoCancellationTimeState()
            }, c.get(Calendar.YEAR), c.get(Calendar.MONTH), c.get(Calendar.DAY_OF_MONTH)
        )

        datePicker.datePicker.minDate = System.currentTimeMillis()
        datePicker.show()
    }

    private fun showAutoCancellationTimePicker() {
        val c = Calendar.getInstance()
        vm.payment.value?.attributes?.autoCancelAt?.let {
            c.time = parseAutoCancellationDate(it) ?: Date()
        }
        val timePicker = TimePickerDialog(
            requireContext(), { _, h, m ->
                c.set(Calendar.HOUR_OF_DAY, h)
                c.set(Calendar.MINUTE, m)

                updateAutoCancellationTimeLabel(c.time)
                vm.updateAutoCancellationTime(h, m)
            },
            c.get(Calendar.HOUR_OF_DAY),
            c.get(Calendar.MINUTE),
            false
        )

        timePicker.show()
    }

    private fun updateAutoCancellationTimeLabel(date: Date) {
        val formatter = SimpleDateFormat("hh:mm a", Locale.US)
        binding.containerPaymentAutoCancellationTime.setText(formatter.format(date))
    }

    private fun updateAutoCancellationDateLabel(date: Date) {
        val formatter = SimpleDateFormat("dd/MM/yyyy", Locale.US)
        binding.containerPaymentAutoCancellationDate.setText(formatter.format(date))
    }

    private fun showPaymentRulesSelector() {
        val selectorItems = arrayListOf<SelectorItem>()
        vm.activePaymentRules.value?.forEach {
            selectorItems.add(
                SelectorItem(
                    it.id,
                    it.attributes.name,
                    it,
                    it.attributes.name == binding.containerPaymentRules.getText(),
                    isHeader = false,
                    isDisabled = false
                )
            )
        }

        bottomSheetDialog(selectorItems, getString(R.string.payment_package_label), true) {
            binding.containerPaymentRules.setText(it!!.name)
            updateTotalAmount(PaymentType.PAYMENT_PACKAGE)
        }
    }

    private fun updateTotalAmount(paymentType: PaymentType) {
        val amount = binding.containerPaymentAmount.getText().toDoubleOrNull() ?: 0.0
        val bookingFee = (vm.eatManager.restaurant()?.attributes?.bookingFeeRate ?: 0.0) / 100

        val total: Double = when (paymentType) {
            PaymentType.PAYMENT_PACKAGE -> {
                vm.calculatePackageTotal(
                    bookingFee,
                    binding.containerPaymentRules.getText()
                )
            }

            PaymentType.LUMP -> {
                vm.calculateLumpTotal(amount, bookingFee)
            }

            PaymentType.PER_COVER -> {
                vm.calculatePerCoverTotal(amount, bookingFee)
            }
        }

        vm.validateAmount(total.toString())
        binding.containerPaymentTotal.setText(String.format("%.2f", total))
    }

    private fun parseAutoCancellationDate(dateString: String): Date? {
        val sdf = SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ssXXX", Locale.US)
        return sdf.parse(dateString)
    }
}
