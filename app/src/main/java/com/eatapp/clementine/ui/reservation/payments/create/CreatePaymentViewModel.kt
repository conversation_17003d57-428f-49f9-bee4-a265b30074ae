package com.eatapp.clementine.ui.reservation.payments.create

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.eatapp.clementine.data.network.body.CreatePaymentBody
import com.eatapp.clementine.data.network.body.EditPaymentBody
import com.eatapp.clementine.data.network.response.payment.Payment
import com.eatapp.clementine.data.network.response.payment.PaymentChargeType
import com.eatapp.clementine.data.network.response.payment.PaymentRule
import com.eatapp.clementine.data.network.response.payment.PaymentType
import com.eatapp.clementine.data.network.response.reservation.Reservation
import com.eatapp.clementine.data.repository.PaymentsRepository
import com.eatapp.clementine.internal.formatISO8601Timezone
import com.eatapp.clementine.internal.formatNonLocal
import com.eatapp.clementine.internal.managers.EatManager
import com.eatapp.clementine.ui.base.BaseViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import java.util.Calendar
import javax.inject.Inject

@HiltViewModel
class CreatePaymentViewModel @Inject constructor(
    private val paymentsRepository: PaymentsRepository,
    val eatManager: EatManager
) : BaseViewModel() {

    val validationError = MutableLiveData<Boolean>()

    private val _reservation = MutableLiveData<Reservation>()
    val reservation: LiveData<Reservation> = _reservation

    private val _payment = MutableLiveData<Payment?>()
    val payment: LiveData<Payment?> = _payment

    private val _createdPayment = MutableLiveData<Payment>()
    val createdPayment: LiveData<Payment> = _createdPayment

    private val _activePaymentRules = MutableLiveData<List<PaymentRule>>()
    val activePaymentRules: LiveData<List<PaymentRule>> = _activePaymentRules

    private val _isInEditMode = MutableLiveData<Boolean>()
    val isInEditMode: LiveData<Boolean> = _isInEditMode

    val paymentDescription = MutableLiveData<String>()
    val autoCancellationDate = MutableLiveData<Calendar?>()

    private val selectedRule = MutableLiveData<PaymentRule>()
    private val paymentNotes = MutableLiveData<String>()

    init {
        loadPaymentRules()
    }

    fun reservation(reservation: Reservation) {
        _reservation.value = reservation
    }

    fun payment(payment: Payment?) {
        _payment.value = payment
        _isInEditMode.value = payment != null
        validationError.value = payment == null

        payment?.attributes?.autoCancelAt?.let {
            formatNonLocal().parse(it)?.let { date ->
                val c = Calendar.getInstance()
                c.time = date
                autoCancellationDate.value = c
            }
        }
    }

    fun validateAmount(amount: String) {
        validationError.value = (amount.toDoubleOrNull() ?: 0.0) <= 0.0
    }

    fun updateAutoCancellationDate(year: Int, month: Int, day: Int) {
        val c = autoCancellationDate.value ?:  Calendar.getInstance()
        c.set(Calendar.YEAR, year)
        c.set(Calendar.MONTH, month)
        c.set(Calendar.DAY_OF_MONTH, day)

        autoCancellationDate.value = c
    }

    fun updateAutoCancellationTime(hour: Int, minute: Int) {
        autoCancellationDate.value?.set(Calendar.HOUR_OF_DAY, hour)
        autoCancellationDate.value?.set(Calendar.MINUTE, minute)
    }

    fun updateInternalNotes(notes: String) {
        paymentNotes.value = notes
    }

    fun calculatePackageTotal(bookingFee: Double, selectedRuleName: String): Double {
        val selectedRule =
            activePaymentRules.value?.firstOrNull { it.attributes.name == selectedRuleName }
        this.selectedRule.value = selectedRule

        return when (selectedRule?.attributes?.ruleType) {
            PaymentType.PER_COVER.value -> {
                calculatePerCoverTotal(selectedRule.attributes.amount, bookingFee)
            }

            PaymentType.LUMP.value -> {
                calculateLumpTotal(selectedRule.attributes.amount, bookingFee)
            }

            else -> 0.0
        }
    }

    fun calculateLumpTotal(amount: Double, bookingFee: Double): Double {
        return amount + amount * bookingFee
    }

    fun calculatePerCoverTotal(
        amount: Double,
        bookingFee: Double
    ): Double {
        return amount * (reservation.value?.covers ?: 0.0).toDouble() +
                amount * (reservation.value?.covers ?: 0.0).toDouble() * bookingFee
    }

    fun createPayment(amount: Double, chargeType: PaymentChargeType) {
        launch({
            loading(true)
            val body = CreatePaymentBody(
                amount = amount,
                autoCancelAt = loadAutoCancelAt(),
                chargeStrategy = chargeType.value,
                description = paymentDescription.value,
                notes = paymentNotes.value,
                paymentRuleId = selectedRule.value?.id,
                reservationId = reservation.value!!.id
            )
            val response = paymentsRepository.createPayment(body)
            _createdPayment.value = response.payment
            loading(false)
        })
    }

    fun editPayment(amount: Double, chargeType: PaymentChargeType) {
        launch({
            loading(true)
            val body = EditPaymentBody(
                amount = amount,
                autoCancelAt = loadAutoCancelAt(),
                chargeStrategy = chargeType.value,
                description = paymentDescription.value,
                notes = paymentNotes.value
            )

            val response = paymentsRepository.editPayment(payment.value!!.id, body)
            _createdPayment.value = response.payment
            loading(false)
        })
    }

    fun amountWithoutFee(amount: Double, paymentType: PaymentType): Double {

        val total: Double = when (paymentType) {
            PaymentType.LUMP -> amount
            PaymentType.PER_COVER -> amount * (reservation.value?.covers ?: 0)
            PaymentType.PAYMENT_PACKAGE -> {
                when (selectedRule.value?.type) {
                    PaymentType.PER_COVER.value -> (selectedRule.value?.attributes?.amount
                        ?: 0.0) * (reservation.value?.covers?.toDouble()
                        ?: 0.0)

                    else -> selectedRule.value?.attributes?.amount ?: 0.0
                }
            }
        }

        return total
    }

    private fun loadAutoCancelAt(): String? {
        val autoCancellationDate = autoCancellationDate.value ?: return null

        val c = Calendar.getInstance()
        c.set(Calendar.YEAR, autoCancellationDate.get(Calendar.YEAR))
        c.set(Calendar.MONTH, autoCancellationDate.get(Calendar.MONTH))
        c.set(Calendar.DAY_OF_MONTH, autoCancellationDate.get(Calendar.DAY_OF_MONTH))
        c.set(Calendar.HOUR_OF_DAY, autoCancellationDate.get(Calendar.HOUR_OF_DAY))
        c.set(Calendar.MINUTE, autoCancellationDate.get(Calendar.MINUTE))

        return c.time.formatISO8601Timezone()
    }

    private fun loadPaymentRules() {
        launch({
            val response = paymentsRepository.loadPaymentRules()
            _activePaymentRules.value =
                response.paymentRules.filter { it.attributes.status == "active" }
        })
    }
}