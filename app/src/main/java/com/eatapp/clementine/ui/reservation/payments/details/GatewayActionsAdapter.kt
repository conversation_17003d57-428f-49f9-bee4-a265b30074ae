package com.eatapp.clementine.ui.reservation.payments.details

import android.annotation.SuppressLint
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.eatapp.clementine.data.network.response.payment.GatewayAction
import com.eatapp.clementine.data.network.response.payment.GatewayActionStatus
import com.eatapp.clementine.data.network.response.payment.Payment
import com.eatapp.clementine.databinding.ListItemReservationPaymentBinding
import com.eatapp.clementine.internal.shortDateTime
import com.eatapp.clementine.internal.visible
import com.eatapp.clementine.internal.visibleOrGone
import com.eatapp.clementine.views.PaymentHeaderView

class GatewayActionsAdapter :
    RecyclerView.Adapter<RecyclerView.ViewHolder>() {

    private var payment: Payment? = null

    companion object {
        const val TYPE_HEADER = 0
        const val TYPE_ITEM = 1
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        return if (viewType == TYPE_HEADER) {
            val view = PaymentHeaderView(parent.context)
            view.layoutParams = ViewGroup.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.WRAP_CONTENT
            )
            GatewayActionHeaderViewHolder(view)
        } else GatewayActionViewHolder(
            ListItemReservationPaymentBinding.inflate(
                LayoutInflater.from(parent.context), parent, false
            )
        )
    }

    override fun getItemCount(): Int {
        return (payment?.attributes?.gatewayActions?.size ?: 0) + 1
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        if (holder is GatewayActionHeaderViewHolder) {
            holder.bind(payment!!)
        } else {
            (holder as GatewayActionViewHolder).bind(payment!!.attributes.gatewayActions?.get(position - 1))
        }
    }

    override fun getItemViewType(position: Int): Int {
        return if (position == 0) TYPE_HEADER else TYPE_ITEM
    }

    @SuppressLint("NotifyDataSetChanged")
    fun updateData(payment: Payment) {
        this.payment = payment
        this.notifyDataSetChanged()
    }

    inner class GatewayActionViewHolder(val binding: ListItemReservationPaymentBinding) :
        RecyclerView.ViewHolder(binding.root) {

        fun bind(action: GatewayAction?) = with(binding) {
            containerBankCode.visible = true
            containerPaymentLink.visibleOrGone = false
            imageChevron.visibleOrGone = false

            imageStatus.setImageResource(GatewayActionStatus.icon(action?.type ?: "Requested"))
            textCode.text = action?.responseCode
            textBankMessage.text = action?.responseMessage

            textCreated.text = action?.date?.shortDateTime()

            amountValue.text = action?.amount
            statusValue.text = binding.root.context.getString(
                GatewayActionStatus.valueOf(
                    action?.type?.uppercase() ?: ""
                ).readableStatus
            )
            containerUpdated.visibleOrGone = false
            containerExpires.visibleOrGone = false
        }
    }

    inner class GatewayActionHeaderViewHolder(private val header: PaymentHeaderView) :
        RecyclerView.ViewHolder(header) {

        fun bind(payment: Payment) {
            header.updateHeader(payment, payment.attributes.amount, showInternalNotes = true)
        }
    }
}