package com.eatapp.clementine.ui.reservation.vouchers

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.eatapp.clementine.R
import com.eatapp.clementine.data.network.response.vouchers.VoucherAssignment
import com.eatapp.clementine.databinding.ListItemVoucherAssignmentBinding
import com.eatapp.clementine.internal.visibleOrGone

typealias VoucherAssignmentsClickListener = (VoucherAssignment) -> Unit

class VouchersAssignmentsAdapter(
    val clickListener: VoucherAssignmentsClickListener,
) : ListAdapter<VoucherAssignment, VouchersAssignmentsAdapter.VoucherAssignmentViewHolder>(
    VoucherAssignmentItemDiffCallback()
) {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): VoucherAssignmentViewHolder {
        return VoucherAssignmentViewHolder(
            ListItemVoucherAssignmentBinding.inflate(
                LayoutInflater.from(parent.context), parent, false
            )
        )
    }

    override fun onBindViewHolder(holder: VoucherAssignmentViewHolder, position: Int) {
        holder.bind(getItem(position))
    }

    inner class VoucherAssignmentViewHolder(val binding: ListItemVoucherAssignmentBinding) :
        RecyclerView.ViewHolder(binding.root) {

        fun bind(voucherAssignment: VoucherAssignment) {
            binding.run {
                root.setOnClickListener {
                    clickListener.invoke(voucherAssignment)
                }
                ivLeft.visibleOrGone = voucherAssignment.redeemed
                tvName.text = voucherAssignment.attributes.name

                if (voucherAssignment.redeemed) {
                    root.setBackgroundResource(R.drawable.shape_pill_voucher_redeemed)
                    ivRight.setBackgroundResource(R.drawable.ic_voucher_redeemed_icon)
                } else {
                    root.setBackgroundResource(R.drawable.shape_pill_voucher)
                    ivRight.setBackgroundResource(R.drawable.ic_voucher)
                }
            }
        }
    }
}

class VoucherAssignmentItemDiffCallback : DiffUtil.ItemCallback<VoucherAssignment>() {

    override fun areItemsTheSame(oldItem: VoucherAssignment, newItem: VoucherAssignment): Boolean {
        return oldItem.id == newItem.id
    }

    override fun areContentsTheSame(
        oldItem: VoucherAssignment,
        newItem: VoucherAssignment
    ): Boolean {
        return oldItem == newItem
    }
}