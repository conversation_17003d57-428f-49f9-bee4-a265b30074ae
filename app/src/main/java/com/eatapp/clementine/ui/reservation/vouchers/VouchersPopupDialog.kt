package com.eatapp.clementine.ui.reservation.vouchers

import android.content.DialogInterface
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.widget.addTextChangedListener
import androidx.fragment.app.DialogFragment
import androidx.fragment.app.viewModels
import com.eatapp.clementine.R
import com.eatapp.clementine.data.network.response.vouchers.Voucher
import com.eatapp.clementine.data.network.response.vouchers.VoucherAssignment
import com.eatapp.clementine.databinding.PopupDialogVouchersBinding
import com.eatapp.clementine.internal.visibleOrGone
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class VouchersPopupDialog : DialogFragment() {

    enum class DialogType {
        ASSIGN,
        REDEEM
    }

    // Its mandatory to set vouchers to shared viewmodel before showing dialog
    private val vouchersSharedViewModel: VouchersSharedViewModel by viewModels(
        ownerProducer = { requireParentFragment() }
    )

    private lateinit var binding: PopupDialogVouchersBinding
    private lateinit var dialogType: DialogType

    companion object {

        private const val ARG_DIALOG_TYPE = "arg_dialog_type"

        fun newInstance(
            dialogType: DialogType,
        ): VouchersPopupDialog {
            val fragment = VouchersPopupDialog()
            val bundle = Bundle().apply {
                putSerializable(ARG_DIALOG_TYPE, dialogType)
            }
            fragment.arguments = bundle
            return fragment
        }
    }

    override fun onStart() {
        super.onStart()
        // Resize the dialog
        dialog?.window?.setLayout(
            (resources.displayMetrics.widthPixels * 0.85).toInt(),
            ViewGroup.LayoutParams.WRAP_CONTENT
        )
        dialog?.window?.setBackgroundDrawableResource(R.drawable.shape_rounded_dialog_background)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        arguments?.let {
            dialogType = it.getSerializable(ARG_DIALOG_TYPE) as DialogType
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = PopupDialogVouchersBinding.inflate(layoutInflater)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        val vouchers = vouchersSharedViewModel.vouchers.value ?: emptyList()
        val adapter = VouchersAdapter { voucher ->
            val index = vouchers.indexOfFirst { it.id == voucher.id }
            (vouchers[index] as Voucher).selected = !(vouchers[index] as Voucher).selected
        }

        binding.rvVouchers.adapter = adapter
        adapter.submitList(vouchers)

        binding.ivClose.setOnClickListener {
            dismiss()
        }

        if (dialogType == DialogType.ASSIGN) {
            binding.btnAction.setTitle(getString(R.string.assign_voucher_label))
        } else {
            binding.btnAction.setTitle(getString(R.string.redeem_voucher_label))
            val assignment = vouchers.first() as VoucherAssignment
            binding.etVoucherCode.visibleOrGone = assignment.codeRequired
        }

        binding.btnAction.setOnClickListener {
            if (dialogType == DialogType.ASSIGN) {
                val selectedVouchers = vouchers.filterIsInstance<Voucher>().filter { it.selected }
                vouchersSharedViewModel.assignVouchers(selectedVouchers)
            } else {
                val assignment = vouchers.first() as VoucherAssignment
                if (assignment.codeRequired) {
                    checkVoucherCode(assignment)
                } else {
                    vouchersSharedViewModel.redeemVoucher(vouchers.first() as VoucherAssignment)
                }
            }
        }

        binding.etVoucherCode.addTextChangedListener {
            clearInputError()
        }

        observe()
    }

    override fun onDismiss(dialog: DialogInterface) {
        super.onDismiss(dialog)
        vouchersSharedViewModel.vouchers.value?.filterIsInstance<Voucher>()
            ?.forEach { it.selected = false }
    }

    fun showLoading(loading: Boolean) {
        binding.btnAction.showLoading(loading)
        binding.btnAction.isEnabled = !loading
    }

    private fun showDialog(show: Boolean) {
        dialog?.window?.decorView?.visibility = if (show) (View.VISIBLE) else (View.INVISIBLE)
    }

    private fun checkVoucherCode(assignment: VoucherAssignment) {
        if (binding.etVoucherCode.text.toString() == assignment.attributes.code) {
            vouchersSharedViewModel.redeemVoucher(assignment)
        } else {
            showInputError()
        }
    }

    private fun observe() {
        vouchersSharedViewModel.dismissVoucher.observe(viewLifecycleOwner) {
            dismiss()
        }

        vouchersSharedViewModel.showVoucherLoading.observe(viewLifecycleOwner) {
            showLoading(it)
        }

        vouchersSharedViewModel.showVoucher.observe(viewLifecycleOwner) {
            showDialog(it)
        }
    }

    private fun showInputError() {
        binding.etVoucherCode.setBackgroundResource(R.drawable.shape_rounded_btn_bcg_red_outline)
        binding.tvError.visibleOrGone = true
    }

    private fun clearInputError() {
        binding.etVoucherCode.setBackgroundResource(R.drawable.shape_rounded_edittext_bcg)
        binding.tvError.visibleOrGone = false
    }
}