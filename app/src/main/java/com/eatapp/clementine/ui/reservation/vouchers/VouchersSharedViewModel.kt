package com.eatapp.clementine.ui.reservation.vouchers

import SingleLiveEvent
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import com.eatapp.clementine.data.network.response.vouchers.Voucher
import com.eatapp.clementine.data.network.response.vouchers.VoucherAssignment
import com.eatapp.clementine.data.network.response.vouchers.VoucherBaseClass

class VouchersSharedViewModel: ViewModel() {

    private val _vouchers = MutableLiveData<List<VoucherBaseClass>>()
    val vouchers: LiveData<List<VoucherBaseClass>> = _vouchers

    private val _assignVouchers = SingleLiveEvent<List<Voucher>>()
    val assignVouchers: LiveData<List<Voucher>> = _assignVouchers

    private val _redeemVoucher =  SingleLiveEvent<VoucherAssignment>()
    val redeemVoucher: LiveData<VoucherAssignment> = _redeemVoucher

    private val _showVoucherLoading = MutableLiveData<Boolean>()
    val showVoucherLoading: LiveData<Boolean> = _showVoucherLoading

    private val _showVoucher = MutableLiveData<Boolean>()
    val showVoucher: LiveData<Boolean> = _showVoucher

    private val _dismissVoucher = SingleLiveEvent<Void>()
    val dismissVoucher: LiveData<Void> = _dismissVoucher

    fun updateVouchers(vouchers: List<VoucherBaseClass>) {
        _vouchers.value = vouchers
    }

    fun dismissPopup() {
        _dismissVoucher.call()
    }

    fun assignVouchers(vouchers: List<Voucher>) {
        _assignVouchers.value = vouchers
    }

    fun redeemVoucher(voucherAssignment: VoucherAssignment) {
        _redeemVoucher.value = voucherAssignment
    }

    fun showVoucherLoading(show: Boolean) {
        _showVoucherLoading.value = show
    }

    fun showVoucherPopup(show: Boolean) {
        _showVoucher.value = show
    }
}