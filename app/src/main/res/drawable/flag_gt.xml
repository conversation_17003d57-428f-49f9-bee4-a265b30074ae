<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="640dp"
    android:height="480dp"
    android:viewportWidth="640"
    android:viewportHeight="480">
  <path
      android:pathData="M0,0h640v480H0Z"
      android:fillColor="#4997d0"/>
  <path
      android:pathData="M213.3,0h213.4v480H213.3Z"
      android:fillColor="#fff"/>
  <path
      android:pathData="M297.4,303.97c2.19,-0.33 3.41,-0.99 4.87,-1.49m1.87,-3.39a10.74,10.55 90,0 1,2.43 3.88m-6.57,-4.21a10.74,10.55 90,0 1,2.43 3.47m-40.16,-112.89c-0.81,1.32 -2.43,2.23 -3.89,2.81m-0.24,-0.17q-0.16,-1.82 0,-3.64m-1.54,4.13c0,0.33 0.32,1.16 0.49,1.65m1.7,0.83 l-1.62,0.25m-3.25,-0.74c0,2.15 0.24,4.63 0.41,6.53m-1.46,-0.83q0.57,1.07 1.3,1.57m-3.73,5.37q0.65,0.99 1.05,3.31m3,-3.88 l-1.62,1.24m1.38,3.64q-1.22,1.07 -2.84,1.74m-3.08,33.39a13.22,12.98 90,0 0,3.25 3.88m0.49,1.4a6.61,6.49 90,0 1,-2.43 -0.83m3.57,7.02 l1.38,0.83m-0.32,2.81q1.46,1.16 3,3.22m-1.14,2.31q1.7,0.41 2.6,0.83m-0.81,2.07l1.62,0m1.05,-4.38 l-0.41,3.47m0.81,1.32 l0.32,-1.32m3.81,4.96 l-0.08,2.64m31.8,23.06 l-0.97,1.32m-1.95,-3.22 l-0.57,-1.49"
      android:strokeWidth="0.33"
      android:fillColor="#00000000"
      android:strokeColor="#24420e"/>
  <path
      android:pathData="M324.99,310.16a6.61,6.49 90,0 0,-2.68 -2.23c-6.25,-3.31 -12.49,-4.88 -19.47,-6.03 -1.7,-0.25 -3.33,-1.82 -5.03,-2.64 -4.06,-1.9 -8.52,-2.64 -12.41,-4.71 -0.24,-0.08 -1.22,-0.66 -1.62,-1.24 -1.62,-1.82 -4.46,-2.4 -6.17,-4.13 -6.82,-6.45 -13.95,-12.4 -17.69,-21.07 -4.54,-10.5 -10.14,-21.32 -9.41,-33.22 0.49,-7.6 -0.57,-15.21 1.22,-22.31 2.76,-10.74 6.17,-21.24 12.49,-30.08l-1.05,0.66a85.95,84.38 90,0 0,-12.49 30c-1.87,7.19 -0.81,14.88 -1.22,22.31 -0.81,11.98 4.87,22.81 9.33,33.3 3.73,8.59 10.95,14.63 17.69,21.16 1.78,1.65 4.54,2.23 6.17,4.13l1.62,1.24c3.98,1.98 8.44,2.73 12.49,4.71 1.62,0.83 3.25,2.31 4.87,2.64a63.63,62.47 90,0 1,19.63 5.95,6.61 6.49,90 0,1 2.68,2.23l1.05,-0.74"
      android:strokeWidth="0.16"
      android:fillColor="#406325"
      android:strokeColor="#24420e"/>
  <path
      android:pathData="M260.24,266.03a38.84,38.13 90,0 1,-0.16 -19.42c5.44,10.74 3.65,19.59 0.16,19.42zM297.48,303.55a40.49,39.76 90,0 0,-17.77 9.42c12.25,0.08 19.47,-6.03 17.77,-9.42zM303.57,300a49.58,48.68 90,0 1,-11.36 -21.9c12.17,9.01 15.33,20.08 11.36,21.9z"
      android:strokeWidth="0.16"
      android:fillColor="#67923d"
      android:strokeColor="#24420e"/>
  <path
      android:pathData="M299.43,300.08a45.45,44.62 90,0 1,-12.98 -18.84c12.41,6.78 16.47,16.69 12.98,18.84zM286.77,294.46c0.81,-6.45 -4.54,-20 -6.08,-21.49 -2.43,13.64 2.43,22.81 6.08,21.49zM264.62,274.38a34.71,34.08 90,0 1,0.41 -17.35c4.46,9.67 2.68,17.52 -0.41,17.35zM253.51,210.91a42.15,41.38 90,0 1,17.04 -13.22c-5.52,12.07 -14.6,16.45 -17.04,13.22z"
      android:strokeWidth="0.16"
      android:fillColor="#67923d"
      android:strokeColor="#24420e"/>
  <path
      android:pathData="M254.08,206.29a42.97,42.19 90,0 1,14.12 -16.53c-3,12.97 -11.12,19.17 -14.12,16.53zM276.71,288.26a45.45,44.62 90,0 1,-5.92 -21.82c9.09,10.33 9.74,20.91 5.84,21.82zM253.43,258.1c-5.68,0.91 -17.36,-3.8 -18.66,-5.21 11.85,-2.64 19.8,1.65 18.66,5.21zM251.64,250.08c0.65,-4.55 6.98,-13.06 8.36,-13.72 -0.81,9.92 -6,15.37 -8.36,13.72zM293.26,298.51a47.1,46.25 90,0 0,-21.18 11.24c14.6,0.08 23.29,-7.19 21.18,-11.24z"
      android:strokeWidth="0.16"
      android:fillColor="#67923d"
      android:strokeColor="#24420e"/>
  <path
      android:pathData="M260.32,265.95c3.41,0.17 5.19,-8.59 -0.24,-19.34a65.29,64.1 90,0 1,0.24 19.34zM269.41,282.48a40.49,39.76 90,0 0,-17.61 -9.26c7.22,10 16.15,12.48 17.61,9.26zM249.61,238.68a47.1,46.25 90,0 0,-12.01 -19.83c1.22,14.21 8.6,22.06 12.01,19.83zM256.43,267.03c-4.06,-3.06 -15.42,-4.46 -17.04,-3.97 8.28,6.94 16.39,7.02 17.04,3.97zM248.8,246.53a34.71,34.08 90,0 0,-13.71 -11.4c4.46,10.25 11.85,14.05 13.71,11.32zM251.08,207.19c0.65,-6.28 -4.54,-19.34 -6,-20.83 -2.35,13.22 2.35,22.06 5.92,20.83zM254.65,194.88c2.19,-5.37 0.65,-18.51 -0.41,-20.16 -5.19,11.16 -3.08,20.33 0.41,20.16z"
      android:strokeWidth="0.16"
      android:fillColor="#406325"
      android:strokeColor="#24420e"/>
  <path
      android:pathData="M258.7,188.52a33.06,32.45 90,0 0,2.84 -16.03c-5.84,8.02 -5.68,15.62 -2.84,16.03zM263.57,183.48a32.23,31.64 90,0 0,7.3 -14.38c-7.87,5.78 -9.9,13.06 -7.3,14.38zM250.51,219.18c0.08,0.74 -0.81,-6.78 -8.11,-15.12 0.32,12.23 5.03,16.86 8.11,15.12zM276.96,290.08a43.8,43 90,0 0,-20.93 -4.96c10.22,8.59 20.2,8.76 20.93,4.96zM286.77,294.46c-3.73,1.24 -8.52,-7.85 -6.17,-21.49a81.81,80.32 90,0 0,6.08 21.49zM297.56,303.55c1.62,3.39 -5.52,9.5 -17.69,9.42 9.74,-4.13 17.2,-8.18 17.69,-9.34zM303.49,299.92c3.98,-1.9 0.89,-12.81 -11.36,-21.82 6.98,10.91 11.76,20.33 11.36,21.82z"
      android:strokeWidth="0.16"
      android:fillColor="#406325"
      android:strokeColor="#24420e"/>
  <path
      android:pathData="M299.43,300c3.41,-2.23 -0.57,-11.98 -12.98,-18.76 7.71,9.26 13.14,17.35 12.98,18.76zM264.71,274.38c3,0.17 4.87,-7.69 0.41,-17.35 0.57,9.09 0.32,16.53 -0.41,17.35zM253.51,211c2.43,3.14 11.36,-1.16 16.96,-13.22a75.2,73.83 90,0 1,-16.96 13.22z"
      android:strokeWidth="0.16"
      android:fillColor="#406325"
      android:strokeColor="#24420e"/>
  <path
      android:pathData="M254.24,206.29c2.92,2.64 10.95,-3.55 13.95,-16.53 -6.49,9.34 -12.66,16.36 -13.95,16.53zM276.71,288.18c3.81,-0.91 3.25,-11.4 -5.92,-21.73 4.06,11.16 6.49,20.49 5.92,21.73zM253.27,258.1c1.14,-3.55 -6.73,-7.77 -18.5,-5.21 9.98,1.82 17.85,4.13 18.5,5.21zM251.72,250.17c2.35,1.49 7.38,-3.88 8.36,-13.8a57.02,55.98 90,0 1,-8.36 13.8zM293.26,298.59c2.03,4.13 -6.49,11.32 -21.09,11.24 11.52,-4.88 20.53,-9.75 21.09,-11.24z"
      android:strokeWidth="0.16"
      android:fillColor="#406325"
      android:strokeColor="#24420e"/>
  <path
      android:pathData="M254.65,261.65a37.19,36.51 90,0 0,-13.79 -13.06c4.06,11.24 11.52,15.78 13.79,13.06z"
      android:strokeWidth="0.16"
      android:fillColor="#406325"
      android:strokeColor="#24420e"/>
  <path
      android:pathData="M248.97,218.43c3.25,-3.88 14.2,-7.6 15.9,-7.44 -6.49,8.68 -14.6,10.41 -15.9,7.44zM269.33,282.48c-1.46,3.31 -10.3,0.83 -17.61,-9.17a69.42,68.15 90,0 0,17.61 9.17zM249.53,238.68c-3.41,2.23 -10.63,-5.62 -11.93,-19.83 5.35,10.99 10.55,19.42 11.93,19.83zM256.35,267.03c-0.57,3.06 -8.68,2.98 -16.88,-3.88a61.15,60.04 90,0 0,16.88 3.88zM248.64,246.45c-1.87,2.64 -9.09,-1.07 -13.55,-11.32 6.65,6.61 12.58,11.32 13.63,11.32zM250.91,207.11c-3.57,1.32 -8.19,-7.44 -5.92,-20.66 2.19,11.16 4.87,20 5.92,20.66zM254.56,194.8c-3.49,0.25 -5.6,-8.93 -0.41,-20.08 -0.65,10.58 -0.41,19.26 0.41,20.08zM258.62,188.52c-2.76,-0.5 -3,-8.02 2.92,-16.03a54.54,53.55 90,0 0,-2.92 16.03zM263.49,183.48c-2.52,-1.4 -0.57,-8.59 7.3,-14.3a54.54,53.55 90,0 0,-7.3 14.3z"
      android:strokeWidth="0.16"
      android:fillColor="#67923d"
      android:strokeColor="#24420e"/>
  <path
      android:pathData="M250.51,219.18c-3.16,1.74 -7.79,-2.89 -8.11,-15.12q6.25,14.54 8.11,15.12zM276.88,290.08c-0.73,3.8 -10.63,3.64 -20.85,-4.88 10.71,3.55 19.72,5.54 20.85,4.88zM270.22,282.23A33.88,33.26 90,0 1,264.87 266.36c7.3,7.19 8.28,14.96 5.52,15.87zM256.59,261.57c-2.92,-4.79 -3.25,-17.27 -2.43,-19.01 6.73,9.83 5.92,18.76 2.43,19.01zM254.4,261.65c-2.19,2.73 -9.65,-1.74 -13.63,-13.06a63.63,62.47 90,0 0,13.63 13.06z"
      android:strokeWidth="0.16"
      android:fillColor="#67923d"
      android:strokeColor="#24420e"/>
  <path
      android:pathData="M248.97,218.43c1.3,2.98 9.25,1.24 15.82,-7.35 -9.74,5.62 -13.55,8.1 -15.82,7.35zM256.51,261.41c3.49,-0.25 4.3,-9.09 -2.43,-18.84a60.33,59.23 90,0 1,2.43 18.92zM270.3,282.23c2.76,-0.83 1.78,-8.68 -5.44,-15.87 3.57,8.02 5.84,14.88 5.44,15.87z"
      android:strokeWidth="0.16"
      android:fillColor="#406325"
      android:strokeColor="#24420e"/>
  <path
      android:pathData="M250.43,233.14c1.62,-6.94 -1.38,-18.68 -3.49,-21.16 -3.81,12.73 -0.16,21.98 3.49,21.16z"
      android:strokeWidth="0.16"
      android:fillColor="#406325"
      android:strokeColor="#24420e"/>
  <path
      android:pathData="M250.26,234.8c-4.87,1.07 -7.06,-9.92 -3.33,-22.4 0.81,11.16 2.27,21.57 3.25,22.31z"
      android:strokeWidth="0.16"
      android:fillColor="#67923d"
      android:strokeColor="#24420e"/>
  <path
      android:pathData="M249.21,234.71a43.8,43 90,0 1,7.3 -20.41c1.62,13.06 -3.65,21.73 -7.3,20.41z"
      android:strokeWidth="0.16"
      android:fillColor="#67923d"
      android:strokeColor="#24420e"/>
  <path
      android:pathData="M249.29,234.63c3.65,1.32 8.84,-7.27 7.22,-20.33 -2.84,10.99 -6.08,19.67 -7.22,20.33z"
      android:strokeWidth="0.16"
      android:fillColor="#406325"
      android:strokeColor="#24420e"/>
  <path
      android:pathData="M250.35,242.65a41.32,40.57 90,0 1,8.6 -19.59c0.65,13.39 -5.11,21.4 -8.6,19.59z"
      android:strokeWidth="0.16"
      android:fillColor="#67923d"
      android:strokeColor="#24420e"/>
  <path
      android:pathData="M250.43,242.73c3.41,1.82 9.17,-6.12 8.52,-19.42 -3.57,10.58 -7.3,18.92 -8.52,19.42z"
      android:strokeWidth="0.16"
      android:fillColor="#406325"
      android:strokeColor="#24420e"/>
  <path
      android:pathData="M252.54,199.67m-0.89,0a0.91,0.89 90,1 1,1.78 0a0.91,0.89 90,1 1,-1.78 0"
      android:strokeWidth="0.16"
      android:fillColor="#ba1f3e"
      android:strokeColor="#511124"/>
  <path
      android:pathData="M259.35,194.55m-0.89,0a0.91,0.89 90,1 1,1.78 0a0.91,0.89 90,1 1,-1.78 0"
      android:strokeWidth="0.16"
      android:fillColor="#ba1f3e"
      android:strokeColor="#511124"/>
  <path
      android:pathData="M256.43,191.74m-0.89,0a0.91,0.89 90,1 1,1.78 0a0.91,0.89 90,1 1,-1.78 0"
      android:strokeWidth="0.16"
      android:fillColor="#ba1f3e"
      android:strokeColor="#511124"/>
  <path
      android:pathData="M249.05,250.75m-1.05,0a1.07,1.05 90,1 1,2.11 0a1.07,1.05 90,1 1,-2.11 0"
      android:strokeWidth="0.16"
      android:fillColor="#ba1f3e"
      android:strokeColor="#511124"/>
  <path
      android:pathData="M251.08,237.52m-1.05,0a1.07,1.05 90,1 1,2.11 0a1.07,1.05 90,1 1,-2.11 0"
      android:strokeWidth="0.16"
      android:fillColor="#ba1f3e"
      android:strokeColor="#511124"/>
  <path
      android:pathData="M261.78,268.43m-1.05,0a1.07,1.05 90,1 1,2.11 0a1.07,1.05 90,1 1,-2.11 0"
      android:strokeWidth="0.16"
      android:fillColor="#ba1f3e"
      android:strokeColor="#511124"/>
  <path
      android:pathData="M257.08,270m-1.05,0a1.07,1.05 90,1 1,2.11 0a1.07,1.05 90,1 1,-2.11 0"
      android:strokeWidth="0.16"
      android:fillColor="#ba1f3e"
      android:strokeColor="#511124"/>
  <path
      android:pathData="M293.02,295.78m-1.05,0a1.07,1.05 90,1 1,2.11 0a1.07,1.05 90,1 1,-2.11 0"
      android:strokeWidth="0.16"
      android:fillColor="#ba1f3e"
      android:strokeColor="#511124"/>
  <path
      android:pathData="M295.37,301.65m-1.05,0a1.07,1.05 90,1 1,2.11 0a1.07,1.05 90,1 1,-2.11 0"
      android:strokeWidth="0.16"
      android:fillColor="#ba1f3e"
      android:strokeColor="#511124"/>
  <path
      android:pathData="M385.84,206.04c0.57,0.5 0.97,0.66 1.87,1.57m-5.35,-15.62c0,-1.16 -0.49,-3.06 -0.73,-4.13m2.84,2.48q0,2.31 -0.89,4.46m-3.41,-0.41q2.11,2.07 4.3,3.22m3.08,0.99q0.16,2.48 -0.57,4.96m0.32,1.24 l1.05,-1.9m-1.54,10.08c1.14,0.25 2.27,1.65 2.84,2.89m0.41,-1.07 l1.3,-1.82m1.38,25.45 l-1.95,2.81m-3.25,0c0.65,1.16 1.3,2.48 2.11,3.31m2.03,4.88a10.74,10.55 90,0 1,-3.65 3.31m0.16,3.72c-0.89,0.66 -2.03,2.48 -3,3.88m6.82,-16.69 l-1.7,0.74m-1.14,-6.12 l0.97,0.83m-7.55,30.91l-1.62,0m-1.7,-1.49 l0.49,1.32m-6.17,4.21q-0.24,2.31 0.08,4.63m3.49,-7.44 l0.81,1.82m3.16,-0.08 l-2.03,0.25M354.11,294.13l-0.81,1.49m9.65,-3.31q-1.78,0.17 -3.33,-0.17m-4.46,4.96q-1.22,-0.5 -2.43,-0.83m-11.93,2.23c-0.73,0.58 -1.62,1.98 -2.43,3.31m-0.49,-3.72c-1.95,1.07 -2.6,3.06 -3.57,4.79m2.27,2.31q-1.95,-0.58 -4.3,-1.65m13.55,-0.58c-1.87,-0.99 -3.81,-0.83 -5.76,-1.16m8.76,-0.33q-4.46,-0.83 -8.36,-0.08m6.33,-2.15 l-1.14,-0.5m-4.54,4.55 l-1.87,-1.07m46.98,-40.66q-1.54,1.16 -3,3.22m1.46,-65.78 l-1.78,-0.66"
      android:strokeWidth="0.33"
      android:fillColor="#00000000"
      android:strokeColor="#24420e"/>
  <path
      android:pathData="M315.49,310.08q1.14,-1.49 2.68,-2.23c6.25,-3.31 12.49,-4.79 19.47,-5.95 1.7,-0.33 3.33,-1.9 5.03,-2.64 4.06,-1.98 8.52,-2.73 12.41,-4.71 0.24,-0.17 1.22,-0.74 1.62,-1.32 1.62,-1.82 4.46,-2.4 6.17,-4.13 6.82,-6.45 13.95,-12.4 17.69,-21.07 4.54,-10.5 10.14,-21.32 9.41,-33.22 -0.49,-7.6 0.57,-15.21 -1.22,-22.31a86.77,85.19 90,0 0,-12.49 -30.08l1.05,0.66a85.95,84.38 90,0 1,12.49 30.08c1.87,7.11 0.81,14.79 1.22,22.31 0.81,11.9 -4.87,22.73 -9.33,33.3 -3.73,8.59 -10.95,14.54 -17.69,21.07 -1.78,1.65 -4.54,2.23 -6.17,4.13l-1.62,1.24c-3.98,1.98 -8.44,2.81 -12.49,4.71 -1.62,0.83 -3.25,2.31 -4.87,2.64A63.63,62.47 90,0 0,319.23 308.51a6.61,6.49 90,0 0,-2.68 2.23l-1.05,-0.66"
      android:strokeWidth="0.16"
      android:fillColor="#406325"
      android:strokeColor="#24420e"/>
  <path
      android:pathData="M379.75,268.51c2.27,-6.12 0,-20.49 -1.14,-22.31 -5.44,12.73 -2.76,22.81 1.14,22.31z"
      android:strokeWidth="0.16"
      android:fillColor="#406325"
      android:strokeColor="#24420e"/>
  <path
      android:pathData="M373.99,274.3c1.87,-4.63 0.57,-15.95 -0.32,-17.35 -4.46,9.67 -2.68,17.6 0.32,17.35zM387.7,213.06a38.13,38.84 0,0 0,-15.66 -12.07c5.03,10.99 13.39,15.04 15.66,12.07z"
      android:strokeWidth="0.16"
      android:fillColor="#406325"
      android:strokeColor="#24420e"/>
  <path
      android:pathData="M386.41,206.45a35.54,34.89 90,0 0,-11.76 -13.8c2.52,10.74 9.25,15.95 11.76,13.8zM390.22,218.76c-2.84,-3.31 -12.33,-6.53 -13.79,-6.36 5.68,7.44 12.66,9.01 13.79,6.36zM387.95,257.11a33.06,32.45 90,0 0,16.47 -4.71c-10.47,-2.31 -17.44,1.57 -16.47,4.71zM387.78,252.4c0,-4.63 -5.11,-13.88 -6.49,-14.88 -0.41,9.92 3.98,16.11 6.49,14.88z"
      android:strokeWidth="0.16"
      android:fillColor="#406325"
      android:strokeColor="#24420e"/>
  <path
      android:pathData="M388.19,241.08a41.32,40.57 90,0 0,-8.6 -19.5c-0.65,13.39 5.11,21.32 8.6,19.5zM345.19,303.3a39.67,38.94 90,0 0,18.01 9.09c-7.14,-10.16 -16.31,-12.48 -17.93,-9.09z"
      android:strokeWidth="0.16"
      android:fillColor="#406325"
      android:strokeColor="#24420e"/>
  <path
      android:pathData="M379.75,268.35c-3.98,0.5 -6.57,-9.42 -1.14,-22.15 -0.41,11.73 0.16,21.24 1.14,22.15zM336.43,304.38a41.32,40.57 90,0 1,17.77 9.5c-12.25,0.08 -19.47,-6.03 -17.85,-9.42zM336.43,297.85c1.62,-6.61 12.17,-17.52 14.2,-18.26 -2.92,14.13 -11.12,21.07 -14.2,18.26zM353.71,294.46a44.63,43.81 90,0 1,6.08 -21.49c2.43,13.55 -2.43,22.73 -6.08,21.49zM371.15,282.4a40.49,39.76 90,0 1,17.52 -9.26c-7.22,10 -16.06,12.48 -17.61,9.26zM392,238.35c0.81,-5.21 7.87,-14.79 9.41,-15.62 -0.97,11.24 -6.73,17.35 -9.41,15.7z"
      android:strokeWidth="0.16"
      android:fillColor="#67923d"
      android:strokeColor="#24420e"/>
  <path
      android:pathData="M382.51,269.84a42.15,41.38 90,0 1,19.8 -4.55c-9.57,8.02 -19.07,8.1 -19.8,4.55zM390.87,249.59a36.36,35.7 90,0 1,14.36 -11.9c-4.71,10.74 -12.41,14.71 -14.36,11.9zM389.65,211.91c0,-6.28 6.49,-18.76 8.11,-20 0.89,13.31 -4.62,21.65 -8.11,20zM384.05,190.5a33.06,32.45 90,0 1,-0.08 -16.53c4.46,9.01 3,16.53 0,16.53z"
      android:strokeWidth="0.16"
      android:fillColor="#67923d"
      android:strokeColor="#24420e"/>
  <path
      android:pathData="M380.56,188.68a41.32,40.57 90,0 1,-8.44 -18.26c9.57,7.69 11.68,16.86 8.44,18.26zM390.06,217.19c-0.24,0.74 2.43,-6.36 11.52,-12.64 -3.25,11.82 -8.92,15.12 -11.52,12.64zM362.39,291.49a50.41,49.49 90,0 1,23.69 -5.54c-11.52,9.75 -22.88,9.92 -23.69,5.54zM373.99,274.3c-3,0.17 -4.87,-7.69 -0.41,-17.35 -0.57,9.09 -0.32,16.53 0.41,17.35zM387.7,213.06c-2.19,2.98 -10.55,-0.99 -15.58,-12.07a65.29,64.1 90,0 0,15.58 12.07zM386.32,206.45c-2.52,2.15 -9.17,-2.98 -11.68,-13.8 5.44,7.77 10.55,13.64 11.68,13.8zM390.22,218.85c-1.14,2.56 -8.03,1.07 -13.71,-6.36 8.44,4.88 11.76,6.94 13.79,6.36zM388.11,257.03c-1.05,-3.14 5.92,-6.86 16.39,-4.55a57.02,55.98 90,0 0,-16.39 4.55zM387.78,252.4c-2.6,1.24 -6.9,-4.88 -6.49,-14.79a57.02,55.98 90,0 0,6.49 14.88z"
      android:strokeWidth="0.16"
      android:fillColor="#67923d"
      android:strokeColor="#24420e"/>
  <path
      android:pathData="M388.11,241.24c-3.49,1.82 -9.17,-6.12 -8.52,-19.42 3.57,10.58 7.3,18.92 8.52,19.42zM345.35,303.3c1.62,-3.47 10.63,-1.16 17.85,8.93 -8.92,-5.54 -16.63,-9.34 -17.85,-8.93z"
      android:strokeWidth="0.16"
      android:fillColor="#67923d"
      android:strokeColor="#24420e"/>
  <path
      android:pathData="M353.71,294.3c3.73,1.32 8.52,-7.77 6.17,-21.4 -2.27,11.57 -5.03,20.74 -6.17,21.49z"
      android:strokeWidth="0.16"
      android:fillColor="#406325"
      android:strokeColor="#24420e"/>
  <path
      android:pathData="M362.07,290a45.45,44.62 90,0 0,3.73 -22.31c-8.11,11.24 -7.63,21.73 -3.73,22.31zM336.35,304.46c-1.62,3.39 5.44,9.5 17.69,9.34 -9.74,-3.97 -17.2,-8.1 -17.69,-9.34zM336.51,297.77c3.08,2.81 11.2,-4.05 14.12,-18.18 -6.57,10.25 -12.74,18.02 -14.12,18.18zM371.15,282.4c1.46,3.31 10.3,0.83 17.61,-9.17a69.42,68.15 90,0 1,-17.61 9.17zM392.08,238.35c2.68,1.74 8.36,-4.38 9.33,-15.54a61.98,60.85 90,0 1,-9.33 15.54zM382.51,269.84c0.81,3.64 10.14,3.47 19.8,-4.55 -10.14,3.31 -18.66,5.12 -19.72,4.55zM390.95,249.51c2.03,2.81 9.65,-1.16 14.36,-11.9a61.98,60.85 90,0 1,-14.36 11.9zM389.81,211.82c3.41,1.65 8.92,-6.61 7.95,-19.83 -3.25,10.74 -6.82,19.26 -8.03,19.83zM384.13,190.42c2.84,0.08 4.38,-7.44 -0.16,-16.53 0.81,8.68 0.81,15.78 0.16,16.53zM380.64,188.6c3.25,-1.4 1.14,-10.5 -8.44,-18.18a66.53,67.76 0,0 1,8.44 18.18zM390.06,217.28c2.6,2.48 8.28,-0.83 11.52,-12.73q-9.57,12.48 -11.52,12.73zM362.47,291.57c0.81,4.3 12.09,4.13 23.53,-5.54 -12.09,3.97 -22.23,6.28 -23.53,5.54z"
      android:strokeWidth="0.16"
      android:fillColor="#406325"
      android:strokeColor="#24420e"/>
  <path
      android:pathData="M367.99,285.21a33.88,33.26 90,0 0,2.92 -16.61c-6.08,8.26 -5.84,16.11 -2.92,16.53zM383.89,261.49c2.92,-4.71 3.25,-17.27 2.43,-19.01 -6.73,9.83 -5.92,18.76 -2.43,19.01z"
      android:strokeWidth="0.16"
      android:fillColor="#406325"
      android:strokeColor="#24420e"/>
  <path
      android:pathData="M362.07,289.92c-3.89,-0.5 -4.3,-10.99 3.65,-22.15 -2.92,11.4 -4.38,20.99 -3.65,22.15zM390.62,233.47c-0.81,-6.45 3.25,-16.61 5.35,-18.59 2.11,11.9 -2.19,19.83 -5.35,18.59zM367.99,285.12c-2.92,-0.41 -3.16,-8.26 2.92,-16.53a55.37,54.36 90,0 0,-2.92 16.53zM386.97,199.01c-1.78,-4.96 0,-16.53 0.97,-18.02 4.3,10.08 2.11,18.26 -0.97,18.02zM339.83,298.26c2.03,-6.61 13.14,-17.35 15.25,-18.18 -3.49,14.46 -12.17,21.16 -15.25,18.18zM384.13,263.89a37.19,36.51 90,0 1,13.71 -13.06c-4.06,11.24 -11.52,15.78 -13.79,13.06zM383.97,261.41c-3.49,-0.33 -4.3,-9.09 2.43,-18.92a60.33,59.23 90,0 0,-2.43 18.92z"
      android:strokeWidth="0.16"
      android:fillColor="#67923d"
      android:strokeColor="#24420e"/>
  <path
      android:pathData="M390.62,234.96c4.22,1.57 7.3,-8.1 5.35,-19.75a80.99,79.51 90,0 1,-5.35 19.83z"
      android:strokeWidth="0.16"
      android:fillColor="#406325"
      android:strokeColor="#24420e"/>
  <path
      android:pathData="M391.27,234.63c0.41,-6.2 -5.68,-19.01 -7.3,-20.41 -1.62,13.06 3.65,21.73 7.3,20.41zM386.97,198.93c3.16,0.33 5.27,-7.77 0.97,-17.93 0.32,9.5 -0.16,17.19 -0.97,17.93zM339.92,298.18c3.08,3.06 11.68,-3.64 15.17,-18.02 -7.14,10.16 -13.79,17.85 -15.17,18.02zM384.21,263.89c2.27,2.73 9.74,-1.74 13.79,-13.06a63.63,62.47 90,0 1,-13.79 13.06z"
      android:strokeWidth="0.16"
      android:fillColor="#406325"
      android:strokeColor="#24420e"/>
  <path
      android:pathData="M391.19,234.55c-3.65,1.4 -8.84,-7.19 -7.22,-20.25 2.84,10.91 6.08,19.59 7.22,20.25z"
      android:strokeWidth="0.16"
      android:fillColor="#67923d"
      android:strokeColor="#24420e"/>
  <path
      android:pathData="M388.68,202.65m-0.89,0a0.91,0.89 90,1 1,1.78 0a0.91,0.89 90,1 1,-1.78 0"
      android:strokeWidth="0.16"
      android:fillColor="#ba1f3e"
      android:strokeColor="#511124"/>
  <path
      android:pathData="M391.76,212.48m-0.89,0a0.91,0.89 90,1 1,1.78 0a0.91,0.89 90,1 1,-1.78 0"
      android:strokeWidth="0.16"
      android:fillColor="#ba1f3e"
      android:strokeColor="#511124"/>
  <path
      android:pathData="M392.9,243.47m-1.05,0a1.07,1.05 90,1 1,2.11 0a1.07,1.05 90,1 1,-2.11 0"
      android:strokeWidth="0.16"
      android:fillColor="#ba1f3e"
      android:strokeColor="#511124"/>
  <path
      android:pathData="M388.68,237.69m-1.05,0a1.07,1.05 90,1 1,2.11 0a1.07,1.05 90,1 1,-2.11 0"
      android:strokeWidth="0.16"
      android:fillColor="#ba1f3e"
      android:strokeColor="#511124"/>
  <path
      android:pathData="M376.75,270.66m-1.05,0a1.07,1.05 90,1 1,2.11 0a1.07,1.05 90,1 1,-2.11 0"
      android:strokeWidth="0.16"
      android:fillColor="#ba1f3e"
      android:strokeColor="#511124"/>
  <path
      android:pathData="M381.86,272.89m-1.05,0a1.07,1.05 90,1 1,2.11 0a1.07,1.05 90,1 1,-2.11 0"
      android:strokeWidth="0.16"
      android:fillColor="#ba1f3e"
      android:strokeColor="#511124"/>
  <path
      android:pathData="M347.46,299.67m-1.05,0a1.07,1.05 90,1 1,2.11 0a1.07,1.05 90,1 1,-2.11 0"
      android:strokeWidth="0.16"
      android:fillColor="#ba1f3e"
      android:strokeColor="#511124"/>
  <path
      android:pathData="M341.78,303.8m-1.05,0a1.07,1.05 90,1 1,2.11 0a1.07,1.05 90,1 1,-2.11 0"
      android:strokeWidth="0.16"
      android:fillColor="#ba1f3e"
      android:strokeColor="#511124"/>
  <path
      android:pathData="M382.59,198.02m-0.89,0a0.91,0.89 90,1 1,1.78 0a0.91,0.89 90,1 1,-1.78 0"
      android:strokeWidth="0.16"
      android:fillColor="#ba1f3e"
      android:strokeColor="#511124"/>
  <path
      android:pathData="M282.96,269.92c-0.16,0.83 0.65,1.9 1.62,1.65 0.41,0 0.57,-0.41 0.16,-0.41q-0.57,0.17 -0.97,-0.25a1.65,1.62 90,0 1,-0.32 -1.65z"
      android:strokeWidth="0.08"
      android:fillColor="#8c959d"
      android:strokeColor="#485654"/>
  <path
      android:pathData="M285.31,271.57c-0.81,0.99 -2.03,1.4 -2.76,0.74q-0.81,-0.74 -0.41,-1.65l-0.81,1.07s0.49,1.07 1.3,1.49c1.05,0.41 2.43,-0.25 3.25,-1.32 0.65,-0.83 1.3,-1.98 0.81,-2.98a3.31,3.25 90,0 0,-1.62 -1.4l-0.81,0.83q0.65,-0.17 1.38,0.33c1.05,0.83 0.49,2.07 -0.32,2.89"
      android:strokeWidth="0.08"
      android:fillColor="#8c959d"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="m285.31,271.57 l-0.89,0.83q-0.57,0.33 -1.22,0.25l-0.57,-0.25 -0.41,-0.5q-0.16,-0.25 -0.24,-0.58t0.16,-0.66l0.08,0.08 -0.89,1.07l0,-0.08l0.81,1.07 0.57,0.33 0.65,0.08a2.48,2.43 90,0 0,1.3 -0.41q0.65,-0.33 1.05,-0.83l0.81,-1.16q0.32,-0.58 0.24,-1.32 0,-0.33 -0.16,-0.66a3.31,3.25 90,0 0,-1.54 -1.32l0.08,0l-0.81,0.91l-0.08,0q0.24,-0.17 0.57,-0.17l0.65,0.17q0.57,0.33 0.73,0.99 0.08,0.66 -0.16,1.16 -0.24,0.66 -0.73,0.99m0,0q0.49,-0.33 0.65,-0.99t0.16,-1.16q-0.16,-0.58 -0.73,-0.83 -0.57,-0.41 -1.05,-0.17l-0.08,0l0.81,-0.99a3.31,3.25 90,0 1,1.62 1.4q0.24,0.25 0.24,0.66 0,0.83 -0.32,1.4 -0.24,0.66 -0.81,1.16 -0.41,0.58 -1.05,0.91 -0.57,0.41 -1.38,0.41a1.65,1.62 90,0 1,-1.3 -0.5l-0.81,-1.16 0.81,-1.07l0.16,0l-0.08,0.66q0,0.25 0.16,0.58l0.41,0.41q0.16,0.25 0.49,0.25 0.65,0.08 1.22,-0.17z"
      android:strokeWidth="0.08"
      android:fillColor="#485654"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="m348.6,198.02 l-32.53,32.64L282.72,263.06l-3.73,4.46c-1.62,1.9 -3.57,4.38 -5.84,6.61 -0.41,0.58 -0.89,0.33 -1.38,0.66 -1.46,0.83 -3,2.31 -4.06,3.55L257.73,289.5q-0.97,0.99 -0.32,1.65l5.35,7.27c0.97,0.99 2.11,1.49 2.6,0.66 2.52,-4.55 8.92,-11.4 11.36,-16.78 1.38,-3.31 3.16,-9.17 4.46,-10.58 1.62,-1.82 5.76,-6.45 9.57,-10.5l0.81,-0.83q0.24,-0.5 0.73,-0.83c19.07,-19.83 41.13,-43.88 57.2,-60.82z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.16"
      android:fillColor="#6c301e"
      android:strokeColor="#351710"/>
  <path
      android:pathData="M281.01,263.14q-1.38,-0.17 -2.35,-1.16c0.97,0.33 1.95,0.66 2.84,0.25z"
      android:strokeWidth="0.08"
      android:fillColor="#8c959d"
      android:strokeColor="#485654"/>
  <path
      android:pathData="m382.51,173.89 l-25.8,21.65q-0.41,0.41 -0.73,0l-2.35,-1.98l0,-0.5l1.14,-1.16q0.16,-0.17 -0.08,-0.5l-0.49,-0.5q-0.32,-0.08 -0.57,0.17l-1.14,1.24q-0.97,0.08 -1.14,1.16c-15.58,15.45 -29.05,29.25 -44.54,44.46l-18.34,17.85c-0.65,0.83 -3.49,2.4 -5.35,3.64q-1.05,0.66 -1.46,1.24l-1.46,4.55c-0.49,0.99 -2.03,3.31 -2.03,3.47l3.08,3.06 9.01,-9.83 0.73,-0.83a10.74,10.55 90,0 0,-2.11 -2.23l-0.57,-0.41q0,-0.33 0.41,-0.58c21.42,-21.16 44.62,-44.38 63.45,-63.3l0.81,0l2.35,2.56q0.81,0.83 1.38,0.33l25.88,-23.55z"
      android:strokeWidth="0.16"
      android:fillColor="#b2b6ba"
      android:strokeColor="#485654"/>
  <path
      android:pathData="M280.28,265.04l0.49,0l0.41,-0.08 0.57,-1.98 0.65,-1.98q-0.24,-0.25 -0.57,-0.33 0.32,0 0.57,0.33l-0.57,1.98 -0.57,1.98 -0.49,0.17z"
      android:strokeWidth="0.08"
      android:fillColor="#485654"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M286.12,263.14m-0.49,0a0.5,0.49 90,1 1,0.97 0a0.5,0.49 90,1 1,-0.97 0"
      android:strokeWidth="0.16"
      android:fillColor="#00000000"
      android:strokeColor="#485654"/>
  <path
      android:pathData="M283.04,266.28m-0.49,0a0.5,0.49 90,1 1,0.97 0a0.5,0.49 90,1 1,-0.97 0"
      android:strokeWidth="0.16"
      android:fillColor="#00000000"
      android:strokeColor="#485654"/>
  <path
      android:pathData="m288.4,258.43 l-1.46,-1.32z"
      android:strokeWidth="0.08"
      android:fillColor="#485654"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="m338.05,206.04 l2.84,2.89 0.49,-0.5 -2.84,-2.81 -0.49,0.5z"
      android:strokeWidth="0.08"
      android:fillColor="#8c959d"
      android:strokeColor="#485654"/>
  <path
      android:pathData="M343.75,271.06c0.12,0.83 -0.74,1.87 -1.7,1.57 -0.41,-0.02 -0.55,-0.44 -0.14,-0.42q0.56,0.19 0.98,-0.2a1.65,1.62 90,0 0,0.4 -1.63z"
      android:strokeWidth=".1"
      android:fillColor="#8c959d"
      android:strokeColor="#485654"/>
  <path
      android:pathData="M341.32,272.59c0.76,1.03 1.96,1.5 2.72,0.88q0.85,-0.7 0.48,-1.63l0.76,1.11s-0.54,1.05 -1.37,1.42c-1.07,0.36 -2.42,-0.37 -3.18,-1.48 -0.61,-0.86 -1.2,-2.05 -0.67,-3.01a3.31,3.25 90,0 1,1.69 -1.32l0.77,0.87q-0.64,-0.2 -1.39,0.26c-1.09,0.77 -0.59,2.04 0.19,2.91"
      android:strokeWidth=".1"
      android:fillColor="#8c959d"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="m341.32,272.59l0.85,0.87q0.55,0.36 1.2,0.31l0.58,-0.22 0.43,-0.48q0.17,-0.24 0.27,-0.57t-0.13,-0.67l-0.08,0.08 0.84,1.12l0,-0.08l-0.86,1.03 -0.58,0.3 -0.65,0.05a2.48,2.43 90,0 1,-1.28 -0.48q-0.63,-0.36 -1.01,-0.88l-0.75,-1.2q-0.3,-0.59 -0.18,-1.33 0.02,-0.33 0.19,-0.65a3.31,3.25 90,0 1,1.6 -1.24l-0.08,-0l0.77,0.95l0.08,0q-0.24,-0.18 -0.56,-0.19l-0.66,0.13q-0.58,0.3 -0.78,0.95 -0.11,0.66 0.11,1.16 0.21,0.67 0.68,1.03m-0,0q-0.47,-0.35 -0.6,-1.02t-0.11,-1.16q0.19,-0.57 0.77,-0.79 0.59,-0.38 1.06,-0.11l0.08,0l-0.76,-1.03a3.31,3.25 90,0 0,-1.69 1.32q-0.25,0.24 -0.27,0.65 -0.04,0.83 0.26,1.42 0.21,0.67 0.75,1.2 0.38,0.6 1.01,0.96 0.55,0.44 1.36,0.48a1.65,1.62 90,0 0,1.32 -0.43l0.87,-1.12 -0.76,-1.11l-0.16,-0.01l0.05,0.66q-0.01,0.25 -0.19,0.57l-0.42,0.39q-0.17,0.24 -0.5,0.22 -0.65,0.05 -1.21,-0.23z"
      android:strokeWidth=".1"
      android:fillColor="#485654"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="m281.64,195.98l30.93,34.22L344.32,264.22l3.51,4.64c1.53,1.98 3.36,4.55 5.52,6.89 0.38,0.6 0.88,0.37 1.35,0.73 1.42,0.9 2.89,2.46 3.88,3.75L368.01,291.87q0.92,1.04 0.24,1.67l-5.7,7c-1.02,0.94 -2.18,1.38 -2.62,0.53 -2.29,-4.66 -8.37,-11.83 -10.54,-17.32 -1.22,-3.37 -2.72,-9.32 -3.95,-10.79 -1.53,-1.9 -5.44,-6.72 -9.06,-10.96l-0.77,-0.87q-0.22,-0.51 -0.69,-0.86c-18.09,-20.76 -38.98,-45.88 -54.21,-63.6z"
      android:strokeLineJoin="round"
      android:strokeWidth=".2"
      android:fillColor="#6c301e"
      android:strokeColor="#351710"/>
  <path
      android:pathData="M346.02,264.38q1.39,-0.1 2.41,-1.04c-0.99,0.28 -1.98,0.56 -2.85,0.11z"
      android:strokeWidth=".1"
      android:fillColor="#8c959d"
      android:strokeColor="#485654"/>
  <path
      android:pathData="m248.92,170.19l24.73,22.91q0.39,0.43 0.73,0.04l2.45,-1.86l0.02,-0.5l-1.08,-1.21q-0.15,-0.17 0.1,-0.49l0.51,-0.47q0.33,-0.07 0.56,0.19l1.08,1.29q0.97,0.13 1.08,1.21c14.82,16.21 27.61,30.66 42.36,46.62l17.46,18.74c0.61,0.86 3.37,2.57 5.17,3.9q1.02,0.71 1.4,1.31l1.24,4.61c0.44,1.01 1.87,3.4 1.86,3.57l-3.23,2.9 -8.52,-10.27 -0.69,-0.86a10.74,10.55 90,0 1,2.21 -2.12l0.59,-0.38q0.02,-0.33 -0.38,-0.6c-20.38,-22.2 -42.44,-46.55 -60.33,-66.38l-0.81,-0.04l-2.47,2.44q-0.85,0.79 -1.39,0.26l-24.72,-24.81z"
      android:strokeWidth=".2"
      android:fillColor="#b2b6ba"
      android:strokeColor="#485654"/>
  <path
      android:pathData="M346.65,266.32l-0.49,-0.02l-0.4,-0.1 -0.47,-2.01 -0.55,-2.01q0.25,-0.24 0.58,-0.3 -0.32,-0.02 -0.58,0.3l0.47,2.01 0.47,2.01 0.48,0.19z"
      android:strokeWidth=".1"
      android:fillColor="#485654"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M340.91,264.13m0.49,0.02a0.5,0.49 90,1 0,-0.97 -0.05a0.5,0.49 90,1 0,0.97 0.05"
      android:strokeWidth=".2"
      android:fillColor="#00000000"
      android:strokeColor="#485654"/>
  <path
      android:pathData="M343.84,267.42m0.49,0.02a0.5,0.49 90,1 0,-0.97 -0.05a0.5,0.49 90,1 0,0.97 0.05"
      android:strokeWidth=".2"
      android:fillColor="#00000000"
      android:strokeColor="#485654"/>
  <path
      android:pathData="m338.87,259.31l1.52,-1.25z"
      android:strokeWidth=".1"
      android:fillColor="#485654"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="m291.79,204.51l-2.97,2.75 -0.46,-0.52 2.97,-2.67 0.46,0.52z"
      android:strokeWidth=".1"
      android:fillColor="#8c959d"
      android:strokeColor="#485654"/>
  <path
      android:pathData="M282.72,293.97a46.28,45.43 90,0 0,-23.77 0.74c13.22,6.61 23.93,3.8 23.69,-0.83zM354.93,296.69c6.33,-2.73 21.74,-0.58 23.61,0.83 -13.14,6.53 -23.93,3.72 -23.69,-0.83z"
      android:strokeWidth="0.16"
      android:fillColor="#67923d"
      android:strokeColor="#24420e"/>
  <path
      android:pathData="M282.55,294.05c0.24,4.55 -10.39,7.35 -23.61,0.74 12.41,0.83 22.55,0.33 23.53,-0.83zM354.93,296.78c-0.16,4.55 10.47,7.35 23.69,0.74 -12.41,0.83 -22.55,0.33 -23.61,-0.83z"
      android:strokeWidth="0.16"
      android:fillColor="#406325"
      android:strokeColor="#24420e"/>
  <path
      android:pathData="M343.16,289.5a196.68,193.1 90,0 0,-61.74 -56.2c2.84,-0.17 9.25,3.22 11.93,4.96 19.39,12.73 35.94,29.59 53.22,49.42 -0.97,0.99 -1.62,1.65 -2.76,2.48z"
      android:strokeWidth="0.16"
      android:fillColor="#b2b6ba"
      android:strokeColor="#485654"/>
  <path
      android:pathData="M344.87,289.34 L343.57,290.33c-18.66,-24.46 -44.62,-47.85 -62.23,-57.02 24.42,11.4 44.95,34.46 63.28,55.86l0.08,0z"
      android:fillColor="#8c959d"/>
  <path
      android:pathData="M344.87,289.34 L343.57,290.33a250.4,245.83 90,0 0,-28.56 -31.4,235.52 231.23,90 0,0 -24.66,-20.08q-4.38,-2.98 -9.01,-5.54l0,-0.17q9.74,4.63 18.25,10.99 8.52,6.28 16.23,13.72a297.5,292.08 90,0 1,14.85 15.29q7.22,8.02 14.12,16.11zM344.87,289.34q-6.98,-8.18 -14.2,-16.03a293.37,288.02 90,0 0,-14.85 -15.29q-7.71,-7.35 -16.23,-13.72t-18.17,-10.91l0,-0.17a161.97,159.02 90,0 1,17.61 11.82,231.23 235.52,0 0,1 30.99,29.01 259.49,254.76 90,0 1,13.63 16.36z"
      android:fillColor="#485654"/>
  <path
      android:pathData="M350.46,285.37q-2.43,0.74 -4.3,1.9c-0.16,1.16 -1.62,2.48 -2.84,2.73l-0.49,-0.66l-0.16,0l-0.49,0.25q-0.81,-0.08 -1.3,0.83c-0.32,0.83 0.24,1.9 0.97,2.56 0.81,0.58 1.14,0.83 1.95,0.83 0.89,-0.25 1.38,-1.07 1.62,-1.32 3,3.72 5.27,5.78 9.09,8.76 1.7,0 2.6,-1.07 2.03,-2.31q-0.32,-0.66 -1.05,-0.5l0,-0.41c1.95,-1.82 2.84,-4.13 0.73,-8.1 -1.78,-3.31 -3.81,-4.46 -5.68,-4.46l-0.08,0zM355.49,289.17q0.57,0.83 0.81,1.65c1.3,2.48 0.24,5.54 -1.87,6.45l-0.16,0c0.32,-0.58 -0.65,-1.82 -1.14,-1.4 0.24,-0.58 -0.57,-1.65 -1.22,-1.4 0.32,-0.58 -0.41,-1.57 -1.14,-1.24 0.32,-0.58 -0.24,-1.49 -1.05,-1.32 0.24,-0.66 -0.32,-1.49 -1.05,-1.32q0,-0.91 -0.41,-1.24c0.81,-0.66 1.87,-1.74 2.76,-2.23 2.19,-0.99 3.57,0.83 4.46,2.07z"
      android:strokeWidth="0.16"
      android:fillColor="#fab81c"
      android:strokeColor="#6c301e"/>
  <path
      android:pathData="M356.47,291.82q-0.65,-0.5 -0.49,-0.91 0.32,-0.5 0.97,-0.17t0.57,0.83q-0.32,0.5 -1.05,0.25z"
      android:strokeWidth="0.08"
      android:fillColor="#fab81c"
      android:strokeColor="#6c301e"/>
  <path
      android:pathData="M356.22,290.99q-0.65,-0.41 -0.49,-0.99 0.32,-0.41 0.97,-0.08t0.57,0.83q-0.32,0.5 -1.05,0.25z"
      android:strokeWidth="0.08"
      android:fillColor="#fab81c"
      android:strokeColor="#6c301e"/>
  <path
      android:pathData="M355.74,290q-0.65,-0.33 -0.49,-0.83 0.32,-0.5 0.97,-0.25 0.65,0.5 0.49,0.91t-0.97,0.17z"
      android:strokeWidth="0.08"
      android:fillColor="#fab81c"
      android:strokeColor="#6c301e"/>
  <path
      android:pathData="M355.09,289.17q-0.65,-0.41 -0.49,-0.91 0.32,-0.41 0.97,-0.17 0.65,0.5 0.57,0.91 -0.32,0.5 -1.05,0.17z"
      android:strokeWidth="0.08"
      android:fillColor="#fab81c"
      android:strokeColor="#6c301e"/>
  <path
      android:pathData="M354.28,288.35q-0.73,-0.41 -0.49,-0.83l0,-0.17c0.16,0.08 0.57,0.17 0.49,-0.08q0,-0.25 0,-0.17l0.49,0.17q0.65,0.5 0.49,0.91t-0.97,0.17z"
      android:strokeWidth="0.08"
      android:fillColor="#fab81c"
      android:strokeColor="#6c301e"/>
  <path
      android:pathData="M345.43,292.48c0.57,-1.49 1.62,-2.81 2.84,-3.14m-2.68,3.39c1.05,0.17 2.68,-1.07 3.08,-2.15m-2.11,3.31c1.3,0.33 2.68,-0.99 3.25,-1.98m-2.03,3.31c1.62,0 2.52,-1.07 3,-1.98m-1.78,3.22c1.38,0 2.6,-1.07 2.92,-1.98m-1.78,3.14q2.27,-0.17 3,-1.65m-1.62,2.81c1.38,0.08 2.43,-0.74 2.76,-1.49m-1.3,2.73c1.3,0 2.19,-0.83 2.52,-1.65"
      android:fillColor="#fab81c"/>
  <path
      android:pathData="M345.43,292.48a5.78,5.68 90,0 1,1.87 -2.64q0.32,-0.33 0.97,-0.5l-0.97,0.5 -0.81,0.83a6.61,6.49 90,0 0,-1.05 1.82m0.16,0.25q0.49,0 0.97,-0.17l0.81,-0.5 0.81,-0.66 0.49,-0.83 -0.49,0.83a4.13,4.06 90,0 1,-1.62 1.16q-0.49,0.25 -0.97,0.17m0.97,1.16l0.97,0l0.81,-0.5a4.96,4.87 90,0 0,1.38 -1.49l-0.57,0.83 -0.73,0.74 -0.81,0.41zM347.79,295.21q0.49,0 0.89,-0.17l0.81,-0.41 0.81,-0.66 0.49,-0.83a3.31,3.25 90,0 1,-1.22 1.57l-0.81,0.41zM349,296.45q0.97,0 1.7,-0.66a4.13,4.06 90,0 0,1.22 -1.32q-0.08,0.41 -0.49,0.83l-0.65,0.58 -0.81,0.5zM350.14,297.6q0.49,0 0.89,-0.17a4.13,4.06 90,0 0,1.62 -0.83l0.49,-0.74 -0.49,0.83q-0.32,0.33 -0.81,0.5 -0.81,0.5 -1.62,0.41zM351.52,298.76a4.13,4.06 90,0 0,1.62 -0.41l0.65,-0.41q0.32,-0.33 0.49,-0.66 -0.08,0.41 -0.41,0.66a3.31,3.25 90,0 1,-2.43 0.83zM352.98,300 L353.79,299.92 354.44,299.59 355.01,299.09 355.49,298.43a2.48,2.43 90,0 1,-2.52 1.57"
      android:fillColor="#6c301e"/>
  <path
      android:pathData="M293.99,287.34a193.1,196.68 0,0 1,64.91 -52.37c-2.83,-0.34 -9.42,2.66 -12.19,4.23 -20.1,11.53 -37.6,27.36 -56.01,46.12 0.91,1.05 1.52,1.75 2.61,2.64z"
      android:strokeWidth=".2"
      android:fillColor="#b2b6ba"
      android:strokeColor="#485654"/>
  <path
      android:pathData="M292.3,287.07L293.54,288.14c20.05,-23.29 47.33,-45.07 65.44,-53.16 -25.04,9.91 -46.88,31.69 -66.43,51.94l-0.08,-0z"
      android:fillColor="#8c959d"/>
  <path
      android:pathData="M292.3,287.07L293.54,288.14a250.4,245.83 90,0 1,30.34 -29.62,231.23 235.52,0 0,1 25.79,-18.56q4.55,-2.71 9.31,-4.98l0.01,-0.16q-9.99,4.03 -18.86,9.87 -8.87,5.75 -17,12.71a297.5,292.08 90,0 0,-15.71 14.36q-7.67,7.57 -15.03,15.23zM292.3,287.07q7.44,-7.75 15.11,-15.15a293.37,288.02 90,0 1,15.71 -14.36q8.12,-6.88 17,-12.71t18.78,-9.79l0.01,-0.16a161.97,159.02 90,0 0,-18.26 10.73,235.52 231.23,90 0,0 -32.63,27.08 254.76,259.49 0,0 0,-14.56 15.51z"
      android:fillColor="#485654"/>
  <path
      android:pathData="M286.94,282.77q2.39,0.89 4.18,2.16c0.09,1.16 1.48,2.57 2.68,2.89l0.52,-0.63l0.16,0.01l0.47,0.28q0.81,-0.03 1.25,0.9c0.28,0.84 -0.35,1.88 -1.12,2.5 -0.84,0.53 -1.18,0.76 -1.99,0.71 -0.88,-0.3 -1.31,-1.16 -1.54,-1.42 -3.21,3.53 -5.6,5.46 -9.58,8.2 -1.7,-0.1 -2.53,-1.23 -1.89,-2.43q0.36,-0.64 1.08,-0.43l0.02,-0.41c-1.84,-1.93 -2.59,-4.3 -0.26,-8.13 1.97,-3.19 4.07,-4.22 5.93,-4.11l0.08,0zM281.7,286.27q-0.62,0.79 -0.91,1.6c-1.44,2.4 -0.57,5.51 1.49,6.55l0.16,0.01c-0.29,-0.6 0.75,-1.78 1.22,-1.33 -0.21,-0.59 0.66,-1.62 1.3,-1.33 -0.29,-0.6 0.5,-1.54 1.21,-1.17 -0.29,-0.6 0.33,-1.47 1.13,-1.26 -0.2,-0.67 0.41,-1.47 1.13,-1.26q0.05,-0.91 0.48,-1.21c-0.77,-0.71 -1.76,-1.85 -2.62,-2.39 -2.13,-1.12 -3.61,0.61 -4.57,1.79z"
      android:strokeWidth=".2"
      android:fillColor="#fab81c"
      android:strokeColor="#6c301e"/>
  <path
      android:pathData="M280.57,288.85q0.68,-0.46 0.54,-0.88 -0.3,-0.51 -0.96,-0.22t-0.62,0.79q0.3,0.51 1.04,0.31z"
      android:strokeWidth=".1"
      android:fillColor="#fab81c"
      android:strokeColor="#6c301e"/>
  <path
      android:pathData="M280.86,288.04q0.67,-0.37 0.54,-0.96 -0.3,-0.43 -0.97,-0.14t-0.62,0.79q0.3,0.51 1.04,0.31z"
      android:strokeWidth=".1"
      android:fillColor="#fab81c"
      android:strokeColor="#6c301e"/>
  <path
      android:pathData="M281.41,287.08q0.67,-0.29 0.53,-0.8 -0.3,-0.51 -0.96,-0.31 -0.68,0.46 -0.54,0.88t0.96,0.22z"
      android:strokeWidth=".1"
      android:fillColor="#fab81c"
      android:strokeColor="#6c301e"/>
  <path
      android:pathData="M282.1,286.29q0.67,-0.37 0.54,-0.88 -0.3,-0.43 -0.96,-0.22 -0.68,0.46 -0.62,0.87 0.3,0.51 1.04,0.23z"
      android:strokeWidth=".1"
      android:fillColor="#fab81c"
      android:strokeColor="#6c301e"/>
  <path
      android:pathData="M282.96,285.51q0.75,-0.37 0.53,-0.8l0.01,-0.16c-0.17,0.07 -0.58,0.13 -0.48,-0.11q0.01,-0.25 0.01,-0.16l-0.5,0.14q-0.68,0.46 -0.54,0.88t0.96,0.22z"
      android:strokeWidth=".1"
      android:fillColor="#fab81c"
      android:strokeColor="#6c301e"/>
  <path
      android:pathData="M291.55,290.17c-0.48,-1.52 -1.46,-2.9 -2.65,-3.31m2.48,3.54c-1.06,0.1 -2.61,-1.23 -2.95,-2.33m1.91,3.43c-1.32,0.25 -2.61,-1.15 -3.12,-2.18m1.83,3.42c-1.62,-0.1 -2.45,-1.22 -2.88,-2.16m1.59,3.33c-1.38,-0.08 -2.53,-1.23 -2.8,-2.16m1.6,3.24q-2.26,-0.3 -2.9,-1.83m1.46,2.9c-1.38,-0 -2.39,-0.89 -2.67,-1.65m1.14,2.8c-1.3,-0.08 -2.14,-0.96 -2.41,-1.8"
      android:fillColor="#fab81c"/>
  <path
      android:pathData="M291.55,290.17a5.78,5.68 90,0 0,-1.71 -2.75q-0.3,-0.35 -0.94,-0.55l0.94,0.55 0.76,0.87a6.61,6.49 90,0 1,0.95 1.88m-0.18,0.24q-0.49,-0.03 -0.96,-0.22l-0.78,-0.54 -0.77,-0.71 -0.44,-0.85 0.44,0.85a4.13,4.06 90,0 0,1.55 1.25q0.47,0.28 0.96,0.22m-1.04,1.1l-0.97,-0.06l-0.78,-0.54a4.96,4.87 90,0 1,-1.29 -1.57l0.52,0.86 0.69,0.79 0.79,0.46zM289.04,292.75q-0.49,-0.03 -0.88,-0.22l-0.79,-0.46 -0.77,-0.71 -0.44,-0.85a3.31,3.25 90,0 0,1.12 1.64l0.79,0.46zM287.75,293.92q-0.97,-0.06 -1.66,-0.76a4.13,4.06 90,0 1,-1.14 -1.39q0.06,0.42 0.44,0.85l0.61,0.62 0.78,0.54zM286.55,295q-0.49,-0.03 -0.88,-0.22a4.13,4.06 90,0 1,-1.57 -0.92l-0.44,-0.77 0.44,0.85q0.3,0.35 0.78,0.54 0.78,0.54 1.6,0.51zM285.11,296.08a4.13,4.06 90,0 1,-1.6 -0.51l-0.62,-0.45q-0.3,-0.35 -0.45,-0.69 0.06,0.42 0.37,0.68a3.31,3.25 90,0 0,2.38 0.97zM283.58,297.22L282.77,297.09 282.14,296.72 281.61,296.19 281.16,295.5a2.48,2.43 90,0 0,2.42 1.72"
      android:fillColor="#6c301e"/>
  <path
      android:pathData="M262.6,190.34a42.97,42.19 90,0 0,15.82 -14.79c-12.66,3.47 -18.42,11.82 -15.82,14.88zM380.89,194.22a41.32,40.57 90,0 0,-12.66 -16.45c2.27,12.56 9.74,18.84 12.66,16.53z"
      android:strokeWidth="0.16"
      android:fillColor="#406325"
      android:strokeColor="#24420e"/>
  <path
      android:pathData="M262.6,190.34c-2.68,-2.98 3.08,-11.24 15.74,-14.71 -8.92,6.94 -15.58,13.39 -15.74,14.71zM380.89,194.22c-3,2.48 -10.3,-3.8 -12.58,-16.28 5.84,9.09 11.36,16.11 12.58,16.28z"
      android:strokeWidth="0.16"
      android:fillColor="#67923d"
      android:strokeColor="#24420e"/>
  <path
      android:pathData="M297.48,254.88s-4.95,0.58 -6.41,-6.45c-1.46,-7.52 4.3,-8.26 4.3,-8.26s6.57,-0.74 10.71,-0.83l1.78,14.88z"
      android:strokeWidth="0.16"
      android:strokeColor="#999270">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:centerX="293.59"
          android:centerY="246.78"
          android:gradientRadius="13.39"
          android:type="radial">
        <item android:offset="0.2" android:color="#FFF9F0AA"/>
        <item android:offset="1" android:color="#FFB07E09"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M298.13,252.65s-3.25,0.58 -3.89,-4.63c-0.49,-3.72 1.78,-4.21 1.78,-4.21l7.46,1.32l0,6.78z"
      android:strokeWidth="0.16"
      android:strokeColor="#999270">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:centerX="297"
          android:centerY="250.66"
          android:gradientRadius="8.84"
          android:type="radial">
        <item android:offset="0.2" android:color="#FFF9F0AA"/>
        <item android:offset="1" android:color="#FFB07E09"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M296.02,243.8s4.87,-0.33 7.3,-0.66l1.05,4.71 -5.76,0.33s-0.41,-4.21 -2.6,-4.38z"
      android:strokeWidth="0.16"
      android:strokeColor="#999270">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:centerX="299.02"
          android:centerY="247.03"
          android:gradientRadius="7.95"
          android:type="radial">
        <item android:offset="0.2" android:color="#FFF9F0AA"/>
        <item android:offset="1" android:color="#FFB07E09"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M295.05,240.25s6.08,-1.57 6.9,5.29c0.16,1.82 -0.81,5.95 -3.81,7.11l11.28,-1.24 -1.05,-12.4 -3.73,0.33s-8.11,0.25 -9.57,0.83z"
      android:strokeWidth="0.16"
      android:strokeColor="#999270">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:centerX="302.51"
          android:centerY="245.79"
          android:gradientRadius="14.12"
          android:type="radial">
        <item android:offset="0.2" android:color="#FFF9F0AA"/>
        <item android:offset="1" android:color="#FFB07E09"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M297.48,254.88s42.11,-2.89 46.41,-2.73c12.82,-7.11 0,-35.12 -10.79,-50.82 1.22,-3.55 -24.26,-11.49 -35.7,-10.74q-2.27,0 -4.22,0.25c-6.25,0.66 -6.33,8.84 -3.81,14.05 2.43,4.96 24.66,45.45 9.09,49.83z"
      android:strokeWidth="0.16"
      android:fillColor="#f9f0aa"
      android:strokeColor="#999270"/>
  <path
      android:pathData="M342.59,217.86c-2.84,-6.03 -6.25,-11.82 -9.49,-16.53 1.22,-3.55 -24.26,-11.49 -35.7,-10.74q-2.27,0 -4.22,0.25c-6.25,0.66 -6.33,8.84 -3.81,14.05 0.81,1.49 3.49,6.61 6.41,12.97"
      android:strokeWidth="0.16"
      android:strokeColor="#999270">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="315.09"
          android:startY="206.2"
          android:endX="313.87"
          android:endY="183.72"
          android:type="linear">
        <item android:offset="0.2" android:color="#00B07E09"/>
        <item android:offset="1" android:color="#FFB07E09"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M299.76,227.52c4.54,12.07 7.06,24.79 -1.3,27.19l-0.97,0.17s42.19,-2.89 46.41,-2.73c6.98,-3.88 6.33,-13.88 2.6,-24.79"
      android:strokeWidth="0.16"
      android:strokeColor="#999270">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="322.55"
          android:startY="237.19"
          android:endX="324.5"
          android:endY="261.9"
          android:type="linear">
        <item android:offset="0.2" android:color="#00B07E09"/>
        <item android:offset="1" android:color="#FFB07E09"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M293.59,193.23c-3,0 -3.81,2.98 -3.73,4.79 0.08,4.96 3.73,5.12 3.73,5.12l4.87,-0.25 2.27,-10.08z"
      android:strokeWidth="0.16"
      android:strokeColor="#999270">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:centerX="292.45"
          android:centerY="200.58"
          android:gradientRadius="10.55"
          android:type="radial">
        <item android:offset="0.2" android:color="#FFF9F0AA"/>
        <item android:offset="1" android:color="#FFB07E09"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="m293.59,193.23 l7.14,-0.41 0.81,7.52 -6.33,0.33s2.03,-6.03 -1.62,-7.44z"
      android:strokeWidth="0.16"
      android:strokeColor="#999270">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="296.92"
          android:startY="198.19"
          android:endX="300.08"
          android:endY="190.83"
          android:type="linear">
        <item android:offset="0.2" android:color="#FFF9F0AA"/>
        <item android:offset="1" android:color="#FFB07E09"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M333.1,201.33c5.44,-0.83 5.35,-8.26 1.62,-11.32 -12.58,-0.5 -29.05,-0.08 -41.05,0.83 1.38,0 5.19,0.33 5.6,5.37a6.61,6.49 90,0 1,-2.43 5.78,5.78 5.68,90 0,1 -3.25,1.16l2.92,0l2.43,-0.33c17.04,-2.23 34.08,-1.49 34.08,-1.49z"
      android:strokeWidth="0.16"
      android:strokeColor="#999270">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="314.6"
          android:startY="188.02"
          android:endX="316.71"
          android:endY="215.46"
          android:type="linear">
        <item android:offset="0.2" android:color="#FFF9F0AA"/>
        <item android:offset="1" android:color="#FFB07E09"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M302.59,212.98q-0.16,-0.41 -0.65,-0.66 -0.41,0.08 -0.41,-0.33l0.65,0l1.3,-0.17q0.41,0.25 0,0.41c-0.41,0 -0.16,0.41 -0.08,0.66l1.05,2.48q0.16,0.5 0.57,0.58l0.89,-0.08q0.24,-0.33 0.16,-0.83c-0.08,-0.25 0.41,-0.17 0.41,0l0.32,1.16q0,0.17 -0.32,0.08l-2.43,0.17c-0.24,0 -0.65,0.25 -0.81,0 0,-0.33 0.49,-0.17 0.49,-0.41l-0.24,-0.83zM306.49,212.65q-0.08,-0.33 -0.57,-0.58 -0.41,0.08 -0.49,-0.33 0.24,-0.17 0.57,0l1.38,-0.17c0.24,0 0.32,0.41 0,0.33q-0.32,0.08 -0.16,0.58l1.22,2.81q0,0.33 0.41,0.5 0.32,0 0.57,0.25 -0.08,0.25 -0.49,0.08 -0.57,0 -1.3,0.17 -0.32,0 -0.41,-0.25c-0.08,-0.25 0.41,0 0.49,-0.33q0,-0.33 -0.32,-0.83l-0.89,-2.15zM310.14,213.31q0.16,0.17 0.41,0 0.41,0 0.49,-0.33 0,-0.66 -0.57,-0.99 -0.41,-0.25 -0.89,-0.17l0.08,0.41zM310.63,214.63q0.16,0.41 0.49,0.83 0.32,0.17 0.65,0 0.41,-0.08 0.32,-0.41 -0.08,-0.91 -0.97,-1.24l-0.81,0l0.16,0.41zM309,212.65q-0.16,-0.5 -0.57,-0.83 -0.32,0.08 -0.57,-0.17c0,-0.25 0.49,-0.08 0.65,-0.17 0.73,0 1.46,-0.25 2.11,0q0.97,0.33 1.22,1.32 0,0.5 -0.49,0.66l0.41,0.08q0.81,0.25 1.14,0.99 0.32,0.5 0,1.07 -0.57,0.41 -1.14,0.33 -0.89,0 -1.87,0.17c-0.24,0 -0.32,-0.41 0,-0.41q0.32,-0.08 0.16,-0.5l-1.05,-2.48zM315.01,211.08q0.32,0.08 0.41,0.41l0.24,0.74q-0.32,0.08 -0.49,-0.33 -0.24,-0.33 -0.57,-0.33l-0.97,0q-0.16,0.25 0.08,0.5l0.49,1.16q0.32,0 0.65,-0.25c0,-0.17 -0.24,-0.58 0.16,-0.5q0.24,0.33 0.32,0.74l0.41,0.91c-0.24,0.25 -0.41,-0.08 -0.49,-0.33q-0.32,-0.33 -0.81,-0.25 -0.08,0 0,0.25l0.49,1.24q0.32,0.33 0.81,0.25 0.49,0.08 0.81,-0.33c0.16,-0.17 -0.24,-0.58 0,-0.66q0.41,0.08 0.41,0.5t0.24,0.66q-0.08,0.25 -0.32,0.17l-2.43,0.17c-0.32,0 -0.73,0.17 -0.81,-0.17 0,-0.25 0.57,-0.08 0.49,-0.41l-0.32,-0.83 -0.89,-2.23q-0.16,-0.5 -0.65,-0.58c-0.16,0 -0.49,0 -0.32,-0.33l0.81,0zM316.87,212.15q-0.08,-0.41 -0.57,-0.74 -0.41,0.08 -0.57,-0.33 0.41,-0.08 0.81,0c0.65,0 1.3,-0.25 1.95,0q1.05,0.25 1.3,1.49 0,0.5 -0.57,0.66c-0.24,0 0.16,0.08 0.24,0.17q0.81,0.41 1.14,1.24c0.08,0.17 0.41,0.58 0.49,0.17s0.49,0 0.41,0.25q-0.08,0.5 -0.57,0.5t-0.89,-0.5q-0.41,-0.83 -1.05,-1.4a0.83,0.81 90,0 0,-0.65 -0.17l0,0.41l0.49,0.99q0.32,0.33 0.81,0.25c0.24,0.08 0.16,0.5 -0.16,0.41l-1.38,0c-0.24,0.17 -0.73,0 -0.57,-0.25q0.49,0 0.41,-0.41l-0.81,-2.15zM318.01,212.98q0.32,0.08 0.73,0 0.32,-0.17 0.16,-0.58 -0.16,-0.66 -0.73,-0.99c-0.16,0 -0.65,-0.17 -0.73,0.08l0.57,1.32zM323.85,214.3q0.16,0.41 0.49,0.58 0.32,0 0.57,0.25 -0.08,0.25 -0.49,0.17l-1.3,0q-0.41,0.17 -0.49,-0.25c0.16,-0.17 0.57,0 0.57,-0.33l-0.41,-0.91 -0.89,-2.4q-0.24,-0.33 -0.57,-0.25 -0.49,-0.08 -0.57,0.33 0.16,0.41 0,0.66 -0.32,-0.08 -0.41,-0.41t-0.32,-0.83q0.16,-0.17 0.41,-0.08l3.41,-0.17q0.41,0 0.41,0.41l0.32,0.83q-0.24,0.25 -0.49,-0.25t-0.73,-0.58q-0.32,0 -0.65,0.17l0.16,0.58zM326.93,213.56q-0.16,0.08 -0.08,0.33l0,0.66q0.24,0.33 0.65,0.33c0.16,0 0.08,0.41 -0.16,0.33l-0.97,0c-0.24,0.08 -0.65,-0.08 -0.41,-0.33q0.24,0 0.41,-0.25l0,-4.13q0.32,-0.25 0.49,0.17l2.92,3.72q0.24,0.33 0.65,0.33c0.24,0 0.32,0.41 0,0.33l-1.22,0q-0.32,0.08 -0.57,0 -0.16,-0.25 0.16,-0.33c0.24,0 0,-0.33 -0.08,-0.5l-0.49,-0.66zM327.83,213.14q0.16,0 0,-0.17l-1.05,-1.4l0,1.65l1.05,0zM330.34,211.66c-0.08,-0.33 -0.24,-0.83 -0.65,-0.83q-0.49,0 -0.41,-0.33l1.38,0q0.81,-0.17 1.62,0.08 1.95,0.74 2.43,2.81 0,0.83 -0.49,1.32 -0.97,0.5 -2.03,0.25l-1.14,0.08c-0.24,0 -0.32,-0.41 0,-0.33q0.41,-0.17 0.16,-0.58zM331.96,213.72q0.08,0.41 0.41,0.83l0.81,0q0.57,-0.17 0.57,-0.74a4.13,4.06 90,0 0,-0.49 -1.74q-0.49,-0.83 -1.46,-1.16l-0.81,0q0,0 0,0.33zM314.85,223.97q0.08,0.41 0.65,0.5c0.16,0 0.57,0 0.57,0.33q-0.32,0.17 -0.65,0l-1.22,0.17l-0.73,0q-0.24,-0.17 0.08,-0.33 0.41,0 0.49,-0.33l-0.16,-0.66 -0.97,-2.31q-0.08,-0.33 -0.57,-0.25 -0.32,0.08 -0.57,-0.17c0,-0.25 0.41,-0.17 0.57,-0.33l0.81,-0.25q0.24,0 0.24,0.25zM315.41,220.25l1.62,0c0.32,0 0.16,-0.41 0.32,-0.41q0.41,0.17 0.32,0.58 0,0.58 -0.49,0.5L315.98,220.91l0.41,0.99 0.49,-0.17c0.81,0 1.78,0.5 2.11,1.32q0.32,0.66 0,1.24 -0.49,0.58 -1.3,0.5 -0.65,0 -1.14,-0.5c-0.08,-0.17 -0.08,-0.58 0.16,-0.5q0.57,0 0.57,0.5 0.24,0.17 0.57,0.08 0.41,0 0.49,-0.41 0,-0.66 -0.41,-1.16 -0.24,-0.5 -1.05,-0.58l-0.65,0.08q-0.32,-0.08 -0.32,-0.41l-0.57,-1.57 0.16,-0.08zM321.01,221.08q-0.16,-0.58 -0.73,-0.83c-0.16,0 -0.57,-0.08 -0.32,-0.33l1.3,0q0.89,-0.17 1.7,0.08 1.95,0.74 2.43,2.81 0.08,0.83 -0.57,1.32 -0.89,0.33 -1.95,0.25l-1.05,0.17c-0.24,0 -0.32,-0.41 0,-0.41q0.32,-0.08 0.16,-0.58zM322.63,223.14q0.16,0.41 0.49,0.83l0.81,0q0.49,-0.25 0.49,-0.83 0,-0.83 -0.57,-1.74t-1.46,-1.07l-0.81,0l0,0.33zM327.83,219.59q0.32,0.08 0.41,0.41t0.24,0.66q-0.32,0.25 -0.49,-0.25 -0.08,-0.33 -0.49,-0.41l-1.05,0q-0.08,0.33 0,0.5l0.49,1.16q0.41,0 0.65,-0.17c0.16,-0.17 -0.24,-0.58 0.16,-0.5q0.32,0.17 0.32,0.66l0.41,0.91c-0.08,0.33 -0.41,0 -0.49,-0.25q-0.24,-0.33 -0.65,-0.25 -0.32,0 -0.16,0.17l0.49,1.24q0.32,0.33 0.81,0.25 0.57,0 0.81,-0.25c0.16,-0.17 -0.16,-0.5 0,-0.66q0.49,0.08 0.41,0.41l0.24,0.83q-0.08,0 -0.32,0l-2.52,0.17q-0.41,0.17 -0.73,-0.08c-0.16,-0.33 0.41,-0.17 0.49,-0.41l-0.24,-0.74 -0.89,-2.4q-0.16,-0.41 -0.65,-0.58c-0.16,0 -0.49,0 -0.32,-0.33l0.81,0zM305.76,231.41q0.24,0.58 0.81,0.66 1.05,0.25 1.78,0.91 0.41,0.5 0.41,1.16 -0.08,0.66 -0.81,0.83 -0.65,0.25 -1.38,-0.08c-0.16,-0.08 -0.08,0.33 -0.41,0.25q-0.16,-0.33 -0.16,-0.74l-0.32,-0.83q0.16,-0.25 0.41,0.17 0.41,0.83 1.3,0.91 0.57,0 0.65,-0.5 -0.08,-0.66 -0.65,-0.99 -0.81,-0.25 -1.62,-0.66a1.65,1.62 90,0 1,-0.81 -1.32q0.08,-0.58 0.65,-0.74t1.3,0q0.16,-0.17 0.49,-0.08 0,0.25 0.16,0.5l0.24,0.74q-0.32,0.17 -0.41,-0.17 -0.32,-0.58 -0.81,-0.66 -0.49,0 -0.81,0.25zM311.11,230q0.41,0.08 0.41,0.41l0.16,0.66q-0.16,0.25 -0.41,-0.17 -0.08,-0.33 -0.57,-0.41l-0.97,0q-0.08,0.33 0,0.5l0.41,1.16q0.41,0 0.65,-0.17c0.16,-0.17 -0.16,-0.5 0.08,-0.58q0.41,0.17 0.41,0.58 0.08,0.5 0.32,1.07 -0.24,0.25 -0.41,-0.25 -0.16,-0.33 -0.65,-0.25 -0.32,0 -0.24,0.17l0.49,1.24q0.16,0.33 0.57,0.25 0.57,0 0.97,-0.25 0.08,-0.33 0,-0.66 0.41,-0.17 0.49,0.25l0.16,0.83q-0.08,0.25 -0.32,0.17l-2.43,0.17c-0.32,0 -0.65,0.25 -0.81,0 0,-0.33 0.41,-0.25 0.49,-0.5q0,-0.25 -0.16,-0.66l-0.81,-2.48q0,-0.41 -0.57,-0.5 -0.41,0 -0.32,-0.33l0.81,0zM314.52,233.47q0.16,0.41 0.49,0.58 0.49,-0.08 0.65,0.17c0,0.33 -0.41,0.17 -0.57,0.17q-0.65,0 -1.3,0.17 -0.32,0 -0.49,-0.17c0,-0.33 0.41,-0.17 0.49,-0.33l-0.08,-0.66 -0.81,-2.48q-0.16,-0.5 -0.57,-0.58 -0.41,0.08 -0.41,-0.33 0.16,-0.08 0.57,0 0.81,-0.17 1.78,-0.17a1.65,1.62 90,0 1,1.38 0.99q0.24,0.58 0,1.07 -0.65,0.41 -1.38,0.5 -0.16,0 0,0.25zM314.03,231.99l0.57,0q0.41,-0.17 0.32,-0.58 0,-0.58 -0.41,-0.99t-0.89,-0.17q-0.08,0.17 0,0.41zM319.06,233.23q0,0.33 0.41,0.5 0.41,0 0.57,0.25c0.16,0.25 -0.41,0.17 -0.57,0.17l-1.3,0.08q-0.32,0 -0.41,-0.25c0.16,-0.17 0.57,0 0.57,-0.33l-0.24,-0.99 -0.81,-2.4q-0.08,-0.25 -0.57,-0.17 -0.32,0 -0.57,0.25l0,0.66q-0.32,0.17 -0.32,-0.25l-0.32,-0.83q0.08,-0.25 0.41,-0.25l3.33,-0.17q0.41,0 0.41,0.41l0.32,0.83q-0.24,0.17 -0.41,-0.17 -0.24,-0.5 -0.81,-0.58 -0.24,0 -0.65,0l0.16,0.58zM320.77,230.5q0,-0.5 -0.49,-0.66 -0.32,0.08 -0.49,-0.33 0.16,-0.08 0.49,0l1.46,-0.17c0.24,-0.08 0.41,0.33 0.16,0.33s-0.49,0.17 -0.41,0.41l0.41,1.32 0.57,1.82q0.08,0.25 0.41,0.33 0.49,-0.08 0.49,0.33l-0.57,0l-1.3,0.17c-0.16,0 -0.49,-0.17 -0.32,-0.33q0.41,0 0.49,-0.33l-0.24,-0.99zM325.47,229.18q0.32,0.08 0.32,0.41l0.16,0.66q-0.24,0.25 -0.41,-0.17 -0.08,-0.41 -0.57,-0.5L324.1,229.59q-0.08,0.33 0,0.58l0.41,1.07q0.24,0 0.65,-0.17c0.16,-0.08 -0.16,-0.5 0.08,-0.5q0.41,0.08 0.41,0.5 0.08,0.58 0.32,1.07 -0.24,0.25 -0.41,-0.17t-0.65,-0.33c-0.08,0 -0.41,0 -0.24,0.17l0.41,1.24q0.24,0.33 0.57,0.33 0.57,0 0.97,-0.25 0.08,-0.33 0.08,-0.74 0.41,-0.08 0.41,0.33l0.16,0.83q0,0.25 -0.32,0.17l-2.43,0.08q-0.49,0.08 -0.81,0c-0.08,-0.33 0.41,-0.25 0.49,-0.41l-0.08,-0.66 -0.81,-2.48q0,-0.5 -0.57,-0.58c-0.16,0 -0.49,-0.17 -0.32,-0.33l0.81,0l2.35,-0.17zM327.1,230q-0.16,-0.5 -0.65,-0.58c-0.24,0.08 -0.41,-0.5 0,-0.33l1.22,0l0.57,0.74 1.78,2.4q-0.08,0 -0.08,-0.25l0.32,-2.98c0.16,-0.17 0.57,0 0.81,-0.17 0.24,0 0.81,0 0.65,0.33q-0.57,0 -0.49,0.5l0.97,2.98q0.08,0.5 0.65,0.41c0.24,0 0.24,0.5 0,0.41L331.4,233.47q-0.57,0.17 -0.57,-0.25c0.24,0 0.65,-0.17 0.49,-0.5l-0.81,-2.64q0,0.17 0,0.33l-0.16,2.48 -0.08,0.66q-0.32,0 -0.41,-0.25l-2.35,-3.06 0.16,0.25q0.32,1.24 0.73,2.4 0.16,0.41 0.65,0.41 0.32,0.33 -0.16,0.33l-1.22,0q-0.32,-0.17 0,-0.33 0.49,-0.17 0.16,-0.66zM334.16,230.66q0.16,0.08 0.41,0 0.41,0.08 0.57,-0.33 0,-0.58 -0.41,-0.99 -0.49,-0.25 -0.97,-0.17l0,0.41zM334.56,231.99 L334.89,232.81q0.41,0.17 0.73,0.08 0.32,0 0.32,-0.41 0,-0.83 -0.73,-1.24l-0.81,0l0,0.25zM333.02,230q0,-0.5 -0.49,-0.83 -0.41,0 -0.49,-0.25c-0.08,-0.25 0.41,0 0.57,-0.08q1.14,-0.08 2.19,0 0.97,0.33 1.05,1.32 -0.08,0.5 -0.57,0.74 0.16,-0.08 0.41,0 0.65,0.25 0.97,0.99 0.32,0.5 0,1.07 -0.57,0.41 -1.22,0.33l-1.87,0.08c-0.24,0 -0.32,-0.41 0,-0.33q0.41,-0.08 0.16,-0.58zM337.16,229.84q0,-0.5 -0.49,-0.83 -0.41,0 -0.49,-0.25c0.16,-0.25 0.49,0 0.81,-0.08l1.62,0q1.05,0.08 1.38,1.16 0.16,0.66 -0.32,0.99l-0.32,0.17q0.81,0.33 1.14,0.99l0.41,0.66q0.24,0.08 0.24,-0.25c0.32,-0.17 0.49,0.25 0.32,0.5q-0.32,0.5 -0.81,0.33a0.83,0.81 90,0 1,-0.65 -0.5l-0.81,-1.4q-0.41,-0.25 -0.73,-0.08l0,0.33l0.41,0.99q0.24,0.33 0.65,0.33c0.32,0 0.32,0.41 0,0.33l-1.46,0q-0.32,0.08 -0.57,0c-0.16,-0.41 0.32,-0.25 0.49,-0.41l-0.16,-0.74zM338.21,230.66c0.08,0.25 0.49,0 0.65,0q0.41,-0.08 0.32,-0.41 0,-0.66 -0.41,-1.07 -0.41,-0.25 -0.81,-0.08 -0.16,0.08 0,0.41l0.24,1.07zM343.81,228.52q0.32,0.08 0.32,0.41t0.24,0.66q-0.32,0.25 -0.49,-0.17 0,-0.41 -0.49,-0.5l-1.05,0q-0.16,0.17 0,0.5l0.32,1.16q0.41,0 0.65,-0.17c0.24,-0.08 -0.08,-0.41 0.16,-0.58q0.41,0.08 0.32,0.58 0.08,0.58 0.32,1.07 -0.24,0.25 -0.41,-0.17 -0.08,-0.41 -0.57,-0.33c-0.08,0 -0.41,-0.17 -0.32,0l0.41,1.4q0.16,0.33 0.57,0.25 0.57,0 0.97,-0.17 0.08,-0.33 0.08,-0.66 0.49,-0.17 0.41,0.25l0.16,0.83q-0.08,0.25 -0.32,0.17l-2.52,0q-0.41,0.08 -0.65,0c-0.24,-0.33 0.16,-0.25 0.32,-0.41s0,-0.41 0,-0.58l-0.73,-2.56q0,-0.5 -0.49,-0.58 -0.65,0 -0.41,-0.33l0.81,0zM317.2,239.34q0,-0.5 -0.49,-0.83 -0.49,0 -0.41,-0.25l0.81,-0.08q0.97,-0.08 1.95,0 1.62,0.41 2.11,1.9 0.32,0.99 -0.16,1.9 -0.73,0.74 -1.78,0.66 -0.81,0 -1.54,0.17c-0.16,0.08 -0.49,-0.08 -0.32,-0.33q0.49,0 0.49,-0.41l-0.32,-1.24zM318.5,241.41q0.08,0.41 0.32,0.74 0.41,0.17 0.81,0 0.57,0 0.73,-0.74a3.31,3.25 90,0 0,-0.32 -1.82,1.65 1.62,90 0,0 -1.22,-0.99l-0.89,0q0,0 0,0.33zM324.26,237.69q0.32,0.08 0.32,0.41l0.16,0.74q-0.32,0.17 -0.41,-0.17 -0.16,-0.41 -0.49,-0.5l-1.05,0q-0.16,0.17 0,0.5 0,0.66 0.24,1.16 0.41,0 0.65,-0.17c0.16,-0.17 -0.08,-0.5 0.16,-0.58q0.32,0.17 0.32,0.58l0.24,1.07c0,0.25 -0.41,0 -0.41,-0.17q-0.08,-0.41 -0.57,-0.33c-0.08,0 -0.41,-0.08 -0.24,0.08q0,0.58 0.32,1.32 0.08,0.33 0.57,0.25 0.57,0 0.97,-0.25 0.08,-0.33 0.16,-0.66 0.24,-0.17 0.32,0.33 0,0.33 0.16,0.83 -0.16,0.17 -0.41,0.08l-2.43,0.17q-0.41,0.17 -0.73,0 -0.16,-0.33 0.32,-0.33 0.32,-0.25 0.08,-0.58l-0.57,-2.64q-0.16,-0.5 -0.49,-0.58 -0.49,0 -0.41,-0.33l0.81,0zM329.53,241.08q0,0.41 0.49,0.58 0.32,0 0.57,0.17c0.08,0.25 -0.32,0.17 -0.49,0.17l-1.38,0.08q-0.41,0 -0.65,0 -0.32,-0.17 0.08,-0.33 0.41,0 0.57,-0.33l-0.08,-0.66 -0.57,-2.31q0,-0.33 -0.49,-0.25 -0.32,0.17 -0.57,-0.08c0,-0.33 0.41,-0.25 0.57,-0.33l0.89,-0.33q0.24,0 0.24,0.25zM332.53,238.35q0,-0.58 -0.57,-0.66 -0.49,0 -0.65,0.25 -0.16,0.5 0.32,0.83l0.73,0.41 0.16,-0.5zM333.18,240.75q-0.16,-0.58 -0.81,-0.83 -0.24,-0.08 -0.41,-0.08a0.83,0.81 90,0 0,-0.32 0.83q0.24,0.83 0.81,0.83 0.49,0.17 0.81,-0.33l0,-0.41zM333.99,240.58q0,0.66 -0.49,1.07 -0.65,0.41 -1.62,0.17 -0.73,-0.17 -0.97,-0.99 0,-0.66 0.41,-1.16c0.08,0 0.41,-0.08 0.16,-0.17q-0.73,-0.25 -0.81,-0.99 -0.24,-0.66 0.41,-1.07a1.65,1.62 90,0 1,2.11 0.58q0.24,0.58 0,0.99l-0.49,0.41q0.65,0.08 0.97,0.66 0.24,0.17 0.24,0.5zM336.59,241.08q0.32,0 0.41,-0.33c0.08,-0.17 0,-0.58 0.24,-0.5q0.24,0 0.24,0.5 0.08,0.5 -0.32,0.91 -0.24,0.17 -0.65,0.08l-1.38,0q-0.32,0.17 -0.57,0 -0.08,-0.25 0,-0.58 0.32,-0.83 0.81,-1.32 0.57,-0.33 0.65,-0.91 0,-0.5 -0.16,-0.99a0.83,0.81 90,0 0,-1.14 -0.25q-0.41,0.25 -0.24,0.58c0.16,0.17 0.49,0 0.57,0.33q0.16,0.41 -0.24,0.5t-0.81,-0.58q-0.16,-0.74 0.49,-1.16t1.38,-0.17 0.97,0.99q0.16,0.5 -0.08,1.07 -0.65,0.74 -1.38,1.4 -0.16,0.17 -0.08,0.5 0.16,-0.25 0.41,-0.17zM339.75,240.75q0,0.33 0.49,0.5 0.32,0 0.57,0.17c0.16,0.25 -0.24,0.25 -0.41,0.25l-2.19,0q-0.16,-0.33 0.16,-0.33t0.57,-0.33l0,-0.66l-0.57,-2.31q-0.08,-0.33 -0.41,-0.25t-0.57,0c-0.16,-0.33 0.24,-0.33 0.41,-0.41l0.97,-0.33q0.32,0 0.32,0.25z"
      android:strokeWidth="0.16"
      android:fillColor="#b07e09"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M316.63,173.31 L313.55,177.11c-0.97,4.3 1.46,7.6 6.25,10.41 3.81,2.48 10.95,3.06 13.22,1.07l-11.12,-10.74z"
      android:strokeWidth="0.16"
      android:strokeColor="#4c0505">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:centerX="318.33"
          android:centerY="169.92"
          android:gradientRadius="0.24"
          android:type="radial">
        <item android:offset="0.3" android:color="#FFA50A0A"/>
        <item android:offset="1" android:color="#FF4C0505"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="m339.19,284.79 l-1.62,-2.07a83.47,81.94 90,0 1,-26.53 23.64c12.25,-3.47 20.28,-12.56 28.15,-21.57zM326.61,314.3a376.84,369.97 90,0 1,14.77 -26.69l-1.7,-2.23c-6,10.08 -12.98,22.06 -12.98,28.92zM360.52,219.67a51.24,50.3 90,0 0,-3.49 -10.74,104.95 103.04,90 0,0 -35.54,-36.36l0,-0.25l0.32,0l-0.08,-0.08l0.32,0l-0.16,-0.25l0.32,0l-0.24,-0.33l0.32,0l-0.24,-0.25l0.41,0l-0.24,-0.33l0.32,0l-0.32,-0.25 0.49,-0.08 -0.41,-0.25 0.41,-0.08 -0.32,-0.25 0.49,-0.17 -0.41,-0.25 0.49,-0.25 -0.49,-0.33 0.57,-0.17 -0.65,-0.25 0.57,-0.33l-0.65,0l0.41,-0.58l-0.57,0l0.24,-0.41l-0.49,0l0.24,-0.41 -0.57,0.08 0.24,-0.5 -0.49,0.33 0.16,-0.58 -0.57,0.33 0.08,-0.58 -0.49,0.41 0.08,-0.58 -0.41,0.41l0,-0.58l-0.41,0.5l0,-0.58l-0.32,0.41 -0.16,-0.5 -0.32,0.5 -0.08,-0.58 -0.24,0.58 -0.16,-0.58 -0.24,0.58 -0.16,-0.5 -0.16,0.5 -0.16,-0.5 -0.16,0.66 -0.16,-0.58 -0.24,0.66 -0.24,-0.58l0,0.66l-0.32,-0.58l0,0.66l-0.41,-0.5l0,0.66l-0.32,-0.5l0,0.58l-0.32,-0.25 0.16,0.58 -0.41,-0.25 0.16,0.5 -0.32,-0.17 0.08,0.5l-0.32,0l0.16,0.33l-0.24,0l0.16,0.33a7.44,7.3 90,0 0,-0.32 2.89c0,0.17 0.49,0.33 0.57,0.5q0.24,0.5 0.16,0.5c-1.3,2.15 -2.43,3.8 -2.52,6.28 0.97,-1.32 2.11,-2.98 3.57,-2.98 -0.73,1.24 -1.05,5.04 -0.24,5.62l0.65,-1.07q0,1.24 0.32,1.9 0.32,-0.83 0.81,-1.4 0,1.9 0.49,2.73 0.57,-0.91 1.3,-1.32 -0.32,1.07 -0.24,2.23 0.73,-1.16 1.54,-2.07c-0.32,1.32 0,2.31 0.32,3.72 0.32,-1.65 0.49,-1.9 1.22,-2.56 0.08,1.57 -0.16,2.98 0.16,3.88 0.65,-1.4 1.14,-1.24 1.62,-2.15 -0.16,1.32 -0.41,2.73 0,3.88a4.13,4.06 90,0 1,1.54 -2.15c0,1.82 0.41,1.49 -0.65,3.06 0.49,0.17 2.03,-0.33 2.92,-0.74 -0.41,0.99 -0.32,1.74 -0.73,2.48q1.22,-0.83 2.35,-1.98c-0.32,1.16 -1.22,2.31 -0.81,3.39q0.49,-1.65 1.87,-2.07c-0.16,0.58 -0.24,1.82 -1.05,2.4 1.95,0.17 2.76,-1.16 3.73,-2.23 -0.08,1.07 0,1.98 1.3,3.55 -0.24,-1.98 0.16,-1.57 1.05,-2.73 0.49,1.74 0.97,3.64 2.76,4.96 -0.81,-1.9 -0.81,-2.89 -0.49,-4.05 0.24,1.24 1.38,2.73 1.62,3.8q0.24,-1.98 0.81,-3.31a16.53,16.23 90,0 1,1.14 4.63q0.65,-1.24 0.89,-2.48c16.06,16.94 19.63,38.59 2.03,61.48 6.33,-4.3 7.95,-11.16 11.44,-17.02 -3.41,7.69 -4.87,16.28 -10.95,22.64 5.84,-5.12 7.06,-9.83 10.55,-14.79 -3.65,10.33 -8.92,21.65 -16.06,32.06l3.89,4.3 3.25,-5.7c7.63,-12.73 20.04,-30.99 13.79,-58.76z"
      android:strokeWidth="0.16"
      android:fillColor="#448127"
      android:strokeColor="#34541f"/>
  <path
      android:pathData="M314.52,169.92c-0.81,0.17 -1.14,0.5 -1.78,1.16q1.14,0.25 2.11,0.25 0.24,-0.17 0.24,-0.5l-0.16,-0.83c0,-0.17 -0.49,-0.08 -0.65,0z"
      android:strokeWidth="0.16"
      android:fillColor="#eac102"
      android:strokeColor="#a08307"/>
  <path
      android:pathData="m312.74,171.08 l1.78,-0.41z"
      android:fillColor="#a08307"/>
  <path
      android:fillColor="#FF000000"
      android:pathData="M318.17,170.01m-0.65,0a0.66,0.65 90,1 1,1.3 0a0.66,0.65 90,1 1,-1.3 0"
      android:strokeWidth="0.08"
      android:strokeColor="#000"/>
  <path
      android:pathData="M318.09,169.92a0.33,0.24 90,1 0,0.49 0a0.33,0.24 90,1 0,-0.49 0z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:centerX="318.32"
          android:centerY="169.87"
          android:gradientRadius="0.24"
          android:type="radial">
        <item android:offset="0" android:color="#FFFFFFFF"/>
        <item android:offset="1" android:color="#00FFFFFF"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M326.69,314.05a68.59,67.34 90,0 1,6 -14.21,165.28 162.27,90 0,1 5.76,-10.08l2.11,-3.31 -2.03,3.31 -5.68,10.16a108.72,110.74 0,0 0,-6.17 14.05zM311.36,306.2a62.81,61.66 90,0 0,24.1 -19.09,127.27 124.94,90 0,0 -9.09,9.75 57.85,56.79 90,0 1,-15.01 9.34m30.26,-24.21q0.97,-1.32 1.78,-2.73a112.39,110.34 90,0 0,4.87 -8.43q1.62,-2.89 3.08,-5.78l3,-5.78 -1.38,2.89 -1.46,2.98a211.56,207.7 90,0 1,-6.25 11.4zM346.41,261.24a47.93,47.06 90,0 0,6.9 -11.07,60.33 59.23,90 0,0 4.87,-19.01l-0.08,3.31a48.76,47.87 90,0 1,-4.71 15.7,49.58 48.68,90 0,1 -6.98,11.07"
      android:fillColor="#34541f"/>
  <path
      android:pathData="M345.11,256.45a42.97,42.19 90,0 0,7.06 -10.33A79.33,77.89 90,0 0,355.74 237.44l0.24,-0.74 0.24,-0.74 0.49,-1.49a34.71,34.08 90,0 0,0.16 -12.73,47.93 47.06,90 0,0 0.57,6.36q0,3.31 -0.57,6.36 -0.32,0.83 -0.49,1.57l-0.24,0.74 -0.32,0.74 -3.57,8.76a42.97,42.19 90,0 1,-3.08 5.45,29.75 29.21,90 0,1 -4.06,4.79z"
      android:fillColor="#34541f"/>
  <path
      android:pathData="M340.32,280.58q1.54,-1.98 2.92,-4.13l1.46,-2.07 1.38,-2.07 2.43,-4.46 2.11,-4.55q2.19,-4.55 4.06,-9.34a65.29,64.1 90,0 0,4.3 -19.67,47.93 47.06,90 0,0 -2.11,-20 42.97,42.19 90,0 1,2.27 20,66.11 64.91,90 0,1 -4.3,19.75q-1.87,4.71 -4.06,9.26l-2.27,4.55q-1.05,2.31 -2.35,4.46 -0.65,1.16 -1.38,2.15l-1.46,2.07 -2.92,4.13z"
      android:fillColor="#34541f"/>
  <path
      android:pathData="M342.59,283.14a359.42,366.1 0,0 0,10.87 -19.42,67.76 66.53,90 0,0 7.06,-32.39q0,-5.62 -0.97,-11.07l0.08,0q1.05,5.54 0.97,11.07a85.12,83.57 90,0 1,-2.92 22.06,67.76 66.53,90 0,1 -4.06,10.41 366.1,359.42 90,0 1,-10.95 19.34zM320.04,174.47l1.14,0a11.57,11.36 90,0 1,3.33 1.07,29.75 29.21,90 0,1 7.3,5.62 31.4,30.83 90,0 0,-7.3 -5.45,15.7 15.42,90 0,0 -4.46,-1.24"
      android:fillColor="#34541f"/>
  <path
      android:pathData="M333.34,183.39s2.43,1.49 3.81,3.64c0,0 -3.81,-0.74 -6.49,-3.8"
      android:fillColor="#448127"/>
  <path
      android:pathData="M333.34,183.39a11.57,11.36 90,0 1,3.89 3.64l0.08,0.17l-0.16,0a12.4,12.17 90,0 1,-3.57 -1.49q-1.62,-0.99 -3,-2.48a12.4,12.17 90,0 0,4.79 3.14l1.78,0.58l0,0.17a9.09,8.92 90,0 0,-1.78 -2.07z"
      android:fillColor="#34541f"/>
  <path
      android:pathData="M327.83,182.65s5.84,3.8 6.73,4.13c0,0 -1.38,-2.89 -4.14,-4.13"
      android:fillColor="#448127"/>
  <path
      android:pathData="m327.83,182.65 l3.33,2.07 2.52,1.49 0.81,0.5l0,0.08a9.09,8.92 90,0 0,-1.7 -2.4,9.09 8.92,90 0,0 -2.35,-1.74 6.61,6.49 90,0 1,2.43 1.65,9.09 8.92,90 0,1 1.7,2.48l0.16,0.17 -0.24,-0.08 -1.7,-0.99z"
      android:fillColor="#34541f"/>
  <path
      android:pathData="M330.18,180.75s3,1.32 3.73,3.55c0,0 -5.52,-2.31 -6.49,-3.55"
      android:fillColor="#448127"/>
  <path
      android:pathData="M330.18,180.75a9.09,8.92 90,0 1,2.27 1.32l0.89,0.99 0.65,1.24l0,0.17l-0.16,0a63.63,62.47 90,0 1,-5.68 -3.06l-0.73,-0.66 0.73,0.58 0.81,0.5a29.75,29.21 90,0 0,4.95 2.48l-0.08,0q-0.16,-0.58 -0.65,-1.07l-0.81,-0.99a9.09,8.92 90,0 0,-2.19 -1.49"
      android:fillColor="#34541f"/>
  <path
      android:pathData="M325.56,183.15s5.35,3.88 6.98,3.88c0,0 -1.62,-2.73 -4.38,-3.88"
      android:fillColor="#448127"/>
  <path
      android:pathData="m325.56,183.15 l6,3.55 0.97,0.25 -0.08,0.17a9.92,9.74 90,0 0,-1.87 -2.31,10.74 10.55,90 0,0 -2.43,-1.65 9.09,8.92 90,0 1,3.57 2.64l0.81,1.24 0.16,0.08l-0.16,0q-0.57,0 -1.05,-0.25l-0.81,-0.41a27.27,26.77 90,0 1,-5.11 -3.31"
      android:fillColor="#34541f"/>
  <path
      android:pathData="M325.56,184.3s2.76,2.48 4.38,2.48c0,0 -1.62,-2.73 -4.38,-3.97"
      android:fillColor="#448127"/>
  <path
      android:pathData="m325.56,184.3 l3.25,2.07 0.49,0.17 0.65,0.17 -0.08,0.08a9.92,9.74 90,0 0,-1.87 -2.23,10.74 10.55,90 0,0 -2.43,-1.65 9.09,8.92 90,0 1,3.57 2.56l0.81,1.24 0.16,0.17l-0.16,0l-1.3,-0.33a9.92,9.74 90,0 1,-2.11 -1.4z"
      android:fillColor="#34541f"/>
  <path
      android:pathData="M324.1,178.93s5.84,4.21 6.49,6.53c0,0 -5.19,-2.48 -6.17,-3.72"
      android:fillColor="#448127"/>
  <path
      android:pathData="M324.1,178.93a42.97,42.19 90,0 1,6.08 5.45l0.57,0.99l0,0.25l-0.16,-0.08 -5.52,-3.22 -0.65,-0.58 0.65,0.58 0.81,0.5a30.02,30.58 0,0 0,4.87 2.48l-0.16,0.17 -0.57,-0.99a23.97,23.53 90,0 0,-4.06 -4.13z"
      android:fillColor="#34541f"/>
  <path
      android:pathData="M291.64,298.02a34.71,34.08 90,0 1,-16.55 3.97c8.11,-6.78 15.98,-6.94 16.55,-3.97zM348.27,301.73c4.22,3.88 17.04,6.61 19.07,6.12 -8.6,-8.84 -17.93,-9.75 -19.07,-6.12z"
      android:strokeWidth="0.16"
      android:fillColor="#406325"
      android:strokeColor="#24420e"/>
  <path
      android:pathData="M291.64,298.02c-0.57,-3.06 -8.52,-2.81 -16.55,3.97a57.85,56.79 90,0 1,16.55 -3.97zM348.35,301.65c1.14,-3.64 10.39,-2.73 18.99,6.2a69.42,68.15 90,0 0,-18.99 -6.2z"
      android:strokeWidth="0.16"
      android:fillColor="#67923d"
      android:strokeColor="#24420e"/>
</vector>
