<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <import type="com.eatapp.clementine.R" />

        <variable
            name="viewmodel"
            type="com.eatapp.clementine.ui.omnisearch.OmniSearchViewModel" />

    </data>

    <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
        android:id="@+id/container"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <com.google.android.material.appbar.AppBarLayout
            android:id="@+id/app_bar_layout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <androidx.appcompat.widget.Toolbar
                android:id="@+id/toolbar"
                android:layout_width="match_parent"
                android:layout_height="?attr/actionBarSize"
                android:background="@color/grey800"
                android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:popupTheme="@style/ThemeOverlay.AppCompat.Light">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent">

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent">

                        <TextView
                            android:id="@+id/tv_title"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/margin_8"
                            android:fontFamily="@font/inter_medium"
                            android:text="@string/search_label"
                            android:textColor="@color/white"
                            android:textSize="@dimen/text_size_16" />
                    </LinearLayout>

                </androidx.constraintlayout.widget.ConstraintLayout>

            </androidx.appcompat.widget.Toolbar>

        </com.google.android.material.appbar.AppBarLayout>

        <ProgressBar
            android:id="@+id/progress"
            style="?android:attr/progressBarStyle"
            android:layout_width="@dimen/progress_size"
            android:layout_height="@dimen/progress_size"
            android:layout_marginTop="10dp"
            android:indeterminateTint="@color/green500"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/chip_group_choice"
            app:visible_or_gone="@{viewmodel.searchInProgress}" />

        <EditText
            android:id="@+id/edit_search"
            android:layout_width="0dp"
            android:layout_height="40dp"
            android:layout_marginStart="@dimen/margin_16"
            android:layout_marginTop="@dimen/margin_16"
            android:layout_marginEnd="@dimen/margin_16"
            android:background="@drawable/shape_rounded_btn_bcg_grey_outline"
            android:drawableStart="@drawable/ic_icon_search_grey600"
            android:drawablePadding="@dimen/margin_8"
            android:fontFamily="@font/inter_regular"
            android:hint="@string/omnisearch_hint"
            android:paddingStart="@dimen/margin_8"
            android:paddingEnd="@dimen/margin_8"
            android:textColor="@color/grey800"
            android:textColorHint="@color/grey500"
            android:textSize="@dimen/text_size_14"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/app_bar_layout"
            app:tint="@color/grey600" />

        <com.google.android.material.chip.ChipGroup
            android:id="@+id/chip_group_choice"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/margin_16"
            android:layout_marginTop="@dimen/margin_8"
            android:layout_marginEnd="@dimen/margin_16"
            app:checkedChip="@+id/chip_all"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/edit_search"
            app:selectionRequired="true"
            app:singleSelection="true">

            <com.google.android.material.chip.Chip
                android:id="@+id/chip_all"
                style="@style/OmniSearchChipStyle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:fontFamily="@font/inter_regular"
                android:text="@string/chip_all_label"
                android:textSize="@dimen/text_size_12"
                android:theme="@style/Theme.MaterialComponents.Light" />

            <com.google.android.material.chip.Chip
                android:id="@+id/chip_guests"
                style="@style/OmniSearchChipStyle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:fontFamily="@font/inter_regular"
                android:text="@string/chip_guests_label"
                android:textSize="@dimen/text_size_12"
                android:theme="@style/Theme.MaterialComponents.Light" />

            <com.google.android.material.chip.Chip
                android:id="@+id/chip_reservation"
                style="@style/OmniSearchChipStyle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:fontFamily="@font/inter_regular"
                android:text="@string/chip_reservations_label"
                android:textSize="@dimen/text_size_12"
                android:theme="@style/Theme.MaterialComponents.Light" />
        </com.google.android.material.chip.ChipGroup>

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recycler_results"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_marginTop="@dimen/margin_8"
            android:layout_marginBottom="@dimen/margin_16"
            android:visibility="gone"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/progress" />

        <include
            android:id="@+id/omnisearch_empty_state"
            layout="@layout/generic_empty_state"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_marginTop="@dimen/margin_8"
            android:clickable="true"
            android:focusable="true"
            app:imageId="@{R.drawable.icon_omni_search_empty_state}"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/progress"
            app:subtitle="@{@string/omnisearch_empty_state_subtitle}"
            app:title="@{@string/omnisearch_empty_state_title}"
            tools:visibility="gone" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>