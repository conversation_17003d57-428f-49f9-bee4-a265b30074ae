<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="viewmodel"
            type="com.eatapp.clementine.ui.home.overview.OverviewViewModel" />

    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/container"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/white"
        tools:context=".ui.home.overview.OverviewFragment">

        <com.google.android.material.appbar.AppBarLayout
            android:id="@+id/appBarLayout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <androidx.appcompat.widget.Toolbar
                android:id="@+id/toolbar"
                android:layout_width="match_parent"
                android:layout_height="?attr/actionBarSize"
                android:background="@color/grey800"
                android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar"
                app:popupTheme="@style/ThemeOverlay.AppCompat.Light">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="horizontal">

                    <ImageView
                        android:id="@+id/iv_omnisearch"
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        android:src="@drawable/ic_icon_search_grey600"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        app:tint="@color/white" />

                    <LinearLayout
                        android:id="@+id/totalCoversCont"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:visibility="gone"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintStart_toEndOf="@id/iv_omnisearch"
                        android:layout_marginStart="@dimen/margin_12"
                        app:layout_constraintTop_toTopOf="parent">

                        <ImageView
                            android:id="@+id/imageView17"
                            android:layout_width="@dimen/global_icon_size_24"
                            android:layout_height="@dimen/global_icon_size_24"
                            app:srcCompat="@drawable/ic_icon_people"
                            app:tint="@color/white" />

                        <TextView
                            android:id="@+id/totalCovers"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="4dp"
                            android:fontFamily="@font/inter_medium"
                            android:textColor="@color/white"
                            android:textSize="16sp"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintStart_toEndOf="parent"
                            app:layout_constraintTop_toTopOf="parent"
                            tools:text="123" />

                    </LinearLayout>

                    <ImageButton
                        android:id="@+id/imageButton"
                        android:layout_width="@dimen/toolbar_icon_size"
                        android:layout_height="@dimen/toolbar_icon_size"
                        android:layout_marginTop="1dp"
                        android:layout_marginEnd="4dp"
                        android:background="?attr/selectableItemBackgroundBorderless"
                        android:onClick="@{() -> viewmodel.minusDay()}"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toStartOf="@+id/date"
                        app:layout_constraintTop_toTopOf="parent"
                        app:srcCompat="@drawable/ic_icon_arrow_left"
                        app:tint="@color/white"
                        tools:ignore="SpeakableTextPresentCheck,TouchTargetSizeCheck" />

                    <TextView
                        android:id="@+id/date"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="72dp"
                        android:fontFamily="@font/inter_medium"
                        android:textColor="@color/white"
                        android:textSize="17sp"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        tools:text="Thu, 13 Jul" />

                    <ImageButton
                        android:id="@+id/imageButton2"
                        android:layout_width="@dimen/toolbar_icon_size"
                        android:layout_height="@dimen/toolbar_icon_size"
                        android:layout_marginStart="4dp"
                        android:layout_marginTop="1dp"
                        android:background="?attr/selectableItemBackgroundBorderless"
                        android:onClick="@{() -> viewmodel.plusDay()}"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintStart_toEndOf="@+id/date"
                        app:layout_constraintTop_toTopOf="parent"
                        app:srcCompat="@drawable/ic_icon_arrow_right"
                        app:tint="@color/white"
                        tools:ignore="TouchTargetSizeCheck,SpeakableTextPresentCheck" />

                    <ProgressBar
                        android:id="@+id/progressMain"
                        style="?android:attr/progressBarStyle"
                        android:layout_width="20dp"
                        android:layout_height="20dp"
                        android:indeterminateTint="@color/white"
                        android:visibility="visible"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintStart_toEndOf="@id/iv_omnisearch"
                        android:layout_marginStart="@dimen/margin_8"
                        app:layout_constraintTop_toTopOf="parent" />

                </androidx.constraintlayout.widget.ConstraintLayout>

            </androidx.appcompat.widget.Toolbar>

        </com.google.android.material.appbar.AppBarLayout>

        <com.google.android.material.tabs.TabLayout
            android:id="@+id/tabLayout"
            android:layout_width="0dp"
            android:layout_height="@dimen/tab_height"
            app:layout_constraintWidth_min="330dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/appBarLayout"
            app:layout_constraintWidth_percent="0.32"
            app:tabGravity="fill"
            app:tabIndicatorColor="@color/colorPrimary"
            app:tabPaddingEnd="4dp"
            app:tabPaddingStart="4dp"
            android:paddingStart="4dp"
            android:paddingEnd="4dp"
            app:tabRippleColor="@color/colorGreen05"
            app:tabSelectedTextColor="@color/colorPrimary"
            app:tabTextAppearance="@style/EatTab"
            app:tabTextColor="@color/colorDark50"
            tools:ignore="SpeakableTextPresentCheck" />

        <View
            android:id="@+id/view"
            android:layout_width="0dp"
            android:layout_height="1dp"
            android:background="@color/colorSeparator"
            app:layout_constraintBottom_toTopOf="@+id/viewPager"
            app:layout_constraintEnd_toEndOf="@id/tabLayout"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tabLayout" />

        <androidx.viewpager2.widget.ViewPager2
            android:id="@+id/viewPager"
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="@id/tabLayout"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/view"
            tools:ignore="SpeakableTextPresentCheck" />

        <View
            android:id="@+id/view2"
            android:layout_width="1dp"
            android:layout_height="0dp"
            android:background="@color/colorSeparator"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toEndOf="@id/viewPager"
            app:layout_constraintTop_toTopOf="@id/tabLayout" />

        <androidx.fragment.app.FragmentContainerView
            android:id="@+id/floor_fragment_container"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:name="com.eatapp.clementine.ui.home.floor.FloorFragment"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/view2"
            app:layout_constraintTop_toBottomOf="@+id/appBarLayout">

        </androidx.fragment.app.FragmentContainerView>

    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>