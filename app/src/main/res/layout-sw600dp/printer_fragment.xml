<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:bind="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="viewmodel"
            type="com.eatapp.clementine.ui.common.printer.PrinterViewModel" />

        <import type="android.view.View" />

        <import type="com.eatapp.clementine.R" />

        <import type="android.text.TextUtils" />

    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/container"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/white">

        <com.google.android.material.appbar.AppBarLayout
            android:id="@+id/app_bar_layout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <androidx.appcompat.widget.Toolbar
                android:id="@+id/toolbar"
                android:layout_width="match_parent"
                android:layout_height="?attr/actionBarSize"
                android:background="@color/grey800"
                android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:popupTheme="@style/ThemeOverlay.AppCompat.Light">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent">

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent">

                        <TextView
                            android:id="@+id/tv_title"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/margin_8"
                            android:fontFamily="@font/inter_medium"
                            android:text="@string/printer_settings"
                            android:textColor="@color/white"
                            android:textSize="@dimen/text_size_16" />
                    </LinearLayout>

                </androidx.constraintlayout.widget.ConstraintLayout>

            </androidx.appcompat.widget.Toolbar>

        </com.google.android.material.appbar.AppBarLayout>

        <ScrollView
            android:layout_width="match_parent"
            android:layout_height="0dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/app_bar_layout">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/constraintLayout"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:visibility="@{viewmodel.printMode ? View.GONE : View.VISIBLE}"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:visibility="visible">

                    <TextView
                        android:id="@+id/textView13"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_margin="@dimen/global_margin_16"
                        android:background="@android:color/transparent"
                        android:ellipsize="end"
                        android:ems="10"
                        android:fontFamily="@font/inter_regular"
                        android:text="@string/printer_desc_1"
                        android:textColor="@color/colorDark100"
                        android:textSize="@dimen/input_value_size"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <include
                        android:id="@+id/printer_name"
                        layout="@layout/input_select"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/global_margin_16"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/textView13"
                        bind:disabled="@{true}"
                        bind:key="@{@string/chit_printer}"
                        bind:showIcon="@{false}"
                        bind:value="@{TextUtils.isEmpty(viewmodel.printerName) ? @string/no_printers_connected : viewmodel.printerName}" />

                    <View
                        android:id="@+id/view13"
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:background="@color/colorSeparator"
                        app:layout_constraintBottom_toTopOf="@+id/printer_name"
                        bind:layout_constraintBottom_toBottomOf="parent"
                        bind:layout_constraintEnd_toEndOf="parent"
                        bind:layout_constraintStart_toStartOf="parent" />

                    <ProgressBar
                        android:id="@+id/progressBar3"
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:layout_gravity="center"
                        android:layout_marginEnd="@dimen/global_margin_16"
                        android:visibility="@{viewmodel.connecting ? View.VISIBLE : View.GONE}"
                        app:layout_constraintBottom_toBottomOf="@+id/printer_name"
                        app:layout_constraintEnd_toEndOf="@+id/printer_name"
                        app:layout_constraintTop_toBottomOf="@+id/view13" />

                    <TextView
                        android:id="@+id/textView18"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_margin="@dimen/global_margin_16"
                        android:background="@android:color/transparent"
                        android:ellipsize="end"
                        android:ems="10"
                        android:fontFamily="@font/inter_regular"
                        android:text="@string/printer_desc_3"
                        android:textColor="@color/colorDark100"
                        android:textSize="@dimen/input_value_size"
                        bind:layout_constraintEnd_toEndOf="parent"
                        bind:layout_constraintStart_toStartOf="parent"
                        bind:layout_constraintTop_toBottomOf="@+id/textView17" />

                    <TextView
                        android:id="@+id/textView17"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_margin="@dimen/global_margin_16"
                        android:background="@android:color/transparent"
                        android:ellipsize="end"
                        android:ems="10"
                        android:fontFamily="@font/inter_regular"
                        android:text="@string/printer_desc_2"
                        android:textColor="@color/colorDark100"
                        android:textSize="@dimen/input_value_size"
                        app:layout_constraintTop_toBottomOf="@+id/printer_name"
                        bind:layout_constraintEnd_toEndOf="parent"
                        bind:layout_constraintStart_toStartOf="parent" />

                    <com.eatapp.clementine.views.LoadingButton
                        android:id="@+id/connectBtn"
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/button_height"
                        android:layout_marginStart="16dp"
                        android:layout_marginTop="24dp"
                        android:layout_marginEnd="16dp"
                        android:background="@drawable/shape_rounded_btn_bcg_green"
                        android:onClick="@{() -> viewmodel.connectPrinter()}"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/textView18"
                        app:progressBarColor="@color/white"
                        app:title="@string/chit_printer_connect"
                        app:tintColor="@{R.color.white}" />

                    <com.eatapp.clementine.views.LoadingButton
                        android:id="@+id/testPrint"
                        android:layout_width="0dp"
                        android:layout_height="@dimen/button_height"
                        android:layout_marginStart="16dp"
                        android:layout_marginTop="16dp"
                        android:layout_marginEnd="16dp"
                        android:layout_marginBottom="12dp"
                        android:background="@drawable/shape_rounded_btn_bcg_green"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/connectBtn"
                        app:title="@string/test_print"
                        app:tintColor="@{R.color.white}"
                        bind:layout_constraintStart_toStartOf="parent" />

                </androidx.constraintlayout.widget.ConstraintLayout>

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:paddingBottom="36dp"
                    app:layout_constraintTop_toBottomOf="@+id/constraintLayout">

                    <TextView
                        android:id="@+id/textView203"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="16dp"
                        android:layout_marginTop="16dp"
                        android:layout_marginEnd="16dp"
                        android:fontFamily="@font/inter_semibold"
                        android:text="@string/chit_print_config"
                        android:textColor="@color/colorDark100"
                        android:textSize="14sp"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/chitPrintConfigList"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp"
                        android:nestedScrollingEnabled="false"
                        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintHorizontal_bias="0.0"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/textView203">

                    </androidx.recyclerview.widget.RecyclerView>

                    <LinearLayout
                        android:id="@+id/linearLayout9"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="24dp"
                        android:layout_marginTop="12dp"
                        android:layout_marginEnd="24dp"
                        android:background="@drawable/shape_rounded_bcg_grey"
                        android:orientation="vertical"
                        android:paddingBottom="12dp"
                        android:visibility="@{viewmodel.posActive &amp; viewmodel.reviewsActive ? View.GONE : View.VISIBLE}"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/chitPrintConfigList">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_marginStart="24dp"
                            android:layout_marginTop="12dp"
                            android:layout_marginEnd="24dp"
                            android:gravity="center_vertical"
                            android:orientation="horizontal"
                            android:visibility="@{viewmodel.posActive ? View.GONE : View.VISIBLE}">

                            <ImageView
                                android:layout_width="@dimen/global_icon_size_16"
                                android:layout_height="@dimen/global_icon_size_16"
                                app:srcCompat="@drawable/ic_icon_addon"
                                app:tint="@color/colorDark50" />

                            <TextView
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="@dimen/margin_8"
                                android:background="@android:color/transparent"
                                android:fontFamily="@font/inter_regular"
                                android:text="@string/purchase_addon"
                                android:textColor="@color/colorDark100"
                                android:textSize="12sp" />

                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="24dp"
                            android:layout_marginTop="12dp"
                            android:layout_marginEnd="24dp"
                            android:gravity="center_vertical"
                            android:orientation="horizontal"
                            android:visibility="@{viewmodel.reviewsActive ? View.GONE : View.VISIBLE}">

                            <ImageView
                                android:layout_width="@dimen/global_icon_size_16"
                                android:layout_height="@dimen/global_icon_size_16"
                                app:srcCompat="@drawable/ic_icon_switch"
                                app:tint="@color/colorDark50" />

                            <TextView
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="@dimen/margin_8"
                                android:background="@android:color/transparent"
                                android:fontFamily="@font/inter_regular"
                                android:text="@string/enable_features"
                                android:textColor="@color/colorDark100"
                                android:textSize="12sp" />
                        </LinearLayout>
                    </LinearLayout>

                    <androidx.appcompat.widget.SwitchCompat
                        android:id="@+id/askMeSwitch"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="14dp"
                        android:layout_marginTop="12dp"
                        android:checked="@{viewmodel.askMeEveryTime}"
                        android:fontFamily="@font/inter_regular"
                        android:onCheckedChanged="@{(switch, checked) -> viewmodel.onAskMeCheckedChanged(checked)}"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/linearLayout9"
                        app:switchPadding="6dp" />

                    <TextView
                        android:id="@+id/textView36"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="6dp"
                        android:layout_marginEnd="16dp"
                        android:fontFamily="@font/inter_regular"
                        android:text="@string/ask_me_every_time"
                        android:textColor="@color/colorDark100"
                        android:textSize="14sp"
                        app:layout_constraintBottom_toBottomOf="@+id/askMeSwitch"
                        app:layout_constraintStart_toEndOf="@+id/askMeSwitch"
                        app:layout_constraintTop_toTopOf="@+id/askMeSwitch"
                        bind:layout_constraintEnd_toEndOf="parent" />

                    <TextView
                        android:id="@+id/textView37"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="16dp"
                        android:fontFamily="@font/inter_regular"
                        android:text="@string/ask_me_info"
                        android:textColor="@color/colorGrey200"
                        android:textSize="12sp"
                        app:layout_constraintStart_toStartOf="@+id/textView36"
                        bind:layout_constraintEnd_toEndOf="parent"
                        bind:layout_constraintTop_toBottomOf="@+id/askMeSwitch" />

                    <androidx.appcompat.widget.SwitchCompat
                        android:id="@+id/printWhenSeated"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="14dp"
                        android:layout_marginTop="16dp"
                        android:checked="@{viewmodel.printWhenSeated}"
                        android:fontFamily="@font/inter_regular"
                        android:onCheckedChanged="@{(switch, checked) -> viewmodel.printWhenSeated(checked)}"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/textView37"
                        app:switchPadding="6dp" />

                    <TextView
                        android:id="@+id/textView363"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="6dp"
                        android:layout_marginEnd="16dp"
                        android:fontFamily="@font/inter_regular"
                        android:text="@string/print_when_seated"
                        android:textColor="@color/colorDark100"
                        android:textSize="14sp"
                        app:layout_constraintBottom_toBottomOf="@+id/printWhenSeated"
                        app:layout_constraintStart_toEndOf="@+id/printWhenSeated"
                        app:layout_constraintTop_toTopOf="@+id/printWhenSeated"
                        bind:layout_constraintEnd_toEndOf="parent" />

                    <TextView
                        android:id="@+id/textView373"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="16dp"
                        android:fontFamily="@font/inter_regular"
                        android:text="@string/print_when_seated_info"
                        android:textColor="@color/colorGrey200"
                        android:textSize="12sp"
                        app:layout_constraintStart_toStartOf="@+id/textView363"
                        bind:layout_constraintEnd_toEndOf="parent"
                        bind:layout_constraintTop_toBottomOf="@+id/printWhenSeated" />

                </androidx.constraintlayout.widget.ConstraintLayout>

            </androidx.constraintlayout.widget.ConstraintLayout>

        </ScrollView>

        <com.eatapp.clementine.views.LoadingButton
            android:id="@+id/print"
            android:layout_width="0dp"
            android:layout_height="@dimen/button_height"
            android:layout_marginStart="16dp"
            android:layout_marginEnd="16dp"
            android:layout_marginBottom="16dp"
            android:background="@drawable/shape_rounded_btn_bcg_green"
            android:visibility="@{viewmodel.printMode ? View.VISIBLE : View.GONE}"
            app:layout_constraintBottom_toBottomOf="parent"
            app:title="@string/print"
            bind:layout_constraintEnd_toEndOf="parent"
            bind:layout_constraintStart_toStartOf="parent"
            app:tintColor="@{R.color.white}" />

    </androidx.constraintlayout.widget.ConstraintLayout>


</layout>