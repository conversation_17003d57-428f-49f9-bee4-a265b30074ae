<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:bind="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="viewmodel"
            type="com.eatapp.clementine.ui.common.tables.TablesViewModel" />

        <import type="android.view.View" />

    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/mainContainer"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/mainBcg"
        tools:context=".ui.common.tables.TablesActivity">

        <HorizontalScrollView
            android:id="@+id/roomScrollView"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_marginBottom="212dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.0"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/roomViewCont"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_gravity="center">

                <com.eatapp.clementine.views.RoomView
                    android:id="@+id/roomView"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintDimensionRatio="1:1"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintVertical_bias="0.0" />

            </androidx.constraintlayout.widget.ConstraintLayout>

        </HorizontalScrollView>

        <androidx.coordinatorlayout.widget.CoordinatorLayout
            app:layout_constraintBottom_toTopOf="@id/actionCont"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            android:layout_width="0dp"
            android:layout_height="0dp">

            <include
                android:id="@+id/bottom_sheet_selected_tables"
                layout="@layout/bottom_sheet_selected_tables"/>

        </androidx.coordinatorlayout.widget.CoordinatorLayout>

        <FrameLayout
            android:id="@+id/actionCont"
            android:layout_width="match_parent"
            android:layout_height="76dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent">

            <View
                android:id="@+id/view10"
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:background="@color/colorSeparator"
                app:layout_constraintBottom_toTopOf="@+id/bottomButtons" />

            <com.eatapp.clementine.views.LoadingButton
                android:id="@+id/applyBtn"
                android:layout_width="match_parent"
                android:layout_height="@dimen/button_height"
                android:layout_marginStart="16dp"
                android:layout_marginTop="16dp"
                android:layout_marginEnd="16dp"
                android:layout_marginBottom="16dp"
                android:background="@drawable/shape_rounded_btn_bcg_green"
                app:title="@string/save"
                android:textAllCaps="false"
                android:textColor="@color/white"
                app:tintColor="@{R.color.white}"
                android:textSize="15sp" />

        </FrameLayout>

        <androidx.recyclerview.widget.RecyclerView
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
            android:id="@+id/recycler_conflicts"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="160dp"
            android:layout_marginEnd="160dp"
            android:layout_marginTop="@dimen/margin_4"
            tools:listitem="@layout/list_item_conflict"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>
