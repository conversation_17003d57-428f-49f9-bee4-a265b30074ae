<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="viewmodel"
            type="com.eatapp.clementine.ui.reservation.ReservationViewModel" />

        <import type="android.view.View" />

    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        tools:context=".ui.guest.GuestActivity"
        android:background="@color/mainBcg">

        <View
            android:id="@+id/view16"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:background="@color/white"
            app:layout_constraintBottom_toTopOf="@+id/separator"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <com.google.android.material.tabs.TabLayout
            android:id="@+id/tabLayoutRF"
            android:layout_width="0dp"
            android:layout_height="@dimen/tab_height"
            android:background="@color/white"
            app:layout_constraintEnd_toEndOf="@+id/view16"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:tabIndicatorColor="@color/colorPrimary"
            app:tabMode="fixed"
            app:tabGravity="start"
            app:tabPaddingEnd="0dp"
            app:tabPaddingStart="0dp"
            app:tabRippleColor="@color/colorGreen05"
            app:tabSelectedTextColor="@color/colorPrimary"
            app:tabTextAppearance="@style/EatTab"
            app:tabTextColor="@color/colorDark50" />

        <View
            android:layout_width="wrap_content"
            android:layout_height="1dp"
            android:id="@+id/separator"
            app:layout_constraintTop_toBottomOf="@+id/tabLayoutRF"
            android:background="@color/colorSeparator"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintBottom_toTopOf="@+id/viewPagerRF" />

        <androidx.viewpager2.widget.ViewPager2
            android:id="@+id/viewPagerRF"
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.0"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/separator" />

        <ProgressBar
            android:id="@+id/progressBar"
            style="?android:attr/progressBarStyle"
            android:layout_width="@dimen/progress_size"
            android:layout_height="@dimen/progress_size"
            android:indeterminateTint="@color/colorPrimary"
            android:visibility="@{viewmodel.loading ? View.VISIBLE : View.GONE}"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.5"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@+id/viewPagerRF" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>
