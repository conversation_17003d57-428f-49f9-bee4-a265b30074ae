<?xml version="1.0" encoding="utf-8"?>
<navigation xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/home_nav_graph"
    app:startDestination="@+id/navigation_overview">

    <fragment
        android:id="@+id/navigation_overview"
        android:name="com.eatapp.clementine.ui.home.overview.OverviewFragment"
        android:label="overview_fragment"
        tools:layout="@layout/overview_fragment" />
    <fragment
        android:id="@+id/navigation_guests"
        android:name="com.eatapp.clementine.ui.home.more.guests.GuestsFragment"
        android:label="guests_fragment"
        tools:layout="@layout/guests_fragment" />
    <fragment
        android:id="@+id/navigation_reports"
        android:name="com.eatapp.clementine.ui.home.reports.ReportsFragment"
        android:label="reports_fragment"
        tools:layout="@layout/reports_fragment" />
    <fragment
        android:id="@+id/navigation_more"
        android:name="com.eatapp.clementine.ui.home.more.MoreFragment"
        android:label="more_fragment"
        tools:layout="@layout/more_fragment" />
    <fragment
        android:id="@+id/web_view_fragment"
        android:name="com.eatapp.clementine.ui.common.webview.WebViewFragment"
        android:label="WebViewFragment" />
    <fragment
        android:id="@+id/printer_fragment"
        android:name="com.eatapp.clementine.ui.common.printer.PrinterFragment"
        android:label="PrinterFragment" />
    <fragment
        android:id="@+id/omnisearch_fragment"
        android:name="com.eatapp.clementine.ui.omnisearch.OmniSearchFragment"
        android:label="OmniSearchFragment" />

</navigation>