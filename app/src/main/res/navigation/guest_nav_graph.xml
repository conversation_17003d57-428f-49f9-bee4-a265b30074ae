<?xml version="1.0" encoding="utf-8"?>
<navigation xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    app:startDestination="@+id/guestFragment">

    <fragment
        android:id="@+id/guestFragment"
        android:name="com.eatapp.clementine.ui.guest.GuestFragment"
        android:label="GuestFragment"
        tools:layout="@layout/guest_fragment">
        <argument
            android:name="simplifiedMode"
            android:defaultValue="false"
            app:argType="boolean" />
        <argument
            android:name="guest"
            app:argType="com.eatapp.clementine.data.network.response.guest.Guest"
            app:nullable="true" />
    </fragment>

</navigation>