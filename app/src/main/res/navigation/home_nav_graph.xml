<?xml version="1.0" encoding="utf-8"?>
<navigation xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/home_nav_graph"
    app:startDestination="@+id/navigation_overview">

    <fragment
        android:id="@+id/navigation_overview"
        android:name="com.eatapp.clementine.ui.home.overview.OverviewFragment"
        android:label="overview_fragment"
        tools:layout="@layout/overview_fragment" />

    <fragment
        android:id="@+id/navigation_floor"
        android:name="com.eatapp.clementine.ui.home.floor.FloorFragment"
        android:label="floor_fragment"
        tools:layout="@layout/floor_fragment" />

    <fragment
        android:id="@+id/navigation_reports"
        android:name="com.eatapp.clementine.ui.home.reports.PhoenixReportsFragment"
        android:label="reports_fragment"
        tools:layout="@layout/phoenix_reports_fragment"/>

    <fragment
        android:id="@+id/navigation_more"
        android:name="com.eatapp.clementine.ui.home.more.MoreFragment"
        android:label="more_fragment"
        tools:layout="@layout/more_fragment" />

</navigation>