// Top-level build file where you can add configuration options common to all sub-projects/modules.

buildscript {

    ext {
        agp_version = '8.9.1'
        kotlin_version = '2.1.0'
        room_version = '2.7.2'
        retrofit_version = '2.11.0'
        nav_version = "2.9.0"
        hilt_version = '2.53'
    }

    repositories {
        google()
        mavenCentral()
    }

    dependencies {
        classpath "com.android.tools.build:gradle:$agp_version"
        classpath 'com.google.gms:google-services:4.4.2'
        classpath 'com.google.firebase:firebase-crashlytics-gradle:3.0.4'
        classpath("androidx.navigation:navigation-safe-args-gradle-plugin:$nav_version")
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
        classpath "com.google.dagger:hilt-android-gradle-plugin:$hilt_version"
    }
}

allprojects {

    repositories {
        google()
        mavenCentral()
        maven { url "https://jitpack.io" }
    }
}


tasks.register('clean', Delete) {
    delete rootProject.buildDir
}
